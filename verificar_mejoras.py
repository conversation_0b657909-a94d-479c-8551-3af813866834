#!/usr/bin/env python3
"""
✅ VERIFICACIÓN DE MEJORAS IMPLEMENTADAS
Script para verificar que las mejoras están correctamente implementadas
"""

def verificar_espaciado():
    """Verificar corrección del espaciado izquierdo"""
    print("🔧 VERIFICANDO CORRECCIÓN DE ESPACIADO")
    print("=" * 40)
    
    try:
        with open('xui_manager_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Buscar la línea específica del espaciado
        if 'minsize=120' in content:
            print("✅ Espaciado corregido: minsize=120 encontrado")
            
            # Verificar que no esté el valor anterior
            if 'minsize=160' not in content:
                print("✅ Valor anterior (160) removido correctamente")
                return True
            else:
                print("⚠️ Valor anterior (160) aún presente")
                return False
        else:
            print("❌ Corrección de espaciado no encontrada")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando espaciado: {e}")
        return False

def verificar_panel_tmdb():
    """Verificar implementación del panel TMDB mejorado"""
    print("\n🎬 VERIFICANDO PANEL TMDB MEJORADO")
    print("=" * 40)
    
    try:
        with open('xui_manager_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar funciones clave implementadas
        funciones_requeridas = [
            'open_movie_assignment_dialog',
            'create_movie_result_cards'
        ]
        
        funciones_encontradas = 0
        for funcion in funciones_requeridas:
            if f'def {funcion}(' in content:
                print(f"✅ Función {funcion} implementada")
                funciones_encontradas += 1
            else:
                print(f"❌ Función {funcion} no encontrada")
        
        # Verificar características específicas
        caracteristicas = [
            ('Ventana más grande', '1200x800'),
            ('Cards visuales', 'card_frame'),
            ('Radio buttons', 'Radiobutton'),
            ('Información completa', 'TMDB ID:'),
            ('Layout en grid', '.grid(')
        ]
        
        caracteristicas_encontradas = 0
        for nombre, codigo in caracteristicas:
            if codigo in content:
                print(f"✅ {nombre}: {codigo} encontrado")
                caracteristicas_encontradas += 1
            else:
                print(f"❌ {nombre}: {codigo} no encontrado")
        
        # Verificar que no hay dependencias externas problemáticas
        if 'from PIL import' not in content or content.count('from PIL import') <= 1:
            print("✅ Sin dependencias PIL problemáticas")
        else:
            print("⚠️ Múltiples referencias a PIL encontradas")
        
        if 'import requests' not in content or content.count('import requests') <= 1:
            print("✅ Sin dependencias requests problemáticas")
        else:
            print("⚠️ Múltiples referencias a requests encontradas")
        
        # Calcular éxito
        total_items = len(funciones_requeridas) + len(caracteristicas)
        items_encontrados = funciones_encontradas + caracteristicas_encontradas
        
        if items_encontrados >= total_items * 0.8:  # 80% o más
            print(f"✅ Panel TMDB implementado correctamente ({items_encontrados}/{total_items})")
            return True
        else:
            print(f"❌ Panel TMDB incompleto ({items_encontrados}/{total_items})")
            return False
            
    except Exception as e:
        print(f"❌ Error verificando panel TMDB: {e}")
        return False

def verificar_sintaxis():
    """Verificar que no hay errores de sintaxis"""
    print("\n🔍 VERIFICANDO SINTAXIS")
    print("=" * 40)
    
    try:
        import ast
        
        with open('xui_manager_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Intentar parsear el archivo
        ast.parse(content)
        print("✅ Sintaxis correcta - archivo válido")
        return True
        
    except SyntaxError as e:
        print(f"❌ Error de sintaxis: {e}")
        print(f"   Línea {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Error verificando sintaxis: {e}")
        return False

def main():
    """Función principal"""
    print("✅ VERIFICACIÓN DE MEJORAS IMPLEMENTADAS")
    print("=" * 50)
    
    # Ejecutar verificaciones
    espaciado_ok = verificar_espaciado()
    panel_ok = verificar_panel_tmdb()
    sintaxis_ok = verificar_sintaxis()
    
    # Resumen final
    print("\n📊 RESUMEN DE VERIFICACIÓN")
    print("=" * 50)
    print(f"🔧 Corrección espaciado: {'✅ CORRECTO' if espaciado_ok else '❌ FALLO'}")
    print(f"🎬 Panel TMDB mejorado: {'✅ CORRECTO' if panel_ok else '❌ FALLO'}")
    print(f"🔍 Sintaxis válida: {'✅ CORRECTO' if sintaxis_ok else '❌ FALLO'}")
    
    if espaciado_ok and panel_ok and sintaxis_ok:
        print("\n🎉 TODAS LAS MEJORAS IMPLEMENTADAS CORRECTAMENTE")
        print("\n📋 RESUMEN DE CAMBIOS:")
        print("   • Espaciado izquierdo reducido de 160px a 120px")
        print("   • Panel TMDB con cards visuales implementado")
        print("   • Información completa de películas (título, año, rating, ID)")
        print("   • Selección por radio buttons")
        print("   • Layout responsive en 2 columnas")
        print("   • Sin dependencias externas adicionales")
        print("\n🚀 El sistema está listo para usar!")
        return 0
    else:
        print("\n⚠️ ALGUNAS MEJORAS NECESITAN REVISIÓN")
        return 1

if __name__ == "__main__":
    exit(main())
