#!/usr/bin/env python3
"""
🎬 XUI MANAGER FINAL
Versión final con importador robusto y GUI thread-safe
Sin long threads, manejo de errores mejorado
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import json
import time
from datetime import datetime
from robust_m3u_importer import RobustM3UImporter

class XUIManagerFinal:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_colors()
        self.create_widgets()
        self.importer = RobustM3UImporter()
        self.import_thread = None
        
    def setup_window(self):
        """Configuración de la ventana"""
        self.root.title("XUI Manager Pro - Final")
        self.root.geometry("1200x800")
        
        # Centrar ventana
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - 600
        y = (self.root.winfo_screenheight() // 2) - 400
        self.root.geometry(f"1200x800+{x}+{y}")
        
        self.root.minsize(1000, 600)
        self.root.configure(bg='#0d1117')
        
        # Manejar cierre de ventana
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_colors(self):
        """Configurar colores estilo NVIDIA + Gemini"""
        self.colors = {
            'bg_primary': '#0d1117',        # Negro GitHub
            'bg_secondary': '#161b22',      # Gris muy oscuro
            'bg_tertiary': '#21262d',       # Gris oscuro
            'bg_card': '#1c2128',           # Cards oscuros
            'accent': '#00d4ff',            # Celeste Gemini
            'accent_hover': '#00b8e6',      # Celeste más oscuro
            'nvidia_green': '#76b900',      # Verde NVIDIA
            'nvidia_green_hover': '#5a8a00', # Verde NVIDIA hover
            'text_primary': '#f0f6fc',      # Blanco suave
            'text_secondary': '#8b949e',    # Gris claro
            'success': '#238636',           # Verde éxito
            'warning': '#d29922',           # Amarillo
            'error': '#f85149',             # Rojo
            'border': '#76b900',            # Bordes verdes NVIDIA
            'border_focus': '#00d4ff'       # Bordes activos celestes
        }
    
    def create_widgets(self):
        """Crear widgets"""
        # Frame principal
        self.main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        self.main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Header
        self.create_header(self.main_frame)

        # Content
        self.content_frame = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        self.content_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Panels
        self.create_series_panel(self.content_frame)
        self.create_movies_panel(self.content_frame)
        
        # Estado inicial
        self.log_message("🎬 XUI Manager Pro iniciado")
        self.log_message("📁 Selecciona un archivo M3U para comenzar")
    
    def create_header(self, parent):
        """Crear header estilo NVIDIA + Gemini"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_secondary'],
                               relief='solid', bd=2, highlightbackground=self.colors['border'])
        header_frame.pack(fill='x', pady=(0, 20))

        # Contenedor principal con padding
        main_container = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        main_container.pack(fill='x', padx=20, pady=15)

        # Lado izquierdo - Título
        left_side = tk.Frame(main_container, bg=self.colors['bg_secondary'])
        left_side.pack(side='left', fill='x', expand=True)

        title_frame = tk.Frame(left_side, bg=self.colors['bg_secondary'])
        title_frame.pack(anchor='w')

        icon_label = tk.Label(title_frame, text="🎬", font=('Segoe UI', 24),
                            bg=self.colors['bg_secondary'], fg=self.colors['accent'])
        icon_label.pack(side='left', padx=(0, 12))

        title_label = tk.Label(title_frame, text="XUI Manager Pro",
                             font=('Segoe UI', 28, 'bold'),
                             bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        title_label.pack(side='left')

        subtitle_label = tk.Label(left_side,
                                text="Gestión profesional de contenido para XUI",
                                font=('Segoe UI', 14),
                                bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        subtitle_label.pack(anchor='w', pady=(8, 0))

        # Lado derecho - Estado con bordes verdes NVIDIA
        right_side = tk.Frame(main_container, bg=self.colors['bg_secondary'])
        right_side.pack(side='right')

        self.status_frame = tk.Frame(right_side, bg=self.colors['bg_card'],
                                   relief='solid', bd=2, highlightbackground=self.colors['nvidia_green'])
        self.status_frame.pack(pady=(5, 0), padx=10, ipadx=12, ipady=8)

        self.status_indicator = tk.Label(self.status_frame, text="●",
                                       font=('Segoe UI', 14),
                                       bg=self.colors['bg_card'],
                                       fg=self.colors['nvidia_green'])
        self.status_indicator.pack(side='left')

        self.status_label = tk.Label(self.status_frame, text="Listo",
                                   font=('Segoe UI', 12, 'bold'),
                                   bg=self.colors['bg_card'],
                                   fg=self.colors['text_primary'])
        self.status_label.pack(side='left', padx=(8, 0))
        
        # Separador verde NVIDIA
        separator = tk.Frame(header_frame, height=3, bg=self.colors['nvidia_green'])
        separator.pack(fill='x', pady=(15, 0))
    
    def create_series_panel(self, parent):
        """Crear panel de series estilo Gemini"""
        series_container = tk.Frame(parent, bg=self.colors['bg_primary'])
        series_container.pack(side='left', fill='both', expand=True, padx=(0, 15))

        # Card de series con bordes verdes NVIDIA
        series_card = tk.Frame(series_container, bg=self.colors['bg_card'],
                             relief='solid', bd=2, highlightbackground=self.colors['border'])
        series_card.pack(fill='both', expand=True)

        # Header
        self.create_card_header(series_card, "📺", "SERIES",
                              "Importar y gestionar series desde archivos M3U")

        # Contenido con mejor padding
        content = tk.Frame(series_card, bg=self.colors['bg_card'])
        content.pack(fill='both', expand=True, padx=24, pady=(0, 24))

        # Secciones
        self.create_import_section(content)
        self.create_log_section(content)
    
    def create_card_header(self, parent, icon, title, description):
        """Crear header de card estilo Gemini"""
        header = tk.Frame(parent, bg=self.colors['bg_card'])
        header.pack(fill='x', padx=24, pady=(24, 16))

        # Icono con estilo más sutil
        icon_container = tk.Frame(header, bg=self.colors['bg_card'])
        icon_container.pack(side='left')

        icon_bg = tk.Frame(icon_container, bg=self.colors['bg_tertiary'], width=48, height=48,
                          relief='solid', bd=2, highlightbackground=self.colors['nvidia_green'])
        icon_bg.pack_propagate(False)
        icon_bg.pack()

        icon_label = tk.Label(icon_bg, text=icon, font=('Segoe UI', 18),
                            bg=self.colors['bg_tertiary'], fg=self.colors['accent'])
        icon_label.pack(expand=True)

        # Texto con mejor tipografía
        text_container = tk.Frame(header, bg=self.colors['bg_card'])
        text_container.pack(side='left', fill='x', expand=True, padx=(16, 0))
        
        title_label = tk.Label(text_container, text=title,
                             font=('Segoe UI', 16, 'normal'),
                             bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack(anchor='w')

        desc_label = tk.Label(text_container, text=description,
                            font=('Segoe UI', 12),
                            bg=self.colors['bg_card'], fg=self.colors['text_secondary'])
        desc_label.pack(anchor='w', pady=(4, 0))
    
    def create_import_section(self, parent):
        """Crear sección de importación"""
        import_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        import_frame.pack(fill='x', pady=(0, 20))
        
        # Título
        title_label = tk.Label(import_frame, text="Importar M3U", 
                             font=('Segoe UI', 12, 'bold'),
                             bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack(anchor='w', pady=(0, 10))
        
        # Selección de archivo
        file_frame = tk.Frame(import_frame, bg=self.colors['bg_card'])
        file_frame.pack(fill='x', pady=(0, 10))
        
        self.file_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.file_var, 
                            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                            font=('Segoe UI', 10), bd=0, highlightthickness=1,
                            highlightcolor=self.colors['accent'])
        file_entry.pack(side='left', fill='x', expand=True, ipady=8)
        
        browse_btn = tk.Button(file_frame, text="📁 Examinar", 
                             command=self.browse_file,
                             bg=self.colors['accent'], fg='white',
                             font=('Segoe UI', 10, 'bold'), bd=0,
                             activebackground=self.colors['accent_hover'])
        browse_btn.pack(side='right', padx=(10, 0), ipady=8, ipadx=15)
        
        # Botones de acción
        action_frame = tk.Frame(import_frame, bg=self.colors['bg_card'])
        action_frame.pack(fill='x')
        
        self.preview_btn = tk.Button(action_frame, text="ℹ️ Vista Previa",
                                   command=self.preview_import,
                                   bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                   font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                   activebackground=self.colors['bg_tertiary'],
                                   highlightbackground=self.colors['nvidia_green'])
        self.preview_btn.pack(side='left', padx=(0, 12), ipady=10, ipadx=20)

        self.import_btn = tk.Button(action_frame, text="📥 Importar Series",
                                  command=self.start_import,
                                  bg=self.colors['accent'], fg=self.colors['bg_primary'],
                                  font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                  activebackground=self.colors['accent_hover'],
                                  highlightbackground=self.colors['accent'])
        self.import_btn.pack(side='left', ipady=10, ipadx=20, padx=(0, 12))

        # Botón de asignación de categorías
        self.category_btn = tk.Button(action_frame, text="🏷️ Asignar Categorías",
                                    command=self.open_category_assignment,
                                    bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                    font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                    activebackground=self.colors['nvidia_green_hover'],
                                    highlightbackground=self.colors['nvidia_green'])
        self.category_btn.pack(side='left', ipady=10, ipadx=20, padx=(0, 12))

        # Botón de asignación de bouquets
        self.bouquet_btn = tk.Button(action_frame, text="📋 Asignar Bouquet",
                                   command=self.open_bouquet_assignment,
                                   bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                   font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                   activebackground=self.colors['bg_tertiary'],
                                   highlightbackground=self.colors['nvidia_green'])
        self.bouquet_btn.pack(side='left', ipady=10, ipadx=20)

        # Botón de herramientas administrativas estilo NVIDIA
        self.admin_btn = tk.Button(action_frame, text="🛠️ Herramientas Admin",
                                 command=self.open_admin_tools,
                                 bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                 font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                 activebackground=self.colors['nvidia_green_hover'],
                                 highlightbackground=self.colors['nvidia_green'])
        self.admin_btn.pack(side='right', ipady=10, ipadx=20, padx=(0, 10))

        # Botón de comparador de contenido (estilo Gemini)
        self.comparator_btn = tk.Button(action_frame, text="🔄 Comparar Contenido",
                                      command=self.open_content_comparator,
                                      bg='#1a1a1a', fg='#00bcd4',
                                      font=('Segoe UI', 10, 'bold'), bd=2,
                                      relief='solid',
                                      highlightbackground='#00bcd4',
                                      highlightcolor='#4fc3f7',
                                      activebackground='#2d2d2d',
                                      activeforeground='#4fc3f7')
        self.comparator_btn.pack(side='right', ipady=8, ipadx=15)
        
        # Barra de progreso
        self.progress_frame = tk.Frame(import_frame, bg=self.colors['bg_card'])
        self.progress_canvas = tk.Canvas(self.progress_frame, height=8, 
                                       bg=self.colors['bg_secondary'], 
                                       highlightthickness=0)
        self.progress_canvas.pack(fill='x', pady=(15, 5))
        
        self.progress_bar = self.progress_canvas.create_rectangle(0, 0, 0, 8, 
                                                                fill=self.colors['accent'], 
                                                                outline="")
        
        self.progress_label = tk.Label(self.progress_frame, text="", 
                                     font=('Segoe UI', 9),
                                     bg=self.colors['bg_card'], fg=self.colors['text_secondary'])
        self.progress_label.pack()
        
        self.progress_frame.pack_forget()  # Ocultar inicialmente
    
    def create_log_section(self, parent):
        """Crear sección de log"""
        log_frame = tk.Frame(parent, bg=self.colors['bg_card'])
        log_frame.pack(fill='both', expand=True)
        
        # Título con controles
        header_frame = tk.Frame(log_frame, bg=self.colors['bg_card'])
        header_frame.pack(fill='x', pady=(0, 10))
        
        title_label = tk.Label(header_frame, text="📋 Log de Actividad", 
                             font=('Segoe UI', 12, 'bold'),
                             bg=self.colors['bg_card'], fg=self.colors['text_primary'])
        title_label.pack(side='left')
        
        clear_btn = tk.Button(header_frame, text="Limpiar", 
                            command=self.clear_log,
                            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                            font=('Segoe UI', 9, 'bold'), bd=0,
                            activebackground=self.colors['border'])
        clear_btn.pack(side='right', ipady=4, ipadx=10)
        
        # Text widget
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, 
                                                bg=self.colors['bg_secondary'],
                                                fg=self.colors['text_primary'],
                                                font=('Consolas', 9),
                                                insertbackground=self.colors['text_primary'],
                                                selectbackground=self.colors['accent'],
                                                bd=0, highlightthickness=1,
                                                highlightcolor=self.colors['border'],
                                                wrap=tk.WORD)
        self.log_text.pack(fill='both', expand=True)
    
    def create_movies_panel(self, parent):
        """Crear panel de películas estilo NVIDIA + Gemini"""
        movies_container = tk.Frame(parent, bg=self.colors['bg_primary'])
        movies_container.pack(side='right', fill='both', expand=True, padx=(15, 0))

        # Card de películas con bordes verdes NVIDIA
        movies_card = tk.Frame(movies_container, bg=self.colors['bg_card'],
                             relief='solid', bd=2, highlightbackground=self.colors['border'])
        movies_card.pack(fill='both', expand=True)

        # Header estilo NVIDIA + Gemini
        self.create_card_header(movies_card, "🎬", "PELÍCULAS",
                              "Importar y gestionar películas desde archivos M3U")

        # Contenido principal
        content = tk.Frame(movies_card, bg=self.colors['bg_card'])
        content.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Frame de importación con bordes verdes
        import_frame = tk.Frame(content, bg=self.colors['bg_secondary'],
                              relief='solid', bd=2, highlightbackground=self.colors['nvidia_green'])
        import_frame.pack(fill='x', pady=(0, 15))

        # Título de importación estilo NVIDIA
        title_container = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        title_container.pack(fill='x', pady=(15, 10))

        import_title = tk.Label(title_container, text="📁 Importar Películas",
                               font=('Segoe UI', 14, 'bold'),
                               bg=self.colors['bg_secondary'], fg=self.colors['nvidia_green'])
        import_title.pack()

        # Línea decorativa verde
        title_line = tk.Frame(title_container, height=2, bg=self.colors['nvidia_green'])
        title_line.pack(fill='x', pady=(5, 0))

        # Selector de archivo
        file_frame = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        file_frame.pack(fill='x', padx=15, pady=(0, 10))

        tk.Label(file_frame, text="Archivo M3U:",
                font=('Segoe UI', 10, 'bold'),
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w')

        file_input_frame = tk.Frame(file_frame, bg=self.colors['bg_secondary'])
        file_input_frame.pack(fill='x', pady=(5, 0))

        self.movie_file_var = tk.StringVar()
        self.movie_file_entry = tk.Entry(file_input_frame, textvariable=self.movie_file_var,
                                        font=('Segoe UI', 10), state='readonly',
                                        bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
        self.movie_file_entry.pack(side='left', fill='x', expand=True, ipady=5)

        self.movie_browse_btn = tk.Button(file_input_frame, text="📁 Examinar",
                                         command=self.browse_movie_file,
                                         bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                         font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                                         activebackground=self.colors['nvidia_green_hover'],
                                         highlightbackground=self.colors['nvidia_green'])
        self.movie_browse_btn.pack(side='right', padx=(10, 0), ipady=6, ipadx=12)

        # Nota informativa sobre servidor (sin input)
        info_frame = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        info_frame.pack(fill='x', padx=15, pady=(0, 15))

        info_label = tk.Label(info_frame, text="📡 Servidor de destino: 82 (Películas)",
                             font=('Segoe UI', 10),
                             bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        info_label.pack(anchor='w')

        # Variable del servidor (fijo en 82)
        self.movie_server_var = tk.StringVar(value="82")

        # Botones de acción principales
        action_frame = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        action_frame.pack(fill='x', padx=15, pady=(0, 10))

        self.movie_preview_btn = tk.Button(action_frame, text="ℹ️ Vista Previa",
                                          command=self.preview_movie_import,
                                          bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                          font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                          activebackground=self.colors['bg_tertiary'],
                                          highlightbackground=self.colors['nvidia_green'])
        self.movie_preview_btn.pack(side='left', padx=(0, 10), ipady=10, ipadx=20)

        self.movie_import_btn = tk.Button(action_frame, text="🎬 Importar Películas",
                                         command=self.start_movie_import,
                                         bg=self.colors['accent'], fg=self.colors['bg_primary'],
                                         font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                                         activebackground=self.colors['accent_hover'])
        self.movie_import_btn.pack(side='left', ipady=8, ipadx=15)

        # Botones de configuración adicionales
        config_frame = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        config_frame.pack(fill='x', padx=15, pady=(0, 15))

        # Botón de asignación de categorías para películas
        self.movie_category_btn = tk.Button(config_frame, text="🏷️ Categorías",
                                          command=self.open_movie_category_assignment,
                                          bg=self.colors['warning'], fg=self.colors['bg_primary'],
                                          font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                                          activebackground='#b8730a',
                                          highlightbackground=self.colors['warning'])
        self.movie_category_btn.pack(side='left', padx=(0, 10), ipady=8, ipadx=15)

        # Botón de asignación de bouquets para películas
        self.movie_bouquet_btn = tk.Button(config_frame, text="📋 Bouquets",
                                         command=self.open_movie_bouquet_assignment,
                                         bg='#9b59b6', fg=self.colors['text_primary'],
                                         font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                                         activebackground='#8e44ad',
                                         highlightbackground='#9b59b6')
        self.movie_bouquet_btn.pack(side='left', ipady=8, ipadx=15)

        # Barra de progreso para películas
        self.movie_progress_frame = tk.Frame(import_frame, bg=self.colors['bg_secondary'])
        self.movie_progress_canvas = tk.Canvas(self.movie_progress_frame, height=8,
                                             bg=self.colors['bg_tertiary'],
                                             highlightthickness=0)
        self.movie_progress_canvas.pack(fill='x', pady=(15, 5))

        self.movie_progress_bar = self.movie_progress_canvas.create_rectangle(0, 0, 0, 8,
                                                                            fill=self.colors['accent'],
                                                                            outline="")

        self.movie_progress_label = tk.Label(self.movie_progress_frame, text="",
                                           font=('Segoe UI', 9),
                                           bg=self.colors['bg_secondary'], fg=self.colors['text_secondary'])
        self.movie_progress_label.pack()

        self.movie_progress_frame.pack_forget()  # Ocultar inicialmente

        # Log de películas
        log_frame = tk.Frame(content, bg=self.colors['bg_secondary'], relief='solid', bd=1)
        log_frame.pack(fill='both', expand=True)

        log_title = tk.Label(log_frame, text="📋 Log de Importación",
                            font=('Segoe UI', 12, 'bold'),
                            bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
        log_title.pack(pady=(15, 10))

        log_content = tk.Frame(log_frame, bg=self.colors['bg_secondary'])
        log_content.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        self.movie_log_text = scrolledtext.ScrolledText(log_content, height=12,
                                                       bg=self.colors['bg_tertiary'],
                                                       fg=self.colors['text_primary'],
                                                       font=('Consolas', 9),
                                                       wrap=tk.WORD)
        self.movie_log_text.pack(fill='both', expand=True)
    
    def browse_file(self):
        """Seleccionar archivo M3U"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo M3U",
            filetypes=[("Archivos M3U", "*.m3u"), ("Archivos M3U8", "*.m3u8"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.file_var.set(file_path)
            self.log_message(f"📁 Archivo seleccionado: {os.path.basename(file_path)}")
            self.update_status("Archivo cargado", "success")
    
    def preview_import(self):
        """Vista previa de importación"""
        file_path = self.file_var.get()
        if not file_path:
            messagebox.showwarning("Advertencia", "Por favor selecciona un archivo M3U")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "El archivo seleccionado no existe")
            return
        
        self.log_message("🔍 Analizando archivo M3U...")
        self.update_status("Analizando...", "info")
        self.disable_buttons()
        
        # Ejecutar análisis directamente (sin thread)
        self.root.after(100, lambda: self._preview_worker(file_path))
    
    def start_import(self):
        """Iniciar importación"""
        file_path = self.file_var.get()
        if not file_path:
            messagebox.showwarning("Advertencia", "Por favor selecciona un archivo M3U")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "El archivo seleccionado no existe")
            return
        
        # Confirmar importación
        result = messagebox.askyesno("Confirmar Importación", 
                                   "¿Estás seguro de que deseas importar este archivo?\n\n"
                                   "Esta acción modificará la base de datos.")
        if not result:
            return
        
        self.log_message("🚀 Iniciando importación...")
        self.update_status("Importando...", "info")
        self.show_progress()
        self.disable_buttons()
        
        # Ejecutar importación directamente (sin thread)
        self.root.after(100, lambda: self._import_worker(file_path))
    
    def _preview_worker(self, file_path):
        """Worker para vista previa (thread-safe)"""
        try:
            # Análisis básico del archivo
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                lines = file.readlines()
            
            episode_count = 0
            series_names = set()
            
            for i, line in enumerate(lines):
                if line.strip().startswith('#EXTINF:'):
                    episode_count += 1
                    # Extraer nombre de serie
                    if 'tvg-name=' in line:
                        import re
                        match = re.search(r'tvg-name="([^"]*)"', line)
                        if match:
                            name = match.group(1)
                            series_match = re.search(r'^(.+?)\s+S\d+E\d+', name)
                            if series_match:
                                series_names.add(series_match.group(1).strip())
            
            # Actualizar UI en hilo principal
            self.root.after(0, lambda: self._preview_complete(len(series_names), episode_count))
            
        except Exception as e:
            self.root.after(0, lambda: self._preview_error(str(e)))
    
    def _preview_complete(self, series_count, episode_count):
        """Completar vista previa"""
        self.log_message("📊 Análisis completado")
        self.log_message(f"📺 Series detectadas: {series_count}")
        self.log_message(f"🎬 Episodios encontrados: {episode_count}")
        
        if episode_count > 0:
            self.log_message("✅ Archivo listo para importar")
            self.update_status("Listo para importar", "success")
        else:
            self.log_message("⚠️ No se encontraron episodios válidos")
            self.update_status("Sin episodios válidos", "warning")
        
        self.enable_buttons()
    
    def _preview_error(self, error_msg):
        """Error en vista previa"""
        self.log_message(f"❌ Error en análisis: {error_msg}")
        self.update_status("Error en análisis", "error")
        self.enable_buttons()
    
    def _import_worker(self, file_path):
        """Worker para importación (thread-safe)"""
        try:
            # Conectar al importador
            if not self.importer.connect():
                self.root.after(0, lambda: self._import_error("No se pudo conectar a la base de datos"))
                return

            # Establecer categoría por defecto si está configurada
            if hasattr(self, 'default_category_id') and self.default_category_id:
                self.importer.default_category_id = self.default_category_id
                self.root.after(0, lambda: self.log_message(f"🏷️ Usando categoría por defecto: ID {self.default_category_id}"))

            # Establecer bouquet por defecto si está configurado
            if hasattr(self, 'default_bouquet_id') and self.default_bouquet_id:
                self.importer.default_bouquet_id = self.default_bouquet_id
                self.root.after(0, lambda: self.log_message(f"📋 Usando bouquet por defecto: ID {self.default_bouquet_id}"))

            # Callback para progreso
            def progress_callback(value):
                self.root.after(0, lambda: self.update_progress(value, f"Progreso: {value}%"))

            # Aplicar correcciones según patrón real
            def format_url_real(url):
                if not url: return '[""]'
                escaped_url = url.replace('/', '\\/')
                return f'["{escaped_url}"]'
            self.importer.format_url = format_url_real
            # Ejecutar importación
            success = self.importer.import_m3u_safe(file_path, progress_callback)

            # Desconectar
            self.importer.disconnect()

            # Actualizar UI
            if success:
                self.root.after(0, self._import_complete)
            else:
                self.root.after(0, lambda: self._import_error("Error durante la importación"))

        except Exception as e:
            self.root.after(0, lambda: self._import_error(str(e)))
    
    def _import_complete(self):
        """Completar importación"""
        self.log_message("✅ Importación completada exitosamente")
        self.log_message("📊 Revisa el log del importador para detalles")
        self.log_message("🖥️ Series asignadas al servidor 212")
        self.update_status("Importación completada", "success")
        self.hide_progress()
        self.enable_buttons()
    
    def _import_error(self, error_msg):
        """Error en importación"""
        self.log_message(f"❌ Error en importación: {error_msg}")
        self.update_status("Error en importación", "error")
        self.hide_progress()
        self.enable_buttons()
    
    def update_status(self, message, status_type="info"):
        """Actualizar estado (thread-safe)"""
        color_map = {
            "success": self.colors['success'],
            "error": self.colors['error'],
            "warning": self.colors['warning'],
            "info": self.colors['accent']
        }
        
        self.status_indicator.config(fg=color_map.get(status_type, self.colors['accent']))
        self.status_label.config(text=message)
    
    def show_progress(self):
        """Mostrar barra de progreso"""
        self.progress_frame.pack(fill='x', pady=(15, 0))
        self.update_progress(0, "Iniciando...")
    
    def update_progress(self, value, text=""):
        """Actualizar progreso"""
        try:
            width = self.progress_canvas.winfo_width()
            if width > 1:
                progress_width = (width * value) / 100
                self.progress_canvas.coords(self.progress_bar, 0, 0, progress_width, 8)
            
            if text:
                self.progress_label.config(text=text)
        except:
            pass
    
    def hide_progress(self):
        """Ocultar barra de progreso"""
        self.root.after(2000, lambda: self.progress_frame.pack_forget())

    def show_movie_progress(self):
        """Mostrar barra de progreso de películas"""
        self.movie_progress_frame.pack(fill='x', pady=(15, 0))
        self.update_movie_progress(0, "Iniciando importación de películas...")

    def update_movie_progress(self, value, text=""):
        """Actualizar progreso de películas"""
        try:
            width = self.movie_progress_canvas.winfo_width()
            if width > 1:
                progress_width = (width * value) / 100
                self.movie_progress_canvas.coords(self.movie_progress_bar, 0, 0, progress_width, 8)

            if text:
                self.movie_progress_label.config(text=text)
        except:
            pass

    def hide_movie_progress(self):
        """Ocultar barra de progreso de películas"""
        self.root.after(2000, lambda: self.movie_progress_frame.pack_forget())

    def disable_buttons(self):
        """Deshabilitar botones durante operaciones"""
        self.preview_btn.config(state='disabled')
        self.import_btn.config(state='disabled')
    
    def enable_buttons(self):
        """Habilitar botones"""
        self.preview_btn.config(state='normal')
        self.import_btn.config(state='normal')
    
    def clear_log(self):
        """Limpiar log"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("ℹ️ Log limpiado")
    
    def log_message(self, message):
        """Agregar mensaje al log (thread-safe)"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
    
    def on_closing(self):
        """Manejar cierre de ventana"""
        if self.import_thread and self.import_thread.is_alive():
            if messagebox.askokcancel("Cerrar", "Hay una importación en progreso. ¿Deseas cerrar de todas formas?"):
                self.root.destroy()
        else:
            self.root.destroy()
    
    def browse_movie_file(self):
        """Examinar archivo M3U para películas"""
        filename = filedialog.askopenfilename(
            title="Seleccionar archivo M3U de películas",
            filetypes=[("Archivos M3U", "*.m3u"), ("Todos los archivos", "*.*")]
        )
        if filename:
            self.movie_file_var.set(filename)
            self.movie_log_message(f"📁 Archivo seleccionado: {filename}")

    def movie_log_message(self, message):
        """Agregar mensaje al log de películas"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.movie_log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.movie_log_text.see(tk.END)
        self.movie_log_text.update()

    def preview_movie_import(self):
        """Vista previa de importación de películas"""
        if not self.movie_file_var.get():
            messagebox.showwarning("Advertencia", "Selecciona un archivo M3U primero")
            return

        self.movie_log_message("🔍 Iniciando vista previa de películas...")

        def preview_worker():
            try:
                from robust_movie_importer import RobustMovieImporter

                importer = RobustMovieImporter()

                # Configurar parámetros
                config = {
                    'file_path': self.movie_file_var.get(),
                    'server_id': int(self.movie_server_var.get()),
                    'preview_only': True
                }

                # Ejecutar vista previa
                result = importer.preview_import(config)

                if result['success']:
                    self.movie_log_message(f"✅ Vista previa completada")
                    self.movie_log_message(f"📊 Películas encontradas: {result['total_movies']}")
                    self.movie_log_message(f"🎬 Películas válidas: {result['valid_movies']}")
                    self.movie_log_message(f"⚠️ Películas con problemas: {result['invalid_movies']}")
                else:
                    self.movie_log_message(f"❌ Error en vista previa: {result['error']}")

            except ImportError:
                self.movie_log_message("❌ Error: No se encontró robust_movie_importer.py")
            except Exception as e:
                self.movie_log_message(f"❌ Error durante vista previa: {str(e)}")

        # Ejecutar directamente sin thread
        self.root.after(100, preview_worker)

    def start_movie_import(self):
        """Iniciar importación de películas"""
        if not self.movie_file_var.get():
            messagebox.showwarning("Advertencia", "Selecciona un archivo M3U primero")
            return

        # Confirmar importación
        result = messagebox.askyesno("Confirmar Importación",
                                   "¿Iniciar importación de películas?\n\n"
                                   "Esto procesará el archivo M3U y agregará las películas a la base de datos.")

        if not result:
            return

        self.movie_log_message("🚀 Iniciando importación de películas...")

        # Mostrar barra de progreso
        self.show_movie_progress()

        # Deshabilitar botones durante importación
        self.movie_preview_btn.config(state='disabled')
        self.movie_import_btn.config(state='disabled')

        def import_worker():
            try:
                from robust_movie_importer import RobustMovieImporter

                importer = RobustMovieImporter()

                # Conectar al importer
                if not importer.connect():
                    self.root.after(0, lambda: self.movie_log_message("❌ No se pudo conectar a la base de datos"))
                    return

                # Establecer categoría por defecto si está configurada
                if hasattr(self, 'default_movie_category_id') and self.default_movie_category_id:
                    importer.set_default_category(self.default_movie_category_id)
                    self.root.after(0, lambda: self.movie_log_message(f"🏷️ Usando categoría por defecto: ID {self.default_movie_category_id}"))

                # Establecer bouquet por defecto si está configurado
                if hasattr(self, 'default_movie_bouquet_id') and self.default_movie_bouquet_id:
                    importer.set_default_bouquet(self.default_movie_bouquet_id)
                    self.root.after(0, lambda: self.movie_log_message(f"📋 Usando bouquet por defecto: ID {self.default_movie_bouquet_id}"))

                # Callback para progreso
                def progress_callback(value):
                    self.root.after(0, lambda: self.update_movie_progress(value, f"Progreso: {value}%"))

                # Configurar parámetros
                config = {
                    'file_path': self.movie_file_var.get(),
                    'server_id': int(self.movie_server_var.get()),
                    'preview_only': False
                }

                # Ejecutar importación con callback de progreso
                result = importer.import_movies(config, progress_callback)

                if result['success']:
                    self.movie_log_message(f"🎉 Importación completada exitosamente")
                    self.movie_log_message(f"📊 Películas procesadas: {result['total_processed']}")
                    self.movie_log_message(f"✅ Películas importadas: {result['imported']}")
                    self.movie_log_message(f"⚠️ Películas omitidas: {result['skipped']}")
                    self.movie_log_message(f"❌ Errores: {result['errors']}")
                else:
                    self.movie_log_message(f"❌ Error durante importación: {result['error']}")

            except ImportError:
                self.movie_log_message("❌ Error: No se encontró robust_movie_importer.py")
            except Exception as e:
                self.movie_log_message(f"❌ Error durante importación: {str(e)}")
            finally:
                # Ocultar barra de progreso
                self.root.after(0, lambda: self.hide_movie_progress())

                # Rehabilitar botones
                if hasattr(self, 'movie_preview_btn'):
                    self.movie_preview_btn.config(state='normal')
                    self.movie_import_btn.config(state='normal')

        # Ejecutar directamente sin thread
        self.root.after(100, import_worker)

    def open_admin_tools(self):
        """Abrir panel de herramientas administrativas integrado"""
        # Ocultar el contenido principal
        self.content_frame.pack_forget()

        # Crear y mostrar el panel administrativo
        self.create_admin_panel()

        # Cambiar el texto del botón para volver
        self.admin_btn.config(text="🔙 Volver", command=self.close_admin_tools)

    def close_admin_tools(self):
        """Cerrar panel administrativo y volver al principal"""
        # Ocultar panel administrativo si existe
        if hasattr(self, 'admin_panel'):
            self.admin_panel.pack_forget()

        # Mostrar contenido principal
        self.content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Restaurar el botón original
        self.admin_btn.config(text="🛠️ Herramientas Admin", command=self.open_admin_tools)

    def open_category_assignment(self):
        """Abrir ventana de asignación de categorías"""
        try:
            # Verificar que hay un archivo M3U cargado
            file_path = self.file_var.get()
            if not file_path:
                tk.messagebox.showwarning(
                    "Archivo Requerido",
                    "Primero debes seleccionar un archivo M3U en el panel principal\n"
                    "antes de asignar categorías."
                )
                return

            if not os.path.exists(file_path):
                tk.messagebox.showerror(
                    "Archivo No Encontrado",
                    "El archivo M3U seleccionado no existe.\n"
                    "Por favor selecciona un archivo válido."
                )
                return

            # Crear ventana de asignación con el archivo ya cargado
            self.create_simple_category_window(file_path)

        except Exception as e:
            print(f"❌ Error abriendo asignación de categorías: {e}")
            self.log_message(f"❌ Error: {str(e)}")

    def open_bouquet_assignment(self):
        """Abrir ventana de asignación de bouquets"""
        try:
            # Verificar que hay un archivo M3U cargado
            file_path = self.file_var.get()
            if not file_path:
                tk.messagebox.showwarning(
                    "Archivo Requerido",
                    "Primero debes seleccionar un archivo M3U en el panel principal\n"
                    "antes de asignar bouquets."
                )
                return

            if not os.path.exists(file_path):
                tk.messagebox.showerror(
                    "Archivo No Encontrado",
                    "El archivo M3U seleccionado no existe.\n"
                    "Por favor selecciona un archivo válido."
                )
                return

            # Crear ventana de asignación con el archivo ya cargado
            self.create_simple_bouquet_window(file_path)

        except Exception as e:
            print(f"❌ Error abriendo asignación de bouquets: {e}")
            self.log_message(f"❌ Error: {str(e)}")

    def create_simple_category_window(self, file_path):
        """Crear ventana simple de asignación de categorías"""
        try:
            # Crear ventana
            category_window = tk.Toplevel(self.root)
            category_window.title("🏷️ Asignación de Categorías")
            category_window.geometry("900x700")
            category_window.configure(bg=self.colors['bg_primary'])

            # Hacer modal
            category_window.transient(self.root)
            category_window.grab_set()

            # Centrar ventana
            category_window.update_idletasks()
            x = (category_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (category_window.winfo_screenheight() // 2) - (700 // 2)
            category_window.geometry(f"900x700+{x}+{y}")

            # Guardar referencia al archivo
            category_window.m3u_file_path = file_path

            # Crear contenido
            self.create_category_assignment_content(category_window)

        except Exception as e:
            print(f"❌ Error creando ventana de categorías: {e}")

    def create_simple_bouquet_window(self, file_path):
        """Crear ventana simple de asignación de bouquets"""
        try:
            # Crear ventana
            bouquet_window = tk.Toplevel(self.root)
            bouquet_window.title("📋 Asignación de Bouquets")
            bouquet_window.geometry("900x700")
            bouquet_window.configure(bg=self.colors['bg_primary'])

            # Hacer modal
            bouquet_window.transient(self.root)
            bouquet_window.grab_set()

            # Centrar ventana
            bouquet_window.update_idletasks()
            x = (bouquet_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (bouquet_window.winfo_screenheight() // 2) - (700 // 2)
            bouquet_window.geometry(f"900x700+{x}+{y}")

            # Guardar referencia al archivo
            bouquet_window.m3u_file_path = file_path

            # Crear contenido
            self.create_bouquet_assignment_content(bouquet_window)

        except Exception as e:
            print(f"❌ Error creando ventana de bouquets: {e}")

    def create_admin_panel(self):
        """Crear panel integrado de herramientas administrativas"""
        # Frame principal del panel administrativo
        self.admin_panel = tk.Frame(self.main_frame, bg=self.colors['bg_primary'])
        self.admin_panel.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # Configurar grid del panel con proporciones optimizadas
        self.admin_panel.columnconfigure(0, weight=0)  # Botones (ancho natural)
        self.admin_panel.columnconfigure(1, weight=2)  # Datos (más espacio)
        self.admin_panel.columnconfigure(2, weight=3)  # TMDB (más espacio para carátulas)
        self.admin_panel.rowconfigure(1, weight=1)

        # Título del panel con estilo NVIDIA
        title_frame = tk.Frame(self.admin_panel, bg=self.colors['bg_primary'])
        title_frame.grid(row=0, column=0, columnspan=3, pady=(0, 20), sticky='ew')

        admin_title = tk.Label(title_frame, text="🛠️ HERRAMIENTAS ADMINISTRATIVAS",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['nvidia_green'], bg=self.colors['bg_primary'])
        admin_title.pack(side='left')

        # Línea decorativa verde
        separator_line = tk.Frame(title_frame, height=3, bg=self.colors['nvidia_green'])
        separator_line.pack(fill='x', pady=(10, 0))

        # Crear las tres columnas
        self.create_admin_buttons_column()
        self.create_admin_data_column()
        self.create_admin_tmdb_column()

    def create_admin_buttons_column(self):
        """Crear columna de botones de herramientas estilo NVIDIA"""
        # Frame contenedor de botones con bordes verdes
        buttons_container = tk.Frame(self.admin_panel, bg=self.colors['bg_card'],
                                   relief='solid', bd=2, highlightbackground=self.colors['nvidia_green'])
        buttons_container.grid(row=1, column=0, sticky='nsew', padx=(0, 2))

        # Canvas y scrollbar para hacer scrollable
        canvas = tk.Canvas(buttons_container, bg=self.colors['bg_card'], highlightthickness=0)
        scrollbar = tk.Scrollbar(buttons_container, orient="vertical", command=canvas.yview,
                               bg=self.colors['bg_tertiary'], troughcolor=self.colors['bg_card'])
        buttons_frame = tk.Frame(canvas, bg=self.colors['bg_card'])



        # Crear ventana del frame en el canvas y obtener su ID
        canvas_window = canvas.create_window((0, 0), window=buttons_frame, anchor="nw")

        # Función para ajustar el ancho del frame al canvas
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # Hacer que el frame tenga el mismo ancho que el canvas
            canvas.itemconfig(canvas_window, width=event.width)

        canvas.bind('<Configure>', configure_scroll_region)

        # También configurar cuando el frame cambie
        buttons_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Habilitar scroll con rueda del mouse
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Título de la columna compacto estilo NVIDIA
        title_container = tk.Frame(buttons_frame, bg=self.colors['bg_card'])
        title_container.pack(fill='x', pady=(8, 10))

        buttons_title = tk.Label(title_container, text="🔧 HERRAMIENTAS",
                                font=('Segoe UI', 12, 'bold'),
                                fg=self.colors['nvidia_green'], bg=self.colors['bg_card'])
        buttons_title.pack()

        # Línea decorativa
        title_line = tk.Frame(title_container, height=2, bg=self.colors['nvidia_green'])
        title_line.pack(fill='x', pady=(3, 0))

        # Botones de herramientas compactos estilo NVIDIA + Gemini
        button_config = {
            'font': ('Segoe UI', 8, 'bold'),
            'bd': 1,
            'relief': 'solid',
            'pady': 3
        }

        # Botón: Detectar Huérfanos (celeste Gemini)
        self.btn_orphans = tk.Button(buttons_frame, text="🔍 Episodios Huérfanos",
                                    command=self.detect_orphan_episodes,
                                    bg=self.colors['accent'], fg=self.colors['bg_primary'],
                                    activebackground=self.colors['accent_hover'],
                                    highlightbackground=self.colors['accent'],
                                    **button_config)
        self.btn_orphans.pack(pady=1, padx=3, fill='x')

        # Botón: Detectar Fantasmas (naranja)
        self.btn_phantoms = tk.Button(buttons_frame, text="👻 Episodios Fantasmas",
                                     command=self.detect_phantom_episodes,
                                     bg=self.colors['warning'], fg=self.colors['bg_primary'],
                                     activebackground='#b8730a',
                                     highlightbackground=self.colors['warning'],
                                     **button_config)
        self.btn_phantoms.pack(pady=1, padx=3, fill='x')

        # Botón: Detectar Duplicados (rojo)
        self.btn_duplicates = tk.Button(buttons_frame, text="🔄 Episodios Duplicados",
                                       command=self.detect_duplicate_episodes,
                                       bg=self.colors['error'], fg=self.colors['text_primary'],
                                       activebackground='#d42c20',
                                       highlightbackground=self.colors['error'],
                                       **button_config)
        self.btn_duplicates.pack(pady=1, padx=3, fill='x')

        # Botón: Procesar Duplicados Inteligente (verde NVIDIA)
        self.btn_smart_clean = tk.Button(buttons_frame, text="🧹 Procesar Duplicados",
                                        command=self.smart_clean_duplicates,
                                        bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                        activebackground=self.colors['nvidia_green_hover'],
                                        highlightbackground=self.colors['nvidia_green'],
                                        **button_config)
        self.btn_smart_clean.pack(pady=1, padx=3, fill='x')

        # Botón: Asignar Series (verde éxito)
        self.btn_assign = tk.Button(buttons_frame, text="🏠 Asignar Series",
                                   command=self.assign_series_to_orphans,
                                   bg=self.colors['success'], fg=self.colors['text_primary'],
                                   activebackground='#1e6b2e',
                                   highlightbackground=self.colors['success'],
                                   **button_config)
        self.btn_assign.pack(pady=1, padx=3, fill='x')

        # Botón: TMDB Assigner (gris)
        self.btn_tmdb = tk.Button(buttons_frame, text="📊 Cargar TMDB",
                                 command=self.open_tmdb_assigner,
                                 bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                 activebackground=self.colors['bg_tertiary'],
                                 highlightbackground=self.colors['nvidia_green'],
                                 **button_config)
        self.btn_tmdb.pack(pady=1, padx=3, fill='x')

        # Botón: Asignación Automática TMDB (celeste)
        self.btn_auto_tmdb = tk.Button(buttons_frame, text="🤖 Auto TMDB",
                                      command=self.auto_assign_tmdb,
                                      bg=self.colors['accent'], fg=self.colors['bg_primary'],
                                      activebackground=self.colors['accent_hover'],
                                      highlightbackground=self.colors['accent'],
                                      **button_config)
        self.btn_auto_tmdb.pack(pady=1, padx=3, fill='x')

        # Botón: Asignación Manual TMDB (naranja)
        self.btn_manual_tmdb = tk.Button(buttons_frame, text="🔍 Manual TMDB",
                                        command=self.manual_assign_tmdb,
                                        bg=self.colors['warning'], fg=self.colors['bg_primary'],
                                        activebackground='#b8730a',
                                        highlightbackground=self.colors['warning'],
                                        **button_config)
        self.btn_manual_tmdb.pack(pady=1, padx=3, fill='x')

        # Separador verde NVIDIA compacto
        separator = tk.Frame(buttons_frame, height=2, bg=self.colors['nvidia_green'])
        separator.pack(fill='x', padx=3, pady=5)

        # Botón: Limpiar Resultados (gris)
        self.btn_clear = tk.Button(buttons_frame, text="🧹 Limpiar",
                                  command=self.clear_admin_results,
                                  bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                  activebackground=self.colors['bg_tertiary'],
                                  highlightbackground=self.colors['nvidia_green'],
                                  **button_config)
        self.btn_clear.pack(pady=1, padx=3, fill='x')

        # Separador compacto
        separator2 = tk.Frame(buttons_frame, height=2, bg=self.colors['nvidia_green'])
        separator2.pack(fill='x', padx=3, pady=5)

        # === SECCIÓN DE PELÍCULAS COMPACTA ===
        movies_container = tk.Frame(buttons_frame, bg=self.colors['bg_card'])
        movies_container.pack(fill='x', pady=(5, 3))

        movies_label = tk.Label(movies_container, text="🎬 PELÍCULAS",
                               font=('Segoe UI', 10, 'bold'),
                               fg=self.colors['accent'], bg=self.colors['bg_card'])
        movies_label.pack()

        # Línea decorativa celeste
        movies_line = tk.Frame(movies_container, height=2, bg=self.colors['accent'])
        movies_line.pack(fill='x', pady=(2, 0))

        # Botón: Cargar Películas sin TMDB (rojo)
        self.btn_movies_no_tmdb = tk.Button(buttons_frame, text="🎬 Películas sin TMDB",
                                          command=self.load_movies_without_tmdb,
                                          bg=self.colors['error'], fg=self.colors['text_primary'],
                                          activebackground='#d42c20',
                                          highlightbackground=self.colors['error'],
                                          **button_config)
        self.btn_movies_no_tmdb.pack(pady=1, padx=3, fill='x')

        # Botón: Asignación Automática TMDB Películas (verde NVIDIA)
        self.btn_auto_tmdb_movies = tk.Button(buttons_frame, text="🤖 Auto TMDB Movies",
                                            command=self.auto_assign_tmdb_movies,
                                            bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                            activebackground=self.colors['nvidia_green_hover'],
                                            highlightbackground=self.colors['nvidia_green'],
                                            **button_config)
        self.btn_auto_tmdb_movies.pack(pady=1, padx=3, fill='x')

        # Botón: Asignación Manual TMDB Películas (naranja)
        self.btn_manual_tmdb_movies = tk.Button(buttons_frame, text="🔍 Manual TMDB Movies",
                                              command=self.manual_assign_tmdb_movies,
                                              bg=self.colors['warning'], fg=self.colors['bg_primary'],
                                              activebackground='#b8730a',
                                              highlightbackground=self.colors['warning'],
                                              **button_config)
        self.btn_manual_tmdb_movies.pack(pady=1, padx=3, fill='x')

        # Separador para duplicados de películas
        separator_duplicates = tk.Frame(buttons_frame, height=2, bg=self.colors['accent'])
        separator_duplicates.pack(fill='x', padx=3, pady=3)

        # Botón: Detectar Duplicados Películas Manual (celeste)
        self.btn_movie_duplicates_manual = tk.Button(buttons_frame, text="🔍 Duplicados Manual",
                                                   command=self.detect_movie_duplicates_manual,
                                                   bg=self.colors['accent'], fg=self.colors['bg_primary'],
                                                   activebackground=self.colors['accent_hover'],
                                                   highlightbackground=self.colors['accent'],
                                                   **button_config)
        self.btn_movie_duplicates_manual.pack(pady=1, padx=3, fill='x')

        # Botón: Limpiar Duplicados Películas Auto (rojo)
        self.btn_movie_duplicates_auto = tk.Button(buttons_frame, text="🤖 Limpiar Duplicados",
                                                 command=self.clean_movie_duplicates_auto,
                                                 bg=self.colors['error'], fg=self.colors['text_primary'],
                                                 activebackground='#d42c20',
                                                 highlightbackground=self.colors['error'],
                                                 **button_config)
        self.btn_movie_duplicates_auto.pack(pady=1, padx=3, fill='x')

        # Separador antes del botón de regreso
        separator3 = tk.Frame(buttons_frame, height=2, bg=self.colors['nvidia_green'])
        separator3.pack(fill='x', padx=3, pady=5)

        # Botón: Volver al Panel Principal (estilo NVIDIA compacto)
        self.btn_back = tk.Button(buttons_frame, text="🔙 Panel Principal",
                                 command=self.close_admin_tools,
                                 bg=self.colors['nvidia_green'], fg=self.colors['bg_primary'],
                                 font=('Segoe UI', 9, 'bold'),
                                 activebackground=self.colors['nvidia_green_hover'],
                                 relief='solid', bd=2,
                                 highlightbackground=self.colors['nvidia_green'],
                                 pady=5)
        self.btn_back.pack(pady=5, padx=3, fill='x')

    def create_admin_data_column(self):
        """Crear columna central de datos/listados estilo NVIDIA mejorado"""
        # Frame de datos con bordes verdes más prominentes
        data_frame = tk.Frame(self.admin_panel, bg=self.colors['bg_card'],
                            relief='solid', bd=3, highlightbackground=self.colors['nvidia_green'],
                            highlightthickness=2)
        data_frame.grid(row=1, column=1, sticky='nsew', padx=8, pady=(5, 0))
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(1, weight=1)

        # Header mejorado con gradiente visual
        header_frame = tk.Frame(data_frame, bg=self.colors['nvidia_green'], height=50)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Contenedor del título centrado
        title_container = tk.Frame(header_frame, bg=self.colors['nvidia_green'])
        title_container.pack(expand=True, fill='both')

        self.data_title = tk.Label(title_container, text="📊 DATOS ENCONTRADOS",
                                  font=('Segoe UI', 16, 'bold'),
                                  fg=self.colors['bg_primary'], bg=self.colors['nvidia_green'])
        self.data_title.pack(expand=True)

        # Subtítulo informativo
        subtitle_frame = tk.Frame(data_frame, bg=self.colors['bg_card'])
        subtitle_frame.pack(fill='x', pady=(8, 0))

        subtitle = tk.Label(subtitle_frame, text="Selecciona una herramienta para ver los datos encontrados",
                           font=('Segoe UI', 10),
                           fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
        subtitle.pack()

        # Frame para el contenido de datos con mejor espaciado
        data_content = tk.Frame(data_frame, bg=self.colors['bg_card'])
        data_content.pack(fill='both', expand=True, padx=12, pady=(0, 12))
        data_content.columnconfigure(0, weight=1)
        data_content.rowconfigure(0, weight=1)

        # Área de texto mejorada con bordes
        text_container = tk.Frame(data_content, bg=self.colors['bg_secondary'],
                                 relief='solid', bd=2, highlightbackground=self.colors['nvidia_green'])
        text_container.grid(row=0, column=0, sticky='nsew')
        text_container.columnconfigure(0, weight=1)
        text_container.rowconfigure(0, weight=1)

        self.data_text = scrolledtext.ScrolledText(text_container,
                                                  bg=self.colors['bg_secondary'],
                                                  fg=self.colors['text_primary'],
                                                  font=('Consolas', 10),
                                                  wrap=tk.WORD,
                                                  state='disabled',
                                                  relief='flat', bd=0,
                                                  insertbackground=self.colors['nvidia_green'],
                                                  selectbackground=self.colors['nvidia_green'])
        self.data_text.grid(row=0, column=0, sticky='nsew', padx=2, pady=2)

        # Bind para hacer clic en las series
        self.data_text.bind("<Button-1>", self.on_data_click)

        # Configurar tags para colores mejorados
        self.data_text.tag_configure("header", font=('Consolas', 11, 'bold'), foreground=self.colors['nvidia_green'])
        self.data_text.tag_configure("success", font=('Consolas', 10, 'bold'), foreground=self.colors['success'])
        self.data_text.tag_configure("warning", font=('Consolas', 10, 'bold'), foreground=self.colors['warning'])
        self.data_text.tag_configure("error", font=('Consolas', 10, 'bold'), foreground=self.colors['error'])
        self.data_text.tag_configure("info", foreground=self.colors['text_secondary'])
        self.data_text.tag_configure("accent", font=('Consolas', 10, 'bold'), foreground=self.colors['accent'])

    def create_admin_tmdb_column(self):
        """Crear columna derecha de TMDB estilo Gemini mejorado"""
        # Frame de TMDB con bordes celestes más prominentes
        tmdb_frame = tk.Frame(self.admin_panel, bg=self.colors['bg_card'],
                            relief='solid', bd=3, highlightbackground=self.colors['accent'],
                            highlightthickness=2)
        tmdb_frame.grid(row=1, column=2, sticky='nsew', padx=(8, 0), pady=(5, 0))
        tmdb_frame.columnconfigure(0, weight=1)
        tmdb_frame.rowconfigure(1, weight=1)

        # Header mejorado con fondo celeste
        header_frame = tk.Frame(tmdb_frame, bg=self.colors['accent'], height=50)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Contenedor del título centrado
        title_container = tk.Frame(header_frame, bg=self.colors['accent'])
        title_container.pack(expand=True, fill='both')

        self.tmdb_title = tk.Label(title_container, text="🎬 RESULTADOS TMDB",
                                  font=('Segoe UI', 16, 'bold'),
                                  fg=self.colors['bg_primary'], bg=self.colors['accent'])
        self.tmdb_title.pack(expand=True)

        # Subtítulo informativo
        subtitle_frame = tk.Frame(tmdb_frame, bg=self.colors['bg_card'])
        subtitle_frame.pack(fill='x', pady=(8, 0))

        subtitle = tk.Label(subtitle_frame, text="Selecciona el resultado más apropiado",
                           font=('Segoe UI', 10),
                           fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
        subtitle.pack()

        # Frame para el contenido de TMDB con mejor espaciado
        tmdb_content = tk.Frame(tmdb_frame, bg=self.colors['bg_card'])
        tmdb_content.pack(fill='both', expand=True, padx=12, pady=(0, 12))
        tmdb_content.columnconfigure(0, weight=1)
        tmdb_content.rowconfigure(0, weight=1)

        # Área de texto mejorada con bordes celestes
        text_container = tk.Frame(tmdb_content, bg=self.colors['bg_secondary'],
                                 relief='solid', bd=2, highlightbackground=self.colors['accent'])
        text_container.grid(row=0, column=0, sticky='nsew')
        text_container.columnconfigure(0, weight=1)
        text_container.rowconfigure(0, weight=1)

        self.tmdb_text = scrolledtext.ScrolledText(text_container,
                                                  bg=self.colors['bg_secondary'],
                                                  fg=self.colors['text_primary'],
                                                  font=('Consolas', 10),
                                                  wrap=tk.WORD,
                                                  state='disabled',
                                                  relief='flat', bd=0,
                                                  insertbackground=self.colors['accent'],
                                                  selectbackground=self.colors['accent'])
        self.tmdb_text.grid(row=0, column=0, sticky='nsew', padx=2, pady=2)

        # Configurar tags para colores mejorados
        self.tmdb_text.tag_configure("header", font=('Consolas', 11, 'bold'), foreground=self.colors['accent'])
        self.tmdb_text.tag_configure("success", font=('Consolas', 10, 'bold'), foreground=self.colors['success'])
        self.tmdb_text.tag_configure("warning", font=('Consolas', 10, 'bold'), foreground=self.colors['warning'])
        self.tmdb_text.tag_configure("error", font=('Consolas', 10, 'bold'), foreground=self.colors['error'])
        self.tmdb_text.tag_configure("info", foreground=self.colors['text_secondary'])
        self.tmdb_text.tag_configure("nvidia", font=('Consolas', 10, 'bold'), foreground=self.colors['nvidia_green'])

    def update_data_display(self, message, tag=None):
        """Actualizar el display de datos"""
        if hasattr(self, 'data_text'):
            self.data_text.config(state='normal')
            self.data_text.insert(tk.END, message + "\n")
            if tag:
                start_line = self.data_text.index(tk.END + "-2l linestart")
                end_line = self.data_text.index(tk.END + "-1l lineend")
                self.data_text.tag_add(tag, start_line, end_line)
            self.data_text.see(tk.END)
            self.data_text.config(state='disabled')
            self.data_text.update()

    def update_tmdb_display(self, message, tag=None):
        """Actualizar el display de TMDB"""
        if hasattr(self, 'tmdb_text'):
            self.tmdb_text.config(state='normal')
            self.tmdb_text.insert(tk.END, message + "\n")
            if tag:
                start_line = self.tmdb_text.index(tk.END + "-2l linestart")
                end_line = self.tmdb_text.index(tk.END + "-1l lineend")
                self.tmdb_text.tag_add(tag, start_line, end_line)
            self.tmdb_text.see(tk.END)
            self.tmdb_text.config(state='disabled')
            self.tmdb_text.update()

    def clear_admin_results(self):
        """Limpiar resultados de las herramientas administrativas"""
        if hasattr(self, 'data_text'):
            self.data_text.config(state='normal')
            self.data_text.delete(1.0, tk.END)
            self.data_text.config(state='disabled')
            self.update_data_display("Resultados limpiados. Selecciona una herramienta.", "info")

        if hasattr(self, 'tmdb_text'):
            self.tmdb_text.config(state='normal')
            self.tmdb_text.delete(1.0, tk.END)
            self.tmdb_text.config(state='disabled')
            self.update_tmdb_display("Panel TMDB limpiado.", "info")

    # ==================== FUNCIONES DE HERRAMIENTAS ADMINISTRATIVAS ====================

    def detect_orphan_episodes(self):
        """Detectar episodios huérfanos"""
        self.update_data_display("🔍 DETECTANDO EPISODIOS HUÉRFANOS...", "header")
        self.update_data_display("Buscando episodios sin serie asignada...", "info")

        def detect_worker():
            try:
                from orphan_episodes_detector import OrphanEpisodesDetector

                detector = OrphanEpisodesDetector()
                if detector.connect():
                    orphans = detector.find_orphan_episodes()

                    # Mostrar resultados en el panel
                    total_orphans = sum(len(orphan_list) for orphan_list in orphans.values())

                    self.update_data_display(f"✅ Análisis completado", "success")
                    self.update_data_display(f"📊 Total episodios huérfanos: {total_orphans}", "info")

                    for category, episodes in orphans.items():
                        if episodes:
                            self.update_data_display(f"\n🔍 {category.upper()}: {len(episodes)} episodios", "warning")
                            for i, episode in enumerate(episodes[:5], 1):
                                self.update_data_display(f"   {i}. {episode['stream_display_name'][:50]}...", "info")
                            if len(episodes) > 5:
                                self.update_data_display(f"   ... y {len(episodes) - 5} más", "info")

                    # Guardar datos para uso posterior
                    self.current_orphans = orphans

                    # Agregar botones de acción
                    self.add_orphan_action_buttons()

                    detector.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, detect_worker)

    def detect_phantom_episodes(self):
        """Detectar episodios fantasmas"""
        self.update_data_display("👻 DETECTANDO EPISODIOS FANTASMAS...", "header")
        self.update_data_display("Buscando referencias rotas en streams_episodes...", "info")

        def detect_worker():
            try:
                from phantom_episodes_detector import PhantomEpisodesDetector

                detector = PhantomEpisodesDetector()
                if detector.connect():
                    phantoms = detector.find_phantom_episodes()

                    if phantoms:
                        self.update_data_display(f"👻 Episodios fantasmas encontrados: {len(phantoms)}", "warning")

                        # Agrupar por serie
                        series_phantoms = {}
                        for phantom in phantoms:
                            series_id = phantom['series_id']
                            if series_id not in series_phantoms:
                                series_phantoms[series_id] = []
                            series_phantoms[series_id].append(phantom)

                        for series_id, phantom_list in series_phantoms.items():
                            self.update_data_display(f"📺 Serie {series_id}: {len(phantom_list)} fantasmas", "error")
                    else:
                        self.update_data_display("✅ No se encontraron episodios fantasmas", "success")

                    detector.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, detect_worker)

    def detect_duplicate_episodes(self):
        """Detectar episodios duplicados"""
        self.update_data_display("🔄 DETECTANDO EPISODIOS DUPLICADOS...", "header")
        self.update_data_display("Buscando episodios duplicados en streams_episodes...", "info")

        def detect_worker():
            try:
                from duplicate_episodes_detector import DuplicateEpisodesDetector

                detector = DuplicateEpisodesDetector()
                if detector.connect():
                    duplicates = detector.find_duplicate_episodes()

                    if duplicates:
                        total_duplicates = sum(dup['duplicate_count'] - 1 for dup in duplicates)
                        self.update_data_display(f"🔄 Episodios duplicados encontrados: {len(duplicates)}", "warning")
                        self.update_data_display(f"🗑️ Registros duplicados a eliminar: {total_duplicates}", "info")

                        # Mostrar algunos ejemplos
                        for i, dup in enumerate(duplicates[:10], 1):
                            series_title = dup['series_title'] or f"Serie {dup['series_id']}"
                            self.update_data_display(f"   {i}. {series_title} S{dup['season_num']:02d}E{dup['episode_num']:02d} ({dup['duplicate_count']} veces)", "warning")

                        if len(duplicates) > 10:
                            self.update_data_display(f"   ... y {len(duplicates) - 10} más", "info")
                    else:
                        self.update_data_display("✅ No se encontraron episodios duplicados", "success")

                    detector.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, detect_worker)

    def smart_clean_duplicates(self):
        """Procesar duplicados con lógica inteligente"""
        self.update_data_display("🧹 PROCESANDO DUPLICADOS INTELIGENTEMENTE...", "header")
        self.update_data_display("Analizando episodios duplicados con lógica inteligente...", "info")
        self.update_data_display("• Respetando symlinks (direct_source=0, direct_proxy=0)", "info")
        self.update_data_display("• Solo eliminando direct streams antiguos", "info")
        self.update_data_display("• Manteniendo streams más recientes", "info")

        def smart_clean_worker():
            try:
                from smart_duplicate_cleaner import SmartDuplicateCleaner

                cleaner = SmartDuplicateCleaner()
                if cleaner.connect():
                    # Obtener resumen de duplicados
                    summary = cleaner.get_duplicate_summary()

                    if summary['total_episodes'] == 0:
                        self.update_data_display("✅ No se encontraron episodios duplicados para procesar", "success")
                        cleaner.disconnect()
                        return

                    # Mostrar resumen
                    self.update_data_display(f"🔄 ANÁLISIS COMPLETADO:", "warning")
                    self.update_data_display(summary['summary'], "info")

                    # Ejecutar en hilo principal para mostrar diálogo
                    self.root.after(0, lambda: self.handle_smart_clean_confirmation(cleaner, summary))

                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, smart_clean_worker)

    def handle_smart_clean_confirmation(self, cleaner, summary):
        """Manejar confirmación del usuario para limpieza inteligente"""
        try:
            result = tk.messagebox.askyesnocancel(
                "Procesar Duplicados",
                f"Se encontraron {summary['deletable_streams']} streams direct antiguos eliminables.\n\n"
                f"• Los symlinks serán respetados\n"
                f"• Solo se eliminarán direct streams antiguos\n"
                f"• Se mantendrán los streams más recientes\n\n"
                f"¿Cómo proceder?\n\n"
                f"SÍ = Simulación (dry run)\n"
                f"NO = Limpieza real\n"
                f"Cancelar = No hacer nada",
                icon='question'
            )

            if result is True:  # Simulación
                self.update_data_display("🧪 EJECUTANDO SIMULACIÓN...", "info")
                def simulate():
                    try:
                        deleted_count = cleaner.clean_smart_duplicates(summary['duplicates'], dry_run=True)
                        self.update_data_display(f"🧪 SIMULACIÓN COMPLETADA: {deleted_count} registros se eliminarían", "success")
                        cleaner.disconnect()
                    except Exception as e:
                        self.update_data_display(f"❌ Error en simulación: {str(e)}", "error")
                        cleaner.disconnect()

                self.root.after(100, simulate)

            elif result is False:  # Limpieza real
                # Confirmación adicional para limpieza real
                confirm = tk.messagebox.askyesno(
                    "CONFIRMACIÓN FINAL",
                    f"⚠️ ATENCIÓN: Esta acción eliminará permanentemente {summary['deletable_streams']} registros.\n\n"
                    f"Esta acción NO se puede deshacer.\n\n"
                    f"¿Estás seguro de proceder con la limpieza real?",
                    icon='warning'
                )

                if confirm:
                    self.update_data_display("🧹 EJECUTANDO LIMPIEZA REAL...", "warning")
                    def clean_real():
                        try:
                            deleted_count = cleaner.clean_smart_duplicates(summary['duplicates'], dry_run=False)
                            self.update_data_display(f"✅ LIMPIEZA COMPLETADA: {deleted_count} registros eliminados", "success")
                            cleaner.disconnect()
                        except Exception as e:
                            self.update_data_display(f"❌ Error en limpieza: {str(e)}", "error")
                            cleaner.disconnect()

                    self.root.after(100, clean_real)
                else:
                    self.update_data_display("❌ Limpieza real cancelada por el usuario", "info")
                    cleaner.disconnect()
            else:  # Cancelado
                self.update_data_display("❌ Operación cancelada por el usuario", "info")
                cleaner.disconnect()

        except Exception as e:
            self.update_data_display(f"❌ Error manejando confirmación: {str(e)}", "error")
            cleaner.disconnect()

    def assign_series_to_orphans(self):
        """Asignar series a episodios huérfanos"""
        self.update_data_display("🏠 ASIGNANDO SERIES A HUÉRFANOS...", "header")
        self.update_data_display("Procesando episodios huérfanos y creando series...", "info")

        def assign_worker():
            try:
                from orphan_series_assigner import OrphanSeriesAssigner

                assigner = OrphanSeriesAssigner()
                if assigner.connect():
                    # Detectar huérfanos
                    orphans = assigner.find_orphan_episodes()
                    relevant_orphans = orphans['no_relation'] + orphans['no_series']

                    if not relevant_orphans:
                        self.update_data_display("✅ No hay episodios huérfanos que necesiten asignación", "success")
                        return

                    # Agrupar por serie
                    series_groups = assigner.group_orphans_by_series(orphans)

                    if series_groups:
                        self.update_data_display(f"📺 Series detectadas: {len(series_groups)}", "info")

                        # Procesar automáticamente
                        created_series = 0
                        assigned_episodes = 0

                        for series_name, group in series_groups.items():
                            episodes = group['episodes']

                            # Verificar si existe
                            existing = assigner.db.execute_query(
                                "SELECT id FROM streams_series WHERE id = %s", (series_id,)
                            )

                            if not existing:
                                if assigner.create_missing_series(series_name, series_id, episodes[0]):
                                    created_series += 1
                                else:
                                    self.update_data_display(f"⚠️ Serie ya existe: {series_name}", "warning")

                            # Asignar episodios
                            for episode in episodes:
                                if assigner.assign_episode_to_series(episode['id'], series_id):
                                    assigned_episodes += 1

                        self.update_data_display(f"🎉 ASIGNACIÓN COMPLETADA:", "header")
                        self.update_data_display(f"   📺 Series creadas: {created_series}", "success")
                        self.update_data_display(f"   📼 Episodios asignados: {assigned_episodes}", "success")
                    else:
                        self.update_data_display("⚠️ No se detectaron grupos de series", "warning")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, assign_worker)

    def clean_orphan_episodes(self):
        """Limpiar episodios huérfanos"""
        self.update_data_display("🧹 LIMPIANDO EPISODIOS HUÉRFANOS...", "header")
        self.update_data_display("Eliminando episodios sin serie asociada...", "info")

        def clean_worker():
            try:
                from orphan_episode_cleaner import OrphanEpisodeCleaner

                cleaner = OrphanEpisodeCleaner()
                if cleaner.connect():
                    # Encontrar huérfanos
                    orphans = cleaner.find_orphan_episodes()

                    if orphans:
                        self.update_data_display(f"🔍 Episodios huérfanos encontrados: {len(orphans)}", "warning")

                        # Confirmar eliminación
                        confirm = tk.messagebox.askyesno(
                            "Confirmar Limpieza",
                            f"Se encontraron {len(orphans)} episodios huérfanos.\n\n"
                            f"¿Deseas eliminarlos permanentemente?\n\n"
                            f"⚠️ Esta acción no se puede deshacer.",
                            icon='warning'
                        )

                        if confirm:
                            deleted = cleaner.clean_orphan_episodes(orphans)
                            self.update_data_display(f"✅ Episodios eliminados: {deleted}", "success")
                        else:
                            self.update_data_display("❌ Limpieza cancelada por el usuario", "info")
                    else:
                        self.update_data_display("✅ No hay episodios huérfanos para limpiar", "success")

                    cleaner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, clean_worker)

    def open_tmdb_assignment(self):
        """Abrir ventana de asignación TMDB"""
        self.update_data_display("🎬 ABRIENDO TMDB ASSIGNMENT...", "header")
        self.update_data_display("Iniciando sistema de asignación TMDB para series", "info")

        def tmdb_worker():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Obtener estadísticas
                    self.update_data_display("📊 Obteniendo estadísticas...", "info")

                    series_without_tmdb = assigner.find_series_without_tmdb(50)

                    if series_without_tmdb:
                        self.update_data_display(f"🔍 SERIES SIN TMDB ENCONTRADAS: {len(series_without_tmdb)}", "warning")

                        # Mostrar primeras series
                        for i, series in enumerate(series_without_tmdb[:10], 1):
                            self.update_data_display(
                                f"   {i:2d}. {series['title']} ({series['year']}) - {series['episode_count']} episodios",
                                "info"
                            )

                        if len(series_without_tmdb) > 10:
                            self.update_data_display(f"   ... y {len(series_without_tmdb) - 10} más", "info")

                        # Ejecutar en hilo principal para mostrar diálogo
                        self.root.after(0, lambda: self.handle_tmdb_options(assigner, series_without_tmdb))
                    else:
                        self.update_data_display("✅ Todas las series ya tienen TMDB asignado", "success")
                        assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        self.root.after(100, tmdb_worker)

    def handle_tmdb_options(self, assigner, series_without_tmdb):
        """Manejar opciones de TMDB"""
        try:
            result = tk.messagebox.askyesnocancel(
                "TMDB Assignment",
                f"Se encontraron {len(series_without_tmdb)} series sin TMDB.\n\n"
                f"¿Cómo proceder?\n\n"
                f"SÍ = Búsqueda individual (seleccionar serie)\n"
                f"NO = Asignación automática (primeras 20 series)\n"
                f"Cancelar = No hacer nada",
                icon='question'
            )

            if result is True:  # Búsqueda individual
                self.open_individual_search(assigner, series_without_tmdb)
            elif result is False:  # Asignación automática
                self.start_automatic_assignment(assigner, series_without_tmdb)
            else:  # Cancelado
                self.update_data_display("❌ Operación cancelada por el usuario", "info")
                assigner.disconnect()

        except Exception as e:
            self.update_data_display(f"❌ Error manejando opciones TMDB: {str(e)}", "error")
            assigner.disconnect()

    def open_individual_search(self, assigner, series_list):
        """Abrir búsqueda individual de TMDB"""
        try:
            # Crear ventana de selección
            search_window = tk.Toplevel(self.root)
            search_window.title("🔍 Búsqueda TMDB Individual")
            search_window.geometry("1000x700")
            search_window.configure(bg=self.colors['bg_primary'])

            # Hacer modal
            search_window.transient(self.root)
            search_window.grab_set()

            # Centrar ventana
            search_window.update_idletasks()
            x = (search_window.winfo_screenwidth() // 2) - (1000 // 2)
            y = (search_window.winfo_screenheight() // 2) - (700 // 2)
            search_window.geometry(f"1000x700+{x}+{y}")

            # Crear contenido
            self.create_individual_content(search_window, assigner, series_list)

        except Exception as e:
            print(f"❌ Error abriendo búsqueda individual: {e}")
            assigner.disconnect()

    def start_automatic_assignment(self, assigner, series_list):
        """Iniciar asignación automática"""
        try:
            confirm = tk.messagebox.askyesno(
                "Asignación Automática",
                f"🤖 Asignación automática TMDB\n\n"
                f"Se procesarán las primeras 20 series automáticamente.\n"
                f"El sistema buscará y asignará el resultado más relevante.\n\n"
                f"⚠️ Esta operación puede tomar varios minutos.\n\n"
                f"¿Continuar con la asignación automática?",
                icon='question'
            )

            if confirm:
                self.update_data_display("🤖 INICIANDO ASIGNACIÓN AUTOMÁTICA...", "warning")

                def auto_assign_worker():
                    try:
                        assigned_count = 0
                        error_count = 0
                        max_series = min(20, len(series_list))

                        for i, series in enumerate(series_list[:max_series], 1):
                            try:
                                series_id = series['id']
                                title = series['title']
                                year = series['year']

                                self.update_data_display(f"{i}/{max_series} - 🔍 Buscando: {title}", "info")

                                # Búsqueda TMDB
                                results = assigner.search_tmdb_series(title, year)

                                if results:
                                    # Tomar el mejor resultado
                                    best_match = results[0]
                                    tmdb_id = best_match['id']
                                    match_title = best_match.get('name', 'Sin título')
                                    score = best_match.get('vote_average', 0)

                                    self.update_data_display(
                                        f"   ✅ Encontrado: {match_title} (Score: {score:.1f})",
                                        "success"
                                    )

                                    # Obtener detalles completos
                                    tmdb_details = assigner.get_tmdb_series_details(tmdb_id)

                                    if tmdb_details:
                                        # Asignar a la serie
                                        if assigner.assign_tmdb_to_series(series_id, tmdb_details):
                                            assigned_count += 1
                                            self.update_data_display(f"   ✅ Asignado exitosamente", "success")
                                        else:
                                            error_count += 1
                                            self.update_data_display(f"   ❌ Error asignando a BD", "error")
                                    else:
                                        error_count += 1
                                        self.update_data_display(f"   ❌ Error obteniendo detalles", "error")
                                else:
                                    self.update_data_display(f"   ⚠️ No encontrado en TMDB", "warning")

                                # Pausa para rate limiting
                                time.sleep(0.5)

                            except Exception as e:
                                error_count += 1
                                self.update_data_display(f"   ❌ Error procesando: {str(e)}", "error")

                        # Resumen final
                        self.update_data_display(f"🎉 ASIGNACIÓN AUTOMÁTICA COMPLETADA:", "header")
                        self.update_data_display(f"   ✅ Asignadas: {assigned_count}", "success")
                        self.update_data_display(f"   ❌ Errores: {error_count}", "error")
                        self.update_data_display(f"   📊 Total procesadas: {max_series}", "info")

                        assigner.disconnect()

                    except Exception as e:
                        self.update_data_display(f"❌ Error en asignación automática: {str(e)}", "error")
                        assigner.disconnect()

                self.root.after(100, auto_assign_worker)
            else:
                self.update_data_display("❌ Asignación automática cancelada", "info")
                assigner.disconnect()

        except Exception as e:
            self.update_data_display(f"❌ Error iniciando asignación automática: {str(e)}", "error")
            assigner.disconnect()

    def create_individual_content(self, window, assigner, series_list):
        """Crear contenido para búsqueda individual de TMDB"""
        try:
            # Título
            title_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            title_frame.pack(fill='x', padx=20, pady=(20, 10))

            title_label = tk.Label(title_frame, text="🔍 BÚSQUEDA TMDB INDIVIDUAL",
                                 font=('Segoe UI', 16, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
            title_label.pack()

            desc_label = tk.Label(title_frame, text="Selecciona una serie para buscar en TMDB",
                                font=('Segoe UI', 10),
                                fg=self.colors['text_secondary'], bg=self.colors['bg_primary'])
            desc_label.pack(pady=(5, 0))

            # Frame principal
            main_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # Configurar grid
            main_frame.columnconfigure(0, weight=1)
            main_frame.columnconfigure(1, weight=1)
            main_frame.rowconfigure(0, weight=1)

            # Panel izquierdo: Lista de series
            self.create_series_selection_panel(main_frame, window, series_list, assigner)

            # Panel derecho: Resultados de búsqueda
            self.create_tmdb_results_panel(main_frame, window, assigner)

            # Frame de botones
            button_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            button_frame.pack(fill='x', padx=20, pady=(0, 20))

            # Botón cerrar
            close_btn = tk.Button(button_frame, text="❌ Cerrar",
                                command=lambda: self.close_tmdb_window(window, assigner),
                                bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['border'])
            close_btn.pack(side='right', ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando contenido TMDB individual: {e}")

    def create_series_selection_panel(self, parent, window, series_list, assigner):
        """Crear panel de selección de series"""
        try:
            # Frame de series
            series_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            series_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))

            # Título
            series_title = tk.Label(series_frame, text="📺 SERIES SIN TMDB",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            series_title.pack(pady=(15, 10))

            # Frame con scroll
            scroll_frame = tk.Frame(series_frame, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Variable para serie seleccionada
            window.selected_series = tk.IntVar()
            window.series_data = {}

            # Crear botones para cada serie
            for i, series in enumerate(series_list[:30], 1):  # Limitar a 30 para performance
                series_frame_item = tk.Frame(scrollable_frame, bg=self.colors['bg_card'])
                series_frame_item.pack(fill='x', pady=2)

                # Guardar datos de la serie
                window.series_data[series['id']] = series

                radio = tk.Radiobutton(series_frame_item,
                                     text=f"{series['title']} ({series['year']}) - {series['episode_count']} eps",
                                     variable=window.selected_series,
                                     value=series['id'],
                                     font=('Segoe UI', 9),
                                     fg=self.colors['text_primary'],
                                     bg=self.colors['bg_card'],
                                     selectcolor=self.colors['bg_secondary'],
                                     activebackground=self.colors['bg_card'],
                                     activeforeground=self.colors['accent'],
                                     wraplength=300,
                                     command=lambda: self.search_selected_series(window, assigner))
                radio.pack(anchor='w', padx=5, pady=2)

                # Seleccionar la primera por defecto
                if i == 1:
                    window.selected_series.set(series['id'])

        except Exception as e:
            print(f"❌ Error creando panel de selección: {e}")

    def create_tmdb_results_panel(self, parent, window, assigner):
        """Crear panel de resultados TMDB"""
        try:
            # Frame de resultados
            results_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            results_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))

            # Título
            results_title = tk.Label(results_frame, text="🔍 RESULTADOS TMDB",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            results_title.pack(pady=(15, 10))

            # Frame de búsqueda personalizada
            search_frame = tk.Frame(results_frame, bg=self.colors['bg_card'])
            search_frame.pack(fill='x', padx=15, pady=(0, 10))

            # Campo de búsqueda personalizada
            search_label = tk.Label(search_frame, text="Buscar manualmente:",
                                  font=('Segoe UI', 9),
                                  fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
            search_label.pack(anchor='w')

            search_input_frame = tk.Frame(search_frame, bg=self.colors['bg_card'])
            search_input_frame.pack(fill='x', pady=(5, 0))

            window.custom_search_entry = tk.Entry(search_input_frame,
                                                 font=('Segoe UI', 10),
                                                 bg=self.colors['bg_secondary'],
                                                 fg=self.colors['text_primary'],
                                                 insertbackground=self.colors['text_primary'],
                                                 bd=1, relief='solid')
            window.custom_search_entry.pack(side='left', fill='x', expand=True, ipady=5)

            search_btn = tk.Button(search_input_frame, text="🔍",
                                 command=lambda: self.search_custom_term(window, assigner),
                                 bg=self.colors['accent'], fg='white',
                                 font=('Segoe UI', 10, 'bold'), bd=0,
                                 activebackground=self.colors['accent'])
            search_btn.pack(side='right', padx=(5, 0), ipady=5, ipadx=10)

            # Bind Enter key to search
            window.custom_search_entry.bind('<Return>', lambda e: self.search_custom_term(window, assigner))

            # Frame para mostrar resultados
            window.tmdb_results_frame = tk.Frame(results_frame, bg=self.colors['bg_card'])
            window.tmdb_results_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            # Mensaje inicial
            initial_msg = tk.Label(window.tmdb_results_frame,
                                 text="👆 Selecciona una serie de la lista izquierda\no busca manualmente arriba",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['text_secondary'], bg=self.colors['bg_card'],
                                 justify='center')
            initial_msg.pack(expand=True)

        except Exception as e:
            print(f"❌ Error creando panel de resultados TMDB: {e}")

    def search_selected_series(self, window, assigner):
        """Buscar serie seleccionada en TMDB"""
        try:
            selected_id = window.selected_series.get()
            if selected_id and selected_id in window.series_data:
                series = window.series_data[selected_id]

                # Actualizar campo de búsqueda con el título de la serie
                window.custom_search_entry.delete(0, tk.END)
                window.custom_search_entry.insert(0, series['title'])

                # Realizar búsqueda
                self.perform_tmdb_search(window, assigner, series['title'], series.get('year'))

        except Exception as e:
            print(f"❌ Error buscando serie seleccionada: {e}")

    def search_custom_term(self, window, assigner):
        """Buscar término personalizado en TMDB"""
        try:
            search_term = window.custom_search_entry.get().strip()
            if not search_term:
                tk.messagebox.showwarning("Búsqueda vacía", "Por favor ingresa un término de búsqueda")
                return

            # Obtener año de la serie seleccionada si hay una
            year = None
            selected_id = window.selected_series.get()
            if selected_id and selected_id in window.series_data:
                year = window.series_data[selected_id].get('year')

            # Realizar búsqueda
            self.perform_tmdb_search(window, assigner, search_term, year)

        except Exception as e:
            print(f"❌ Error en búsqueda personalizada: {e}")

    def perform_tmdb_search(self, window, assigner, search_term, year=None):
        """Realizar búsqueda TMDB y mostrar resultados"""
        try:
            # Limpiar resultados anteriores
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            # Mostrar mensaje de búsqueda
            search_msg = tk.Label(window.tmdb_results_frame,
                                text=f"🔍 Buscando: {search_term}...",
                                font=('Segoe UI', 10),
                                fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            search_msg.pack(pady=20)

            def search_worker():
                try:
                    # Realizar búsqueda
                    results = assigner.search_tmdb_series(search_term, year)

                    # Actualizar UI en hilo principal
                    window.after(0, lambda: self.show_tmdb_results(window, assigner, results, search_term))

                except Exception as e:
                    window.after(0, lambda: self.show_tmdb_error(window, f"Error en búsqueda: {str(e)}"))

            self.root.after(100, search_worker)

        except Exception as e:
            print(f"❌ Error realizando búsqueda TMDB: {e}")

    def show_tmdb_results(self, window, assigner, results, search_term):
        """Mostrar resultados de búsqueda TMDB"""
        try:
            # Limpiar frame
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            if not results:
                no_results_label = tk.Label(window.tmdb_results_frame,
                                          text=f"❌ No se encontraron resultados para:\n'{search_term}'",
                                          font=('Segoe UI', 10),
                                          fg=self.colors['error'], bg=self.colors['bg_card'],
                                          justify='center')
                no_results_label.pack(expand=True)
                return

            # Título de resultados
            results_title = tk.Label(window.tmdb_results_frame,
                                   text=f"📊 {len(results)} resultados encontrados:",
                                   font=('Segoe UI', 10, 'bold'),
                                   fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            results_title.pack(pady=(0, 10))

            # Frame con scroll para resultados
            scroll_frame = tk.Frame(window.tmdb_results_frame, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True)

            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Mostrar cada resultado
            for i, result in enumerate(results[:10], 1):  # Limitar a 10 resultados
                self.create_tmdb_result_item(scrollable_frame, window, assigner, result, i)

        except Exception as e:
            print(f"❌ Error mostrando resultados TMDB: {e}")

    def create_tmdb_result_item(self, parent, window, assigner, result, index):
        """Crear item de resultado TMDB con diseño profesional"""
        try:
            # Frame principal del resultado con mejor diseño
            result_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
            result_frame.pack(fill='x', pady=8, padx=10)

            # Frame interno con padding
            inner_frame = tk.Frame(result_frame, bg='white')
            inner_frame.pack(fill='both', expand=True, padx=15, pady=15)

            # Configurar grid
            inner_frame.columnconfigure(1, weight=1)

            # Información del resultado
            title = result.get('name', 'Sin título')
            year = result.get('first_air_date', '')[:4] if result.get('first_air_date') else 'N/A'
            rating = result.get('vote_average', 0)
            overview = result.get('overview', 'Sin descripción')
            poster_path = result.get('poster_path', '')

            # Frame para la imagen (izquierda)
            image_frame = tk.Frame(inner_frame, bg='white', width=80, height=120)
            image_frame.grid(row=0, column=0, rowspan=3, sticky='nw', padx=(0, 15))
            image_frame.grid_propagate(False)

            # Placeholder para imagen (por ahora un rectángulo)
            if poster_path:
                image_text = "🎬\nPOSTER"
            else:
                image_text = "📺\nSIN\nIMAGEN"

            image_label = tk.Label(image_frame, text=image_text,
                                 bg='#f0f0f0', fg='#666666',
                                 font=('Segoe UI', 8, 'bold'),
                                 justify='center', relief='solid', bd=1)
            image_label.pack(fill='both', expand=True)

            # Título y año (derecha, arriba)
            title_label = tk.Label(inner_frame,
                                 text=f"{index}. {title} ({year})",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg='#2c3e50', bg='white',
                                 anchor='w', justify='left')
            title_label.grid(row=0, column=1, sticky='ew', pady=(0, 5))

            # Rating y géneros (derecha, medio)
            rating_text = f"⭐ {rating:.1f}/10"
            if rating >= 7.0:
                rating_color = '#27ae60'
            elif rating >= 5.0:
                rating_color = '#f39c12'
            else:
                rating_color = '#e74c3c'

            rating_label = tk.Label(inner_frame,
                                  text=rating_text,
                                  font=('Segoe UI', 10, 'bold'),
                                  fg=rating_color, bg='white',
                                  anchor='w')
            rating_label.grid(row=1, column=1, sticky='ew', pady=(0, 8))

            # Descripción (derecha, abajo)
            if len(overview) > 200:
                overview = overview[:200] + "..."

            desc_label = tk.Label(inner_frame,
                                text=overview,
                                font=('Segoe UI', 9),
                                fg='#7f8c8d', bg='white',
                                anchor='nw', justify='left', wraplength=350)
            desc_label.grid(row=2, column=1, sticky='ew', pady=(0, 10))

            # Frame para el botón (centrado)
            button_frame = tk.Frame(inner_frame, bg='white')
            button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

            # Botón de asignación más profesional
            assign_btn = tk.Button(button_frame,
                                 text="✅ Seleccionar este resultado",
                                 command=lambda: self.assign_tmdb_result(window, assigner, result),
                                 bg='#3498db', fg='white',
                                 font=('Segoe UI', 10, 'bold'), bd=0,
                                 activebackground='#2980b9',
                                 relief='flat', cursor='hand2')
            assign_btn.pack(pady=5, padx=20, ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando item de resultado: {e}")

    def assign_tmdb_result(self, window, assigner, tmdb_result):
        """Asignar resultado TMDB a serie seleccionada"""
        try:
            selected_id = window.selected_series.get()
            if not selected_id or selected_id not in window.series_data:
                tk.messagebox.showwarning("Sin selección", "Por favor selecciona una serie de la lista izquierda")
                return

            series = window.series_data[selected_id]
            tmdb_id = tmdb_result['id']
            tmdb_name = tmdb_result.get('name', 'Sin título')

            # Confirmar asignación
            confirm = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Asignar TMDB a esta serie?\n\n"
                f"Serie: {series['title']}\n"
                f"TMDB: {tmdb_name}\n\n"
                f"Esta acción actualizará los metadatos de la serie.",
                icon='question'
            )

            if not confirm:
                return

            # Mostrar progreso
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            progress_label = tk.Label(window.tmdb_results_frame,
                                    text="⏳ Asignando TMDB...\nObteniendo detalles completos...",
                                    font=('Segoe UI', 10),
                                    fg=self.colors['text_primary'], bg=self.colors['bg_card'],
                                    justify='center')
            progress_label.pack(expand=True)

            def assign_worker():
                try:
                    # Obtener detalles completos
                    tmdb_details = assigner.get_tmdb_series_details(tmdb_id)

                    if tmdb_details:
                        # Asignar a la serie
                        if assigner.assign_tmdb_to_series(selected_id, tmdb_details):
                            window.after(0, lambda: self.show_assignment_success(window, series, tmdb_name))
                        else:
                            window.after(0, lambda: self.show_assignment_error(window, "Error guardando en base de datos"))
                    else:
                        window.after(0, lambda: self.show_assignment_error(window, "Error obteniendo detalles de TMDB"))

                except Exception as e:
                    window.after(0, lambda: self.show_assignment_error(window, f"Error: {str(e)}"))

            self.root.after(100, assign_worker)

        except Exception as e:
            print(f"❌ Error asignando resultado TMDB: {e}")
            tk.messagebox.showerror("Error", f"Error asignando TMDB: {str(e)}")

    def show_assignment_success(self, window, series, tmdb_name):
        """Mostrar éxito en asignación"""
        try:
            tk.messagebox.showinfo(
                "Asignación Exitosa",
                f"✅ TMDB asignado exitosamente\n\n"
                f"Serie: {series['title']}\n"
                f"TMDB: {tmdb_name}\n\n"
                f"Los metadatos han sido actualizados."
            )

            # Remover la serie de la lista (ya tiene TMDB)
            if hasattr(window, 'series_data') and series['id'] in window.series_data:
                del window.series_data[series['id']]

            # Limpiar resultados
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            success_label = tk.Label(window.tmdb_results_frame,
                                   text="✅ Serie asignada exitosamente\n\nSelecciona otra serie de la lista",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['success'], bg=self.colors['bg_card'],
                                   justify='center')
            success_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando éxito: {e}")

    def show_assignment_error(self, window, error_msg):
        """Mostrar error en asignación"""
        try:
            tk.messagebox.showerror("Error de Asignación", f"❌ {error_msg}")

            # Limpiar progreso
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            error_label = tk.Label(window.tmdb_results_frame,
                                 text=f"❌ {error_msg}\n\nIntenta nuevamente",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error: {e}")

    def show_tmdb_error(self, window, error_msg):
        """Mostrar error en búsqueda TMDB"""
        try:
            # Limpiar frame
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            error_label = tk.Label(window.tmdb_results_frame,
                                 text=f"❌ {error_msg}",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error TMDB: {e}")

    def close_tmdb_window(self, window, assigner):
        """Cerrar ventana TMDB y limpiar recursos"""
        try:
            assigner.disconnect()
            window.destroy()
        except Exception as e:
            print(f"❌ Error cerrando ventana TMDB: {e}")



    def assign_tmdb_result(self, window, assigner, result):
        """Asignar resultado TMDB a serie seleccionada"""
        try:
            selected_id = window.selected_series.get()
            if not selected_id or selected_id not in window.series_data:
                tk.messagebox.showwarning("Sin selección", "Por favor selecciona una serie de la lista izquierda")
                return

            series = window.series_data[selected_id]
            tmdb_id = result['id']
            tmdb_name = result.get('name', 'Sin título')

            # Confirmar asignación
            confirm = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Asignar TMDB a esta serie?\n\n"
                f"Serie: {series['title']}\n"
                f"TMDB: {tmdb_name}\n\n"
                f"Esta acción actualizará los metadatos de la serie.",
                icon='question'
            )

            if not confirm:
                return

            # Mostrar progreso
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            progress_label = tk.Label(window.tmdb_results_frame,
                                    text="⏳ Asignando TMDB...\nObteniendo detalles completos...",
                                    font=('Segoe UI', 10),
                                    fg=self.colors['text_primary'], bg=self.colors['bg_card'],
                                    justify='center')
            progress_label.pack(expand=True)

            def assign_worker():
                try:
                    # Obtener detalles completos
                    tmdb_details = assigner.get_tmdb_series_details(tmdb_id)

                    if tmdb_details:
                        # Asignar a la serie
                        if assigner.assign_tmdb_to_series(selected_id, tmdb_details):
                            window.after(0, lambda: self.show_assignment_success(window, series, tmdb_name))
                        else:
                            window.after(0, lambda: self.show_assignment_error(window, "Error guardando en base de datos"))
                    else:
                        window.after(0, lambda: self.show_assignment_error(window, "Error obteniendo detalles de TMDB"))

                except Exception as e:
                    window.after(0, lambda: self.show_assignment_error(window, f"Error: {str(e)}"))

            self.root.after(100, assign_worker)

        except Exception as e:
            print(f"❌ Error asignando resultado TMDB: {e}")
            tk.messagebox.showerror("Error", f"Error asignando TMDB: {str(e)}")

    def show_assignment_success(self, window, series, tmdb_name):
        """Mostrar éxito en asignación"""
        try:
            tk.messagebox.showinfo(
                "Asignación Exitosa",
                f"✅ TMDB asignado exitosamente\n\n"
                f"Serie: {series['title']}\n"
                f"TMDB: {tmdb_name}\n\n"
                f"Los metadatos han sido actualizados."
            )

            # Remover la serie de la lista (ya tiene TMDB)
            if hasattr(window, 'series_data') and series['id'] in window.series_data:
                del window.series_data[series['id']]

            # Limpiar resultados
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            success_label = tk.Label(window.tmdb_results_frame,
                                   text="✅ Serie asignada exitosamente\n\nSelecciona otra serie de la lista",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['success'], bg=self.colors['bg_card'],
                                   justify='center')
            success_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando éxito: {e}")

    def show_assignment_error(self, window, error_msg):
        """Mostrar error en asignación"""
        try:
            tk.messagebox.showerror("Error de Asignación", f"❌ {error_msg}")

            # Limpiar progreso
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            error_label = tk.Label(window.tmdb_results_frame,
                                 text=f"❌ {error_msg}\n\nIntenta nuevamente",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error: {e}")

    def show_tmdb_error(self, window, error_msg):
        """Mostrar error en búsqueda TMDB"""
        try:
            # Limpiar frame
            for widget in window.tmdb_results_frame.winfo_children():
                widget.destroy()

            error_label = tk.Label(window.tmdb_results_frame,
                                 text=f"❌ {error_msg}",
                                 font=('Segoe UI', 10),
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error TMDB: {e}")

    def close_tmdb_window(self, window, assigner):
        """Cerrar ventana TMDB y limpiar recursos"""
        try:
            assigner.disconnect()
            window.destroy()
        except Exception as e:
            print(f"❌ Error cerrando ventana TMDB: {e}")

    def assign_series_to_orphans(self):
        """Asignar series a episodios huérfanos"""
        self.update_data_display("🏠 ASIGNANDO SERIES A HUÉRFANOS...", "header")
        self.update_data_display("Procesando episodios huérfanos y creando series...", "info")

        def assign_worker():
            try:
                from orphan_series_assigner import OrphanSeriesAssigner

                assigner = OrphanSeriesAssigner()
                if assigner.connect():
                    # Detectar huérfanos
                    orphans = assigner.find_orphan_episodes()
                    relevant_orphans = orphans['no_relation'] + orphans['no_series']

                    if not relevant_orphans:
                        self.update_data_display("✅ No hay episodios huérfanos que necesiten asignación", "success")
                        return

                    # Agrupar por serie
                    series_groups = assigner.group_orphans_by_series(orphans)

                    if series_groups:
                        self.update_data_display(f"📺 Series detectadas: {len(series_groups)}", "info")

                        # Procesar automáticamente
                        created_series = 0
                        assigned_episodes = 0

                        for series_name, group in series_groups.items():
                            series_id = group['series_id']
                            episodes = group['episodes']

                            # Verificar si existe
                            existing = assigner.db.execute_query(
                                "SELECT id FROM streams_series WHERE id = %s", (series_id,)
                            )

                            if not existing:
                                if assigner.create_missing_series(series_name, series_id, episodes[0]):
                                    created_series += 1
                                    self.update_data_display(f"✅ Serie creada: {series_name}", "success")

                            # Asignar episodios
                            assigned = assigner.assign_episodes_to_series(episodes, series_id, series_name)
                            assigned_episodes += assigned
                            self.update_data_display(f"🔗 Asignados {assigned} episodios a {series_name}", "info")

                        self.update_data_display(f"\n🎉 Procesamiento completado:", "header")
                        self.update_data_display(f"📺 Series creadas: {created_series}", "success")
                        self.update_data_display(f"🔗 Episodios asignados: {assigned_episodes}", "success")
                    else:
                        self.update_data_display("❌ No se pudieron detectar series", "error")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=assign_worker, daemon=True)
        thread.start()

    def open_tmdb_assigner(self):
        """Abrir asignador de TMDB integrado"""
        self.update_data_display("📊 CARGANDO TMDB ASSIGNER...", "header")
        self.update_tmdb_display("📊 TMDB ASSIGNER ACTIVADO", "header")

        def load_tmdb():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Obtener estadísticas
                    stats = assigner.get_tmdb_statistics()

                    self.update_data_display("✅ Conectado a TMDB API", "success")
                    self.update_data_display(f"📊 Total series: {stats['total_series']:,}", "info")
                    self.update_data_display(f"✅ Con TMDB: {stats['with_tmdb']:,}", "success")
                    self.update_data_display(f"❌ Sin TMDB: {stats['without_tmdb']:,}", "warning")
                    self.update_data_display(f"📈 Porcentaje: {stats['tmdb_percentage']:.1f}%", "info")

                    # Obtener series sin TMDB
                    series_without_tmdb = assigner.find_series_without_tmdb()

                    if series_without_tmdb:
                        self.update_tmdb_display(f"📺 SERIES SIN TMDB: {len(series_without_tmdb)}", "warning")

                        for i, series in enumerate(series_without_tmdb[:10], 1):
                            self.update_tmdb_display(f"{i}. {series['title']} ({series['year']}) - {series['episode_count']} eps", "info")

                        if len(series_without_tmdb) > 10:
                            self.update_tmdb_display(f"... y {len(series_without_tmdb) - 10} más", "info")

                        self.update_tmdb_display("\n🤖 Usa 'Asignación Automática' para procesar todas", "info")
                    else:
                        self.update_tmdb_display("🎉 ¡Todas las series tienen TMDB asignado!", "success")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=load_tmdb, daemon=True)
        thread.start()

    def auto_assign_tmdb(self):
        """Asignación automática de TMDB"""
        self.update_data_display("🤖 ASIGNACIÓN AUTOMÁTICA TMDB", "header")
        self.update_tmdb_display("🤖 INICIANDO ASIGNACIÓN AUTOMÁTICA...", "header")

        def auto_assign_worker():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Obtener series sin TMDB
                    series_without_tmdb = assigner.find_series_without_tmdb()

                    if not series_without_tmdb:
                        self.update_data_display("✅ Todas las series ya tienen TMDB asignado", "success")
                        self.update_tmdb_display("🎉 ¡100% de cobertura TMDB!", "success")
                        return

                    self.update_data_display(f"📊 Procesando {len(series_without_tmdb)} series...", "info")

                    # Procesar hasta 10 series por vez para no saturar
                    max_process = min(10, len(series_without_tmdb))

                    assigned_count = 0
                    skipped_count = 0
                    error_count = 0

                    for i, series in enumerate(series_without_tmdb[:max_process], 1):
                        try:
                            series_id = series['id']
                            title = series['title']
                            year = series['year']

                            self.update_data_display(f"{i}/{max_process} - 🔍 {title}", "info")

                            # Buscar en TMDB
                            search_results = assigner.search_tmdb_series(title, year)

                            if search_results:
                                # Tomar el primer resultado
                                best_match = search_results[0]
                                tmdb_id = best_match['id']

                                self.update_tmdb_display(f"✅ {title} → {best_match['name']} (ID: {tmdb_id})", "success")

                                # Obtener detalles y asignar
                                tmdb_details = assigner.get_tmdb_series_details(tmdb_id)
                                if tmdb_details and assigner.assign_tmdb_to_series(series_id, tmdb_details):
                                    assigned_count += 1
                                    self.update_tmdb_display(f"   📊 Asignado: Rating {tmdb_details.get('vote_average', 0):.1f}", "info")
                                else:
                                    error_count += 1
                                    self.update_tmdb_display(f"   ❌ Error asignando", "error")
                            else:
                                skipped_count += 1
                                self.update_tmdb_display(f"⚠️ {title} - No encontrado en TMDB", "warning")

                            # Pausa para rate limit
                            import time
                            time.sleep(0.3)

                        except Exception as e:
                            error_count += 1
                            self.update_tmdb_display(f"❌ Error procesando {title}: {str(e)}", "error")

                    # Mostrar resumen
                    self.update_data_display(f"\n🎉 ASIGNACIÓN COMPLETADA:", "header")
                    self.update_data_display(f"✅ Asignadas: {assigned_count}", "success")
                    self.update_data_display(f"⚠️ Omitidas: {skipped_count}", "warning")
                    self.update_data_display(f"❌ Errores: {error_count}", "error")

                    self.update_tmdb_display(f"\n📊 RESUMEN FINAL:", "header")
                    self.update_tmdb_display(f"✅ {assigned_count} series con TMDB asignado", "success")
                    if skipped_count > 0:
                        self.update_tmdb_display(f"⚠️ {skipped_count} series requieren asignación manual", "warning")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=auto_assign_worker, daemon=True)
        thread.start()

    def manual_assign_tmdb(self):
        """Asignación manual de TMDB con cuadro de diálogo"""
        self.update_data_display("🔍 ASIGNACIÓN MANUAL TMDB", "header")
        self.update_tmdb_display("🔍 SELECCIONA UNA SERIE PARA BUSCAR", "header")

        def load_series():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    series_without_tmdb = assigner.find_series_without_tmdb()

                    if not series_without_tmdb:
                        self.update_data_display("✅ Todas las series ya tienen TMDB", "success")
                        return

                    # Mostrar series disponibles
                    self.update_data_display("📺 SERIES SIN TMDB:", "info")
                    for i, series in enumerate(series_without_tmdb, 1):
                        self.update_data_display(f"{i}. {series['title']} ({series['year']}) - {series['episode_count']} eps", "info")

                    self.update_tmdb_display("👆 Haz clic en una serie de la lista para buscar en TMDB", "info")

                    # Guardar series para uso posterior
                    self.current_series_list = series_without_tmdb

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=load_series, daemon=True)
        thread.start()

    def on_data_click(self, event):
        """Manejar clic en el área de datos"""
        try:
            # Obtener la línea clickeada
            index = self.data_text.index(tk.CURRENT)
            line_start = self.data_text.index(f"{index} linestart")
            line_end = self.data_text.index(f"{index} lineend")
            line_text = self.data_text.get(line_start, line_end)

            # Verificar si es una línea de serie (formato: "1. Título (año) - X eps")
            import re
            series_match = re.match(r'^(\d+)\.\s+(.+?)\s+\((\d+)\)\s+-\s+(\d+)\s+eps', line_text)

            if series_match and hasattr(self, 'current_series_list'):
                series_index = int(series_match.group(1)) - 1

                if 0 <= series_index < len(self.current_series_list):
                    selected_series = self.current_series_list[series_index]
                    self.show_tmdb_search_dialog(selected_series)

        except Exception as e:
            print(f"Error en clic: {e}")

    def show_tmdb_search_dialog(self, series):
        """Mostrar cuadro de diálogo para búsqueda de TMDB"""
        # Crear ventana de diálogo
        dialog = tk.Toplevel(self.root)
        dialog.title(f"🔍 Buscar TMDB: {series['title']}")
        dialog.geometry("800x600")
        dialog.configure(bg=self.colors['bg_primary'])
        dialog.transient(self.root)
        dialog.grab_set()

        # Frame principal
        main_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Título
        title_label = tk.Label(main_frame, text=f"🔍 Buscar TMDB para: {series['title']}",
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.grid(row=0, column=0, pady=(0, 20), sticky='w')

        # Frame de búsqueda
        search_frame = tk.Frame(main_frame, bg=self.colors['bg_card'], relief='solid', bd=1)
        search_frame.grid(row=1, column=0, sticky='ew', pady=(0, 20))
        search_frame.columnconfigure(1, weight=1)

        tk.Label(search_frame, text="Término de búsqueda:",
                font=('Segoe UI', 10, 'bold'),
                fg=self.colors['text_primary'], bg=self.colors['bg_card']).grid(row=0, column=0, padx=15, pady=15, sticky='w')

        search_var = tk.StringVar(value=series['title'])
        search_entry = tk.Entry(search_frame, textvariable=search_var,
                               font=('Segoe UI', 10), width=40,
                               bg=self.colors['bg_tertiary'], fg=self.colors['text_primary'])
        search_entry.grid(row=0, column=1, padx=15, pady=15, sticky='ew')

        search_btn = tk.Button(search_frame, text="🔍 Buscar",
                              command=lambda: self.search_tmdb_for_series_professional(series, search_var.get(), dialog),
                              bg=self.colors['accent'], fg='white',
                              font=('Segoe UI', 10, 'bold'), bd=0)
        search_btn.grid(row=0, column=2, padx=15, pady=15)

        # Frame de resultados con diseño profesional
        results_frame = tk.Frame(main_frame, bg='#f8f9fa', relief='solid', bd=1)
        results_frame.grid(row=2, column=0, sticky='nsew')
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Canvas con scroll para resultados profesionales
        canvas = tk.Canvas(results_frame, bg='#f8f9fa', highlightthickness=0)
        scrollbar = tk.Scrollbar(results_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#f8f9fa')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Mensaje inicial
        initial_msg = tk.Label(scrollable_frame,
                              text="🔍 Haz clic en 'Buscar' para encontrar coincidencias en TMDB",
                              font=('Segoe UI', 11),
                              fg='#6c757d', bg='#f8f9fa',
                              pady=50)
        initial_msg.pack(expand=True)

        # Botones de acción
        action_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        action_frame.grid(row=3, column=0, pady=(20, 0))

        tk.Button(action_frame, text="❌ Cancelar",
                 command=dialog.destroy,
                 bg=self.colors['error'], fg='white',
                 font=('Segoe UI', 10, 'bold'), bd=0).pack(side='right', padx=(10, 0), ipady=8, ipadx=15)

        # Guardar referencias para uso posterior
        dialog.series = series
        dialog.scrollable_frame = scrollable_frame
        dialog.canvas = canvas

        # Realizar búsqueda inicial
        self.search_tmdb_for_series_professional(series, series['title'], dialog)

    def search_tmdb_for_series_professional(self, series, search_term, dialog):
        """Buscar serie en TMDB y mostrar resultados con diseño profesional"""
        def search_worker():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Limpiar resultados anteriores
                    for widget in dialog.scrollable_frame.winfo_children():
                        widget.destroy()

                    # Mostrar mensaje de búsqueda
                    search_msg = tk.Label(dialog.scrollable_frame,
                                        text=f"🔍 Buscando '{search_term}' en TMDB...",
                                        font=('Segoe UI', 11),
                                        fg='#007bff', bg='#f8f9fa',
                                        pady=30)
                    search_msg.pack()
                    dialog.canvas.update()

                    # Buscar en TMDB
                    search_results = assigner.search_tmdb_series(search_term, series.get('year'))

                    # Limpiar mensaje de búsqueda
                    search_msg.destroy()

                    if search_results:
                        # Título de resultados
                        results_title = tk.Label(dialog.scrollable_frame,
                                               text=f"✅ {len(search_results)} resultados encontrados:",
                                               font=('Segoe UI', 12, 'bold'),
                                               fg='#28a745', bg='#f8f9fa',
                                               pady=(10, 20))
                        results_title.pack()

                        # Mostrar cada resultado con diseño profesional
                        for i, result in enumerate(search_results, 1):
                            self.create_tmdb_result_item_dialog(dialog.scrollable_frame, dialog, assigner, result, i, series)

                    else:
                        # No hay resultados
                        no_results = tk.Label(dialog.scrollable_frame,
                                            text=f"⚠️ No se encontraron resultados para '{search_term}'\nIntenta con un término diferente",
                                            font=('Segoe UI', 11),
                                            fg='#dc3545', bg='#f8f9fa',
                                            justify='center', pady=50)
                        no_results.pack(expand=True)

                    assigner.disconnect()
                else:
                    # Error de conexión
                    error_msg = tk.Label(dialog.scrollable_frame,
                                       text="❌ Error conectando a la base de datos",
                                       font=('Segoe UI', 11),
                                       fg='#dc3545', bg='#f8f9fa',
                                       pady=50)
                    error_msg.pack(expand=True)

            except Exception as e:
                # Error general
                error_msg = tk.Label(dialog.scrollable_frame,
                                   text=f"❌ Error: {str(e)}",
                                   font=('Segoe UI', 11),
                                   fg='#dc3545', bg='#f8f9fa',
                                   pady=50)
                error_msg.pack(expand=True)

        thread = threading.Thread(target=search_worker, daemon=True)
        thread.start()

    def create_tmdb_result_item_dialog(self, parent, dialog, assigner, result, index, series):
        """Crear item de resultado TMDB para ventana de diálogo"""
        try:
            # Frame principal del resultado con diseño profesional
            result_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
            result_frame.pack(fill='x', pady=8, padx=15)

            # Frame interno con padding
            inner_frame = tk.Frame(result_frame, bg='white')
            inner_frame.pack(fill='both', expand=True, padx=15, pady=15)

            # Configurar grid
            inner_frame.columnconfigure(1, weight=1)

            # Información del resultado
            title = result.get('name', 'Sin título')
            year = result.get('first_air_date', '')[:4] if result.get('first_air_date') else 'N/A'
            rating = result.get('vote_average', 0)
            overview = result.get('overview', 'Sin descripción')
            poster_path = result.get('poster_path', '')
            tmdb_id = result.get('id')

            # Frame para la imagen (izquierda)
            image_frame = tk.Frame(inner_frame, bg='white', width=80, height=120)
            image_frame.grid(row=0, column=0, rowspan=3, sticky='nw', padx=(0, 15))
            image_frame.grid_propagate(False)

            # Placeholder para imagen
            if poster_path:
                image_text = "🎬\nPOSTER"
            else:
                image_text = "📺\nSIN\nIMAGEN"

            image_label = tk.Label(image_frame, text=image_text,
                                 bg='#f0f0f0', fg='#666666',
                                 font=('Segoe UI', 8, 'bold'),
                                 justify='center', relief='solid', bd=1)
            image_label.pack(fill='both', expand=True)

            # Título y año (derecha, arriba)
            title_label = tk.Label(inner_frame,
                                 text=f"{index}. {title} ({year})",
                                 font=('Segoe UI', 12, 'bold'),
                                 fg='#2c3e50', bg='white',
                                 anchor='w', justify='left')
            title_label.grid(row=0, column=1, sticky='ew', pady=(0, 5))

            # Rating y ID (derecha, medio)
            rating_text = f"⭐ {rating:.1f}/10 • ID: {tmdb_id}"
            if rating >= 7.0:
                rating_color = '#27ae60'
            elif rating >= 5.0:
                rating_color = '#f39c12'
            else:
                rating_color = '#e74c3c'

            rating_label = tk.Label(inner_frame,
                                  text=rating_text,
                                  font=('Segoe UI', 10, 'bold'),
                                  fg=rating_color, bg='white',
                                  anchor='w')
            rating_label.grid(row=1, column=1, sticky='ew', pady=(0, 8))

            # Descripción (derecha, abajo)
            if len(overview) > 180:
                overview = overview[:180] + "..."

            desc_label = tk.Label(inner_frame,
                                text=overview,
                                font=('Segoe UI', 9),
                                fg='#7f8c8d', bg='white',
                                anchor='nw', justify='left', wraplength=300)
            desc_label.grid(row=2, column=1, sticky='ew', pady=(0, 10))

            # Frame para el botón (centrado)
            button_frame = tk.Frame(inner_frame, bg='white')
            button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

            # Botón de asignación profesional
            assign_btn = tk.Button(button_frame,
                                 text="✅ Asignar este TMDB",
                                 command=lambda: self.assign_tmdb_to_series_dialog(series, tmdb_id, dialog),
                                 bg='#28a745', fg='white',
                                 font=('Segoe UI', 10, 'bold'), bd=0,
                                 activebackground='#218838',
                                 relief='flat', cursor='hand2')
            assign_btn.pack(pady=5, padx=20, ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando item de resultado: {e}")

    def assign_tmdb_to_series_dialog(self, series, tmdb_id, dialog):
        """Asignar TMDB a serie desde ventana de diálogo"""
        try:
            from tmdb_assigner import TMDBAssigner

            assigner = TMDBAssigner()
            if assigner.connect():
                success = assigner.assign_tmdb_to_series(series['id'], tmdb_id)

                if success:
                    tk.messagebox.showinfo("Éxito", f"✅ TMDB {tmdb_id} asignado correctamente a '{series['title']}'")
                    dialog.destroy()
                    # Actualizar la lista en el panel principal
                    self.manual_assign_tmdb()
                else:
                    tk.messagebox.showerror("Error", "❌ No se pudo asignar el TMDB")

                assigner.disconnect()
            else:
                tk.messagebox.showerror("Error", "❌ Error conectando a la base de datos")

        except Exception as e:
            tk.messagebox.showerror("Error", f"❌ Error: {str(e)}")

    def search_tmdb_for_series(self, series, search_term, results_frame, dialog=None):
        """Buscar serie en TMDB y mostrar resultados"""
        def search_worker():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Limpiar resultados anteriores
                    if dialog:
                        dialog.results_text.delete(1.0, tk.END)
                        dialog.results_text.insert(tk.END, f"🔍 Buscando '{search_term}' en TMDB...\n", "info")
                        dialog.results_text.update()

                    # Buscar en TMDB
                    search_results = assigner.search_tmdb_series(search_term, series.get('year'))

                    if search_results:
                        if dialog:
                            dialog.results_text.insert(tk.END, f"\n✅ Encontrados {len(search_results)} resultados (búsqueda multiidioma):\n\n", "success")

                            for i, result in enumerate(search_results, 1):
                                title = result.get('name', 'Sin título')
                                year = result.get('first_air_date', '')[:4] if result.get('first_air_date') else 'N/A'
                                rating = result.get('vote_average', 0)
                                overview = result.get('overview', 'Sin descripción')[:100] + "..." if len(result.get('overview', '')) > 100 else result.get('overview', 'Sin descripción')
                                tmdb_id = result.get('id')

                                dialog.results_text.insert(tk.END, f"{i}. {title} ({year}) - Rating: {rating:.1f}\n", "header")
                                dialog.results_text.insert(tk.END, f"   ID: {tmdb_id}\n", "info")
                                dialog.results_text.insert(tk.END, f"   {overview}\n", "info")

                                # Crear botón de asignación
                                assign_btn = tk.Button(results_frame, text=f"✅ Asignar #{i}",
                                                     command=lambda tid=tmdb_id, lang=search_lang, idx=i: self.assign_tmdb_to_series(series, tid, dialog, lang),
                                                     bg=self.colors['success'], fg='white',
                                                     font=('Segoe UI', 9, 'bold'), bd=0)

                                # Insertar botón en el texto
                                dialog.results_text.window_create(tk.END, window=assign_btn)
                                dialog.results_text.insert(tk.END, "\n\n")

                            dialog.results_text.see(tk.END)
                    else:
                        if dialog:
                            dialog.results_text.insert(tk.END, f"\n⚠️ No se encontraron resultados para '{search_term}'\n", "info")
                            dialog.results_text.insert(tk.END, "Intenta con un término de búsqueda diferente.\n", "info")

                    assigner.disconnect()
                else:
                    if dialog:
                        dialog.results_text.insert(tk.END, "❌ Error conectando a la base de datos\n", "error")

            except Exception as e:
                if dialog:
                    dialog.results_text.insert(tk.END, f"❌ Error: {str(e)}\n", "error")

        thread = threading.Thread(target=search_worker, daemon=True)
        thread.start()

    # ========================================
    # MÉTODOS PARA PELÍCULAS TMDB
    # ========================================

    def load_movies_without_tmdb(self):
        """Cargar películas sin TMDB ID"""
        self.update_data_display("🎬 CARGANDO PELÍCULAS SIN TMDB...", "header")
        self.update_tmdb_display("🔍 Buscando películas sin metadatos...", "header")

        def load_movies():
            try:
                from movie_tmdb_assigner import MovieTMDBAssigner

                assigner = MovieTMDBAssigner()
                if assigner.connect():
                    movies_without_tmdb = assigner.find_movies_without_tmdb()

                    if not movies_without_tmdb:
                        self.update_data_display("✅ Todas las películas ya tienen TMDB", "success")
                        self.update_tmdb_display("🎉 ¡100% de cobertura TMDB en películas!", "success")
                        return

                    # Mostrar películas disponibles
                    self.update_data_display("🎬 PELÍCULAS SIN TMDB:", "info")
                    for i, movie in enumerate(movies_without_tmdb, 1):
                        year_info = f" ({movie['year']})" if movie['year'] else ""
                        self.update_data_display(f"{i}. {movie['clean_title']}{year_info}", "info")

                    self.update_tmdb_display("👆 Haz clic en una película de la lista para buscar en TMDB", "info")

                    # Guardar películas para uso posterior
                    self.current_movies_list = movies_without_tmdb

                    # Mostrar estadísticas
                    stats = assigner.get_tmdb_statistics()
                    self.update_tmdb_display(f"\n📊 ESTADÍSTICAS:", "header")
                    self.update_tmdb_display(f"🎬 Total películas: {stats['total_movies']}", "info")
                    self.update_tmdb_display(f"✅ Con TMDB: {stats['with_tmdb']}", "success")
                    self.update_tmdb_display(f"❌ Sin TMDB: {stats['without_tmdb']}", "error")
                    self.update_tmdb_display(f"📈 Cobertura: {stats['tmdb_percentage']:.1f}%", "info")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=load_movies, daemon=True)
        thread.start()

    def auto_assign_tmdb_movies(self):
        """Asignación automática de TMDB para películas"""
        self.update_data_display("🤖 ASIGNACIÓN AUTOMÁTICA TMDB PELÍCULAS", "header")
        self.update_tmdb_display("🤖 INICIANDO ASIGNACIÓN AUTOMÁTICA...", "header")

        def auto_assign_worker():
            try:
                from movie_tmdb_assigner import MovieTMDBAssigner

                assigner = MovieTMDBAssigner()
                if assigner.connect():
                    # Obtener películas sin TMDB
                    movies_without_tmdb = assigner.find_movies_without_tmdb()

                    if not movies_without_tmdb:
                        self.update_data_display("✅ Todas las películas ya tienen TMDB asignado", "success")
                        self.update_tmdb_display("🎉 ¡100% de cobertura TMDB!", "success")
                        return

                    self.update_data_display(f"🎬 Procesando {len(movies_without_tmdb)} películas...", "info")
                    self.update_tmdb_display("⚠️ Esto puede tomar varios minutos...", "warning")

                    # Ejecutar asignación automática
                    results = assigner.auto_assign_tmdb(movies_without_tmdb, max_requests=20)

                    # Mostrar resultados
                    self.update_data_display("🎯 RESULTADOS ASIGNACIÓN AUTOMÁTICA:", "header")
                    self.update_data_display(f"✅ Asignadas: {results['assigned']}", "success")
                    self.update_data_display(f"⚠️ No encontradas: {results['skipped']}", "warning")
                    self.update_data_display(f"❌ Errores: {results['errors']}", "error")
                    self.update_data_display(f"📊 Total procesadas: {results['total_processed']}", "info")

                    # Calcular porcentaje de éxito
                    success_rate = (results['assigned'] / results['total_processed'] * 100) if results['total_processed'] > 0 else 0
                    self.update_tmdb_display(f"📈 Tasa de éxito: {success_rate:.1f}%", "success")

                    # Actualizar estadísticas finales
                    stats = assigner.get_tmdb_statistics()
                    self.update_tmdb_display(f"\n📊 ESTADÍSTICAS ACTUALIZADAS:", "header")
                    self.update_tmdb_display(f"🎬 Total películas: {stats['total_movies']}", "info")
                    self.update_tmdb_display(f"✅ Con TMDB: {stats['with_tmdb']}", "success")
                    self.update_tmdb_display(f"❌ Sin TMDB: {stats['without_tmdb']}", "error")
                    self.update_tmdb_display(f"📈 Cobertura: {stats['tmdb_percentage']:.1f}%", "info")

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")
                self.update_tmdb_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=auto_assign_worker, daemon=True)
        thread.start()

    def manual_assign_tmdb_movies(self):
        """Asignación manual de TMDB para películas"""
        self.update_data_display("🔍 ASIGNACIÓN MANUAL TMDB PELÍCULAS", "header")
        self.update_tmdb_display("🔍 Cargando películas disponibles...", "header")

        def load_movies():
            try:
                from movie_tmdb_assigner import MovieTMDBAssigner

                assigner = MovieTMDBAssigner()
                if assigner.connect():
                    movies_without_tmdb = assigner.find_movies_without_tmdb()

                    if not movies_without_tmdb:
                        self.update_data_display("✅ Todas las películas ya tienen TMDB", "success")
                        return

                    # Mostrar películas disponibles
                    self.update_data_display("🎬 PELÍCULAS SIN TMDB:", "info")
                    for i, movie in enumerate(movies_without_tmdb, 1):
                        year_info = f" ({movie['year']})" if movie['year'] else ""
                        self.update_data_display(f"{i}. {movie['clean_title']}{year_info}", "info")

                    self.update_tmdb_display("👆 Haz clic en una película de la lista para buscar en TMDB", "info")

                    # Guardar películas para uso posterior
                    self.current_movies_list = movies_without_tmdb

                    # Configurar click handler para películas
                    self.data_text.bind("<Button-1>", self.on_movie_click)

                    assigner.disconnect()
                else:
                    self.update_data_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_data_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=load_movies, daemon=True)
        thread.start()

    def on_movie_click(self, event):
        """Manejar click en película para búsqueda TMDB"""
        try:
            # Obtener línea clickeada
            index = self.data_text.index(tk.CURRENT)
            line_start = index.split('.')[0] + '.0'
            line_end = index.split('.')[0] + '.end'
            line_text = self.data_text.get(line_start, line_end)

            # Extraer número de película
            import re
            match = re.match(r'^(\d+)\.\s+(.+)', line_text.strip())
            if not match:
                return

            movie_index = int(match.group(1)) - 1
            movie_title = match.group(2)

            # Verificar que tenemos la lista de películas
            if not hasattr(self, 'current_movies_list') or movie_index >= len(self.current_movies_list):
                return

            movie = self.current_movies_list[movie_index]
            self.search_tmdb_for_movie(movie)

        except Exception as e:
            print(f"Error en click de película: {e}")

    def search_tmdb_for_movie(self, movie):
        """Buscar película específica en TMDB"""
        self.update_tmdb_display(f"🔍 BUSCANDO: {movie['clean_title']}", "header")

        year_info = f" ({movie['year']})" if movie['year'] else ""
        self.update_tmdb_display(f"🎬 Película: {movie['clean_title']}{year_info}", "info")

        def search_worker():
            try:
                from movie_tmdb_assigner import MovieTMDBAssigner

                assigner = MovieTMDBAssigner()
                if assigner.connect():
                    # Buscar en TMDB
                    results = assigner.search_tmdb_movies(movie['clean_title'], movie['year'])

                    if results:
                        self.update_tmdb_display(f"✅ Encontrados {len(results)} resultados:", "success")

                        for i, result in enumerate(results[:10], 1):
                            title = result.get('title', 'Sin título')
                            year = result.get('release_date', '')[:4] if result.get('release_date') else 'N/A'
                            rating = result.get('vote_average', 0)
                            tmdb_id = result.get('id')

                            result_text = f"{i}. {title} ({year}) ⭐{rating} [ID: {tmdb_id}]"
                            self.update_tmdb_display(result_text, "info")

                        self.update_tmdb_display("\n🎯 Para asignar, usa el diálogo de asignación manual", "warning")

                        # Abrir diálogo de asignación
                        self.root.after(0, lambda: self.open_movie_assignment_dialog(movie, results))
                    else:
                        self.update_tmdb_display("❌ No se encontraron resultados", "error")

                    assigner.disconnect()
                else:
                    self.update_tmdb_display("❌ Error conectando a la base de datos", "error")

            except Exception as e:
                self.update_tmdb_display(f"❌ Error: {str(e)}", "error")

        thread = threading.Thread(target=search_worker, daemon=True)
        thread.start()

    def open_movie_assignment_dialog(self, movie, tmdb_results):
        """Abrir diálogo mejorado para asignar TMDB a película con carátulas"""
        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            # Crear ventana de diálogo más grande para mostrar carátulas
            dialog = tk.Toplevel(self.root)
            dialog.title(f"🎬 Asignar TMDB - {movie['clean_title']}")
            dialog.geometry("1200x800")
            dialog.configure(bg=self.colors['bg_primary'])
            dialog.transient(self.root)
            dialog.grab_set()

            # Centrar ventana
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (1200 // 2)
            y = (dialog.winfo_screenheight() // 2) - (800 // 2)
            dialog.geometry(f"1200x800+{x}+{y}")

            # Header con información de la película
            header_frame = tk.Frame(dialog, bg=self.colors['bg_secondary'], height=80)
            header_frame.pack(fill='x', padx=20, pady=(20, 10))
            header_frame.pack_propagate(False)

            year_info = f" ({movie['year']})" if movie['year'] else ""
            title_label = tk.Label(header_frame, text=f"🎬 {movie['clean_title']}{year_info}",
                                 font=('Segoe UI', 16, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
            title_label.pack(pady=15)

            subtitle_label = tk.Label(header_frame, text=f"📊 {len(tmdb_results)} resultados encontrados en TMDB",
                                    font=('Segoe UI', 11),
                                    fg=self.colors['text_secondary'], bg=self.colors['bg_secondary'])
            subtitle_label.pack()

            # Frame principal con scroll para los resultados
            main_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=10)

            # Canvas y scrollbar para scroll vertical
            canvas = tk.Canvas(main_frame, bg=self.colors['bg_primary'], highlightthickness=0)
            scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_primary'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Variable para almacenar la selección
            selected_movie = tk.IntVar(value=-1)

            # Crear cards para cada resultado con carátulas
            self.create_movie_result_cards(scrollable_frame, tmdb_results, selected_movie)

            # Frame para botones
            buttons_frame = tk.Frame(dialog, bg=self.colors['bg_primary'])
            buttons_frame.pack(fill='x', padx=20, pady=20)

            # Botón Asignar
            def assign_selected():
                selected_index = selected_movie.get()
                if selected_index == -1:
                    messagebox.showwarning("Selección requerida", "Por favor selecciona un resultado de TMDB")
                    return

                selected_result = tmdb_results[selected_index]
                tmdb_id = selected_result['id']

                # Confirmar asignación
                result_title = selected_result.get('title', 'Sin título')
                result_year = selected_result.get('release_date', '')[:4] if selected_result.get('release_date') else 'N/A'

                confirm = messagebox.askyesno(
                    "Confirmar Asignación",
                    f"¿Asignar TMDB a la película?\n\n"
                    f"🎬 Película: {movie['clean_title']}\n"
                    f"📊 TMDB: {result_title} ({result_year})\n"
                    f"🆔 ID: {tmdb_id}\n"
                    f"⭐ Rating: {selected_result.get('vote_average', 0):.1f}"
                )

                if confirm:
                    self.assign_tmdb_to_movie(movie, tmdb_id, dialog)

            assign_btn = tk.Button(buttons_frame, text="✅ Asignar Seleccionado",
                                 command=assign_selected,
                                 bg=self.colors['success'], fg='white',
                                 font=('Segoe UI', 12, 'bold'), bd=0,
                                 activebackground='#00cc66')
            assign_btn.pack(side='left', padx=(0, 15), ipady=10, ipadx=20)

            # Botón Cancelar
            def cancel():
                dialog.destroy()

            cancel_btn = tk.Button(buttons_frame, text="❌ Cancelar",
                                 command=cancel,
                                 bg=self.colors['error'], fg='white',
                                 font=('Segoe UI', 12, 'bold'), bd=0,
                                 activebackground='#cc3333')
            cancel_btn.pack(side='right', ipady=10, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando diálogo de asignación: {e}")

    def create_movie_result_cards(self, parent, tmdb_results, selected_movie):
        """Crear cards visuales para los resultados de TMDB"""
        try:
            import tkinter as tk

            # Usar solo librerías estándar - sin PIL ni requests
            PIL_AVAILABLE = False

            # Configurar grid para las cards (2 columnas)
            parent.columnconfigure(0, weight=1)
            parent.columnconfigure(1, weight=1)

            for i, result in enumerate(tmdb_results):
                row = i // 2
                col = i % 2

                # Frame principal de la card
                card_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='solid', bd=1)
                card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
                card_frame.columnconfigure(1, weight=1)

                # Radio button para selección
                radio = tk.Radiobutton(card_frame, variable=selected_movie, value=i,
                                     bg=self.colors['bg_card'], fg=self.colors['accent'],
                                     selectcolor=self.colors['bg_tertiary'],
                                     activebackground=self.colors['bg_card'])
                radio.grid(row=0, column=0, rowspan=4, padx=5, pady=5, sticky='n')

                # Frame para información visual (sin carátulas externas)
                poster_frame = tk.Frame(card_frame, bg=self.colors['bg_secondary'], width=120, height=180)
                poster_frame.grid(row=0, column=1, rowspan=4, padx=10, pady=10, sticky='n')
                poster_frame.pack_propagate(False)

                # Información visual de la película
                title = result.get('title', 'Sin título')[:15]
                year = result.get('release_date', '')[:4] if result.get('release_date') else 'N/A'
                rating = result.get('vote_average', 0)

                poster_text = f"🎬\n{title}\n({year})\n⭐{rating:.1f}"
                poster_label = tk.Label(poster_frame, text=poster_text,
                                      font=('Segoe UI', 9),
                                      bg=self.colors['bg_secondary'],
                                      fg=self.colors['text_secondary'],
                                      justify='center')
                poster_label.pack(expand=True)

                # Información de la película
                info_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
                info_frame.grid(row=0, column=2, columnspan=2, padx=15, pady=10, sticky='ew')
                info_frame.columnconfigure(0, weight=1)

                # Título
                title = result.get('title', 'Sin título')
                title_label = tk.Label(info_frame, text=title,
                                     font=('Segoe UI', 12, 'bold'),
                                     fg=self.colors['text_primary'], bg=self.colors['bg_card'],
                                     wraplength=300, justify='left')
                title_label.grid(row=0, column=0, sticky='w', pady=(0, 5))

                # Año y rating
                year = result.get('release_date', '')[:4] if result.get('release_date') else 'N/A'
                rating = result.get('vote_average', 0)
                year_rating_label = tk.Label(info_frame, text=f"📅 {year}  ⭐ {rating:.1f}",
                                           font=('Segoe UI', 10),
                                           fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
                year_rating_label.grid(row=1, column=0, sticky='w', pady=(0, 5))

                # TMDB ID
                tmdb_id = result.get('id')
                id_label = tk.Label(info_frame, text=f"🆔 TMDB ID: {tmdb_id}",
                                  font=('Segoe UI', 9),
                                  fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
                id_label.grid(row=2, column=0, sticky='w', pady=(0, 10))

                # Descripción
                overview = result.get('overview', 'Sin descripción disponible')
                if len(overview) > 200:
                    overview = overview[:200] + "..."

                desc_label = tk.Label(info_frame, text=overview,
                                    font=('Segoe UI', 9),
                                    fg=self.colors['text_secondary'], bg=self.colors['bg_card'],
                                    wraplength=300, justify='left')
                desc_label.grid(row=3, column=0, sticky='ew', pady=(0, 5))

                # No necesitamos cargar carátulas externas - ya está la info visual

        except Exception as e:
            print(f"❌ Error creando cards de películas: {e}")



    def assign_tmdb_to_movie(self, movie, tmdb_id, dialog, preferred_language='es-ES'):
        """Asignar TMDB ID a una película específica"""
        def assign_worker():
            try:
                from movie_tmdb_assigner import MovieTMDBAssigner

                assigner = MovieTMDBAssigner()
                if assigner.connect():
                    # Obtener detalles de TMDB usando el idioma preferido
                    tmdb_details = assigner.get_tmdb_movie_details(tmdb_id, preferred_language)

                    if tmdb_details:
                        # Asignar a la película
                        success = assigner.assign_tmdb_to_movie(movie['id'], tmdb_details)

                        if success:
                            dialog.after(0, lambda: self.show_movie_assignment_success(dialog, movie, tmdb_details))
                        else:
                            dialog.after(0, lambda: self.show_assignment_error(dialog, "Error asignando a la base de datos"))
                    else:
                        dialog.after(0, lambda: self.show_assignment_error(dialog, "Error obteniendo detalles de TMDB"))

                    assigner.disconnect()
                else:
                    dialog.after(0, lambda: self.show_assignment_error(dialog, "No se pudo conectar a la base de datos"))

            except Exception as e:
                dialog.after(0, lambda: self.show_assignment_error(dialog, f"Error: {str(e)}"))

        import threading
        thread = threading.Thread(target=assign_worker, daemon=True)
        thread.start()

    def show_movie_assignment_success(self, dialog, movie, tmdb_details):
        """Mostrar éxito en asignación de película"""
        try:
            from tkinter import messagebox

            title = tmdb_details.get('title', 'Sin título')
            year = tmdb_details.get('release_date', '')[:4] if tmdb_details.get('release_date') else 'N/A'
            rating = tmdb_details.get('vote_average', 0)

            messagebox.showinfo(
                "✅ Asignación Exitosa",
                f"TMDB asignado exitosamente:\n\n"
                f"🎬 Película: {movie['clean_title']}\n"
                f"📊 TMDB: {title} ({year})\n"
                f"⭐ Rating: {rating}\n"
                f"🆔 TMDB ID: {tmdb_details.get('id')}"
            )

            dialog.destroy()

            # Actualizar lista de películas sin TMDB
            if hasattr(self, 'current_movies_list'):
                self.current_movies_list = [m for m in self.current_movies_list if m['id'] != movie['id']]

            # Actualizar display
            self.update_tmdb_display(f"✅ {movie['clean_title']} asignado exitosamente", "success")

        except Exception as e:
            print(f"Error mostrando éxito: {e}")

    def assign_tmdb_to_series(self, series, tmdb_id, dialog, preferred_language='es-ES'):
        """Asignar TMDB ID a una serie específica"""
        def assign_worker():
            try:
                from tmdb_assigner import TMDBAssigner

                assigner = TMDBAssigner()
                if assigner.connect():
                    # Obtener detalles de TMDB usando el idioma preferido
                    tmdb_details = assigner.get_tmdb_series_details(tmdb_id, preferred_language)

                    if tmdb_details:
                        # Asignar a la serie
                        success = assigner.assign_tmdb_to_series(series['id'], tmdb_details)

                        if success:
                            # Actualizar displays
                            details_lang = tmdb_details.get('details_language', preferred_language)
                            self.update_data_display(f"✅ TMDB asignado a {series['title']}", "success")
                            self.update_data_display(f"   📊 TMDB ID: {tmdb_id}", "info")
                            self.update_data_display(f"   📺 Título: {tmdb_details['name']} [{details_lang}]", "info")
                            self.update_data_display(f"   ⭐ Rating: {tmdb_details.get('vote_average', 0):.1f}", "info")

                            self.update_tmdb_display(f"✅ {series['title']} → {tmdb_details['name']} [{details_lang}]", "success")
                            self.update_tmdb_display(f"   📊 TMDB ID: {tmdb_id} asignado exitosamente", "success")

                            # Cerrar diálogo
                            dialog.destroy()

                            # Actualizar lista de series sin TMDB
                            if hasattr(self, 'current_series_list'):
                                self.current_series_list = [s for s in self.current_series_list if s['id'] != series['id']]

                            messagebox.showinfo("Éxito", f"TMDB asignado exitosamente a '{series['title']}'")
                        else:
                            messagebox.showerror("Error", "No se pudo asignar TMDB a la base de datos")
                    else:
                        messagebox.showerror("Error", "No se pudieron obtener detalles de TMDB")

                    assigner.disconnect()
                else:
                    messagebox.showerror("Error", "No se pudo conectar a la base de datos")

            except Exception as e:
                messagebox.showerror("Error", f"Error durante asignación: {str(e)}")

        thread = threading.Thread(target=assign_worker, daemon=True)
        thread.start()

    def add_orphan_action_buttons(self):
        """Agregar botones de acción para episodios huérfanos"""
        if hasattr(self, 'data_text') and hasattr(self, 'current_orphans'):
            # Agregar separador
            self.update_data_display("\n" + "="*50, "info")
            self.update_data_display("🛠️ ACCIONES DISPONIBLES:", "header")

            # Frame para botones (insertado en el texto)
            button_frame = tk.Frame(self.data_text, bg=self.colors['bg_tertiary'])

            # Botón: Ver todos los huérfanos
            view_all_btn = tk.Button(button_frame, text="📋 Ver Todos",
                                   command=self.view_all_orphans,
                                   bg=self.colors['accent'], fg='white',
                                   font=('Segoe UI', 9, 'bold'), bd=0)
            view_all_btn.pack(side='left', padx=5, pady=5)

            # Botón: Asignar automáticamente
            auto_assign_btn = tk.Button(button_frame, text="🏠 Asignar Auto",
                                      command=self.assign_series_to_orphans,
                                      bg=self.colors['success'], fg='white',
                                      font=('Segoe UI', 9, 'bold'), bd=0)
            auto_assign_btn.pack(side='left', padx=5, pady=5)

            # Botón: Selección manual
            manual_btn = tk.Button(button_frame, text="🔍 Selección Manual",
                                 command=self.manual_orphan_selection,
                                 bg=self.colors['warning'], fg='white',
                                 font=('Segoe UI', 9, 'bold'), bd=0)
            manual_btn.pack(side='left', padx=5, pady=5)

            # Insertar frame de botones en el texto
            self.data_text.config(state='normal')
            self.data_text.window_create(tk.END, window=button_frame)
            self.data_text.insert(tk.END, "\n")
            self.data_text.config(state='disabled')

    def view_all_orphans(self):
        """Ver todos los episodios huérfanos en detalle"""
        if not hasattr(self, 'current_orphans'):
            return

        # Crear ventana para mostrar todos los huérfanos
        orphan_window = tk.Toplevel(self.root)
        orphan_window.title("📋 Todos los Episodios Huérfanos")
        orphan_window.geometry("900x700")
        orphan_window.configure(bg=self.colors['bg_primary'])

        # Frame principal
        main_frame = tk.Frame(orphan_window, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Título
        title_label = tk.Label(main_frame, text="📋 Episodios Huérfanos Detallados",
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.grid(row=0, column=0, pady=(0, 20), sticky='w')

        # Área de texto con todos los detalles
        text_area = scrolledtext.ScrolledText(main_frame,
                                            bg=self.colors['bg_tertiary'],
                                            fg=self.colors['text_primary'],
                                            font=('Consolas', 9),
                                            wrap=tk.WORD)
        text_area.grid(row=1, column=0, sticky='nsew')

        # Configurar tags
        text_area.tag_configure("header", font=('Consolas', 10, 'bold'), foreground=self.colors['accent'])
        text_area.tag_configure("warning", foreground=self.colors['warning'])
        text_area.tag_configure("info", foreground=self.colors['text_secondary'])

        # Mostrar todos los huérfanos
        for category, episodes in self.current_orphans.items():
            if episodes:
                text_area.insert(tk.END, f"\n🔍 {category.upper()}: {len(episodes)} episodios\n", "header")
                text_area.insert(tk.END, "="*60 + "\n", "info")

                for i, episode in enumerate(episodes, 1):
                    text_area.insert(tk.END, f"{i:3d}. {episode['stream_display_name']}\n", "warning")
                    text_area.insert(tk.END, f"     ID: {episode['id']}, Servidor: {episode.get('server_id', 'N/A')}\n", "info")
                    if episode.get('stream_source'):
                        source = episode['stream_source'][:80] + "..." if len(episode['stream_source']) > 80 else episode['stream_source']
                        text_area.insert(tk.END, f"     URL: {source}\n", "info")
                    text_area.insert(tk.END, "\n")

        # Botón cerrar
        close_btn = tk.Button(main_frame, text="❌ Cerrar",
                             command=orphan_window.destroy,
                             bg=self.colors['error'], fg='white',
                             font=('Segoe UI', 10, 'bold'), bd=0)
        close_btn.grid(row=2, column=0, pady=(20, 0))

    def manual_orphan_selection(self):
        """Selección manual de episodios huérfanos para procesar"""
        if not hasattr(self, 'current_orphans'):
            return

        # Crear ventana de selección
        selection_window = tk.Toplevel(self.root)
        selection_window.title("🔍 Selección Manual de Huérfanos")
        selection_window.geometry("1000x700")
        selection_window.configure(bg=self.colors['bg_primary'])

        # Frame principal
        main_frame = tk.Frame(selection_window, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Título
        title_label = tk.Label(main_frame, text="🔍 Selecciona Episodios para Procesar",
                              font=('Segoe UI', 14, 'bold'),
                              fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
        title_label.grid(row=0, column=0, pady=(0, 20), sticky='w')

        # Frame con checkboxes
        checkbox_frame = tk.Frame(main_frame, bg=self.colors['bg_card'], relief='solid', bd=1)
        checkbox_frame.grid(row=1, column=0, sticky='nsew')

        # Scrollable frame para checkboxes
        canvas = tk.Canvas(checkbox_frame, bg=self.colors['bg_card'])
        scrollbar = ttk.Scrollbar(checkbox_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Variables para checkboxes
        checkbox_vars = []
        episode_data = []

        # Crear checkboxes para cada episodio
        row = 0
        for category, episodes in self.current_orphans.items():
            if episodes:
                # Título de categoría
                category_label = tk.Label(scrollable_frame, text=f"🔍 {category.upper()}: {len(episodes)} episodios",
                                        font=('Segoe UI', 11, 'bold'),
                                        fg=self.colors['warning'], bg=self.colors['bg_card'])
                category_label.grid(row=row, column=0, columnspan=2, sticky='w', padx=10, pady=(10, 5))
                row += 1

                for episode in episodes:
                    var = tk.BooleanVar()
                    checkbox_vars.append(var)
                    episode_data.append(episode)

                    checkbox = tk.Checkbutton(scrollable_frame,
                                            text=f"{episode['stream_display_name'][:70]}...",
                                            variable=var,
                                            bg=self.colors['bg_card'], fg=self.colors['text_primary'],
                                            selectcolor=self.colors['bg_tertiary'],
                                            font=('Segoe UI', 9))
                    checkbox.grid(row=row, column=0, sticky='w', padx=30, pady=2)

                    # Info adicional
                    info_label = tk.Label(scrollable_frame, text=f"ID: {episode['id']}",
                                        font=('Segoe UI', 8),
                                        fg=self.colors['text_secondary'], bg=self.colors['bg_card'])
                    info_label.grid(row=row, column=1, sticky='w', padx=10, pady=2)

                    row += 1

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Botones de acción
        action_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        action_frame.grid(row=2, column=0, pady=(20, 0))

        # Botón: Seleccionar todos
        select_all_btn = tk.Button(action_frame, text="✅ Seleccionar Todos",
                                  command=lambda: [var.set(True) for var in checkbox_vars],
                                  bg=self.colors['success'], fg='white',
                                  font=('Segoe UI', 10, 'bold'), bd=0)
        select_all_btn.pack(side='left', padx=5, ipady=8, ipadx=15)

        # Botón: Deseleccionar todos
        deselect_all_btn = tk.Button(action_frame, text="❌ Deseleccionar Todos",
                                    command=lambda: [var.set(False) for var in checkbox_vars],
                                    bg=self.colors['error'], fg='white',
                                    font=('Segoe UI', 10, 'bold'), bd=0)
        deselect_all_btn.pack(side='left', padx=5, ipady=8, ipadx=15)

        # Botón: Procesar seleccionados
        process_btn = tk.Button(action_frame, text="🏠 Procesar Seleccionados",
                               command=lambda: self.process_selected_orphans(checkbox_vars, episode_data, selection_window),
                               bg=self.colors['accent'], fg='white',
                               font=('Segoe UI', 10, 'bold'), bd=0)
        process_btn.pack(side='left', padx=5, ipady=8, ipadx=15)

        # Botón: Cancelar
        cancel_btn = tk.Button(action_frame, text="❌ Cancelar",
                              command=selection_window.destroy,
                              bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                              font=('Segoe UI', 10, 'bold'), bd=0)
        cancel_btn.pack(side='left', padx=5, ipady=8, ipadx=15)

    def process_selected_orphans(self, checkbox_vars, episode_data, window):
        """Procesar episodios huérfanos seleccionados"""
        # Obtener episodios seleccionados
        selected_episodes = []
        for i, var in enumerate(checkbox_vars):
            if var.get():
                selected_episodes.append(episode_data[i])

        if not selected_episodes:
            messagebox.showwarning("Advertencia", "No has seleccionado ningún episodio")
            return

        # Confirmar procesamiento
        result = messagebox.askyesno("Confirmar Procesamiento",
                                   f"¿Procesar {len(selected_episodes)} episodios seleccionados?\n\n"
                                   "Esto creará series automáticamente y asignará los episodios.")

        if result:
            window.destroy()

            # Procesar episodios seleccionados
            self.update_data_display(f"\n🔄 PROCESANDO {len(selected_episodes)} EPISODIOS SELECCIONADOS...", "header")

            def process_worker():
                try:
                    from orphan_series_assigner import OrphanSeriesAssigner

                    assigner = OrphanSeriesAssigner()
                    if assigner.connect():
                        # Simular estructura de huérfanos con solo los seleccionados
                        selected_orphans = {'selected': selected_episodes}

                        # Agrupar por serie
                        series_groups = assigner.group_orphans_by_series(selected_orphans)

                        if series_groups:
                            created_series = 0
                            assigned_episodes = 0

                            for series_name, group in series_groups.items():
                                series_id = group['series_id']
                                episodes = group['episodes']

                                # Verificar si existe
                                existing = assigner.db.execute_query(
                                    "SELECT id FROM streams_series WHERE id = %s", (series_id,)
                                )

                                if not existing:
                                    if assigner.create_missing_series(series_name, series_id, episodes[0]):
                                        created_series += 1
                                        self.update_data_display(f"✅ Serie creada: {series_name}", "success")

                                # Asignar episodios
                                assigned = assigner.assign_episodes_to_series(episodes, series_id, series_name)
                                assigned_episodes += assigned
                                self.update_data_display(f"🔗 Asignados {assigned} episodios a {series_name}", "info")

                            self.update_data_display(f"\n🎉 PROCESAMIENTO COMPLETADO:", "header")
                            self.update_data_display(f"📺 Series creadas: {created_series}", "success")
                            self.update_data_display(f"🔗 Episodios asignados: {assigned_episodes}", "success")
                        else:
                            self.update_data_display("❌ No se pudieron detectar series", "error")

                        assigner.disconnect()
                    else:
                        self.update_data_display("❌ Error conectando a la base de datos", "error")

                except Exception as e:
                    self.update_data_display(f"❌ Error: {str(e)}", "error")

            thread = threading.Thread(target=process_worker, daemon=True)
            thread.start()

    def run(self):
        """Ejecutar la aplicación"""
        self.root.mainloop()

    def open_content_comparator(self):
        """Abrir ventana del comparador de contenido"""
        try:
            from content_comparator_window import ContentComparatorWindow

            # Crear ventana del comparador
            comparator_window = ContentComparatorWindow(self.root)
            comparator_window.show()

        except ImportError:
            # Si no existe el módulo, crear la ventana aquí mismo
            self.create_content_comparator_window()
        except Exception as e:
            messagebox.showerror("Error", f"Error abriendo comparador de contenido:\n{str(e)}")

    def create_content_comparator_window(self):
        """Crear ventana del comparador de contenido estilo NVIDIA + Gemini"""
        # Crear ventana principal
        comparator_window = tk.Toplevel(self.root)
        comparator_window.title("🔄 Comparador de Contenido - NVIDIA + Gemini")
        comparator_window.geometry("1400x900")
        comparator_window.configure(bg=self.colors['bg_primary'])
        comparator_window.transient(self.root)

        # Colores estilo NVIDIA + Gemini
        gemini_colors = {
            'bg_primary': '#0d1117',        # Negro GitHub
            'bg_secondary': '#161b22',      # Gris muy oscuro
            'bg_card': '#1c2128',           # Cards oscuros
            'bg_tertiary': '#21262d',       # Gris oscuro
            'border': '#76b900',            # Verde NVIDIA
            'border_hover': '#5a8a00',      # Verde NVIDIA hover
            'accent': '#00d4ff',            # Celeste Gemini
            'accent_hover': '#00b8e6',      # Celeste más oscuro
            'text_primary': '#f0f6fc',      # Blanco suave
            'text_secondary': '#8b949e',    # Gris claro
            'success': '#238636',           # Verde éxito
            'warning': '#d29922',           # Amarillo
            'error': '#f85149'              # Rojo
        }

        # Frame principal con borde azul celeste
        main_frame = tk.Frame(comparator_window, bg=gemini_colors['bg_primary'],
                             relief='solid', bd=2, highlightbackground=gemini_colors['border'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Configurar grid
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Header con estilo Gemini
        self.create_comparator_header(main_frame, gemini_colors)

        # Panel de carga de archivo
        self.create_file_upload_panel(main_frame, gemini_colors)

        # Paneles de comparación
        self.create_comparison_panels(main_frame, gemini_colors)

        # Panel de acciones
        self.create_actions_panel(main_frame, gemini_colors)

        # Guardar referencias
        comparator_window.gemini_colors = gemini_colors
        self.current_comparator_window = comparator_window

        # Inicializar caché de BD
        self.db_cache = None
        self.cache_timestamp = None

    def create_comparator_header(self, parent, colors):
        """Crear header del comparador estilo NVIDIA + Gemini"""
        header_frame = tk.Frame(parent, bg=colors['bg_secondary'],
                               relief='solid', bd=2, highlightbackground=colors['border'])
        header_frame.grid(row=0, column=0, columnspan=2, sticky='ew', padx=5, pady=5)

        # Contenedor del título
        title_container = tk.Frame(header_frame, bg=colors['bg_secondary'])
        title_container.pack(fill='x', pady=15)

        # Título principal estilo NVIDIA
        title_label = tk.Label(title_container, text="🔄 COMPARADOR DE CONTENIDO",
                              font=('Segoe UI', 20, 'bold'),
                              fg=colors['border'], bg=colors['bg_secondary'])
        title_label.pack()

        # Línea decorativa verde
        title_line = tk.Frame(title_container, height=3, bg=colors['border'])
        title_line.pack(fill='x', pady=(8, 0))

        # Subtítulo estilo Gemini
        subtitle_label = tk.Label(header_frame, text="Compara tu M3U con la base de datos actual",
                                 font=('Segoe UI', 12),
                                 fg=colors['accent'], bg=colors['bg_secondary'])
        subtitle_label.pack(pady=(0, 15))

    def create_file_upload_panel(self, parent, colors):
        """Crear panel de carga de archivo estilo NVIDIA + Gemini"""
        upload_frame = tk.Frame(parent, bg=colors['bg_card'],
                               relief='solid', bd=2, highlightbackground=colors['border'])
        upload_frame.grid(row=1, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
        upload_frame.columnconfigure(1, weight=1)

        # Contenedor del título
        title_container = tk.Frame(upload_frame, bg=colors['bg_card'])
        title_container.grid(row=0, column=0, columnspan=3, pady=15)

        # Título del panel estilo NVIDIA
        upload_title = tk.Label(title_container, text="📁 CARGAR ARCHIVO M3U",
                               font=('Segoe UI', 14, 'bold'),
                               fg=colors['border'], bg=colors['bg_card'])
        upload_title.pack()

        # Línea decorativa verde
        title_line = tk.Frame(title_container, height=2, bg=colors['border'])
        title_line.pack(fill='x', pady=(5, 0))

        # Campo de archivo
        tk.Label(upload_frame, text="Archivo M3U:",
                font=('Segoe UI', 10, 'bold'),
                fg=colors['text_primary'], bg=colors['bg_card']).grid(row=1, column=0, padx=15, pady=10, sticky='w')

        self.file_path_var = tk.StringVar()
        file_entry = tk.Entry(upload_frame, textvariable=self.file_path_var,
                             font=('Segoe UI', 10), width=60,
                             bg=colors['bg_secondary'], fg=colors['text_primary'],
                             insertbackground=colors['text_primary'],
                             relief='solid', bd=1, highlightbackground=colors['border'])
        file_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        # Botón examinar estilo NVIDIA
        browse_btn = tk.Button(upload_frame, text="📂 Examinar",
                              command=self.browse_m3u_file,
                              bg=colors['border'], fg=colors['bg_primary'],
                              font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                              activebackground=colors['border_hover'],
                              highlightbackground=colors['border'])
        browse_btn.grid(row=1, column=2, padx=15, pady=10)

        # Botón analizar estilo Gemini
        self.analyze_btn = tk.Button(upload_frame, text="🔍 Analizar M3U",
                                    command=self.analyze_m3u_content,
                                    bg=colors['accent'], fg=colors['bg_primary'],
                                    font=('Segoe UI', 12, 'bold'), bd=2, relief='solid',
                                    activebackground=colors['accent_hover'],
                                    highlightbackground=colors['accent'],
                                    state='disabled')
        self.analyze_btn.grid(row=2, column=0, columnspan=3, pady=15)

        # Botón precargar caché
        self.preload_btn = tk.Button(upload_frame, text="⚡ Precargar Caché BD",
                                    command=self.preload_db_cache,
                                    bg=colors['bg_card'], fg=colors['text_primary'],
                                    font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                                    activebackground=colors['bg_tertiary'],
                                    highlightbackground=colors['border'])
        self.preload_btn.grid(row=3, column=0, columnspan=3, pady=(0, 10))

        # Checkbox para modo inteligente
        self.smart_mode_var = tk.BooleanVar(value=False)
        smart_mode_frame = tk.Frame(upload_frame, bg=colors['bg_card'])
        smart_mode_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))

        self.smart_mode_check = tk.Checkbutton(smart_mode_frame,
                                             text="🧠 Modo Inteligente (TMDB)",
                                             variable=self.smart_mode_var,
                                             bg=colors['bg_card'], fg=colors['text_primary'],
                                             font=('Segoe UI', 10),
                                             activebackground=colors['bg_card'],
                                             activeforeground=colors['accent'],
                                             selectcolor=colors['bg_secondary'])
        self.smart_mode_check.pack()

        # Label explicativo
        smart_info_label = tk.Label(smart_mode_frame,
                                  text="Usa TMDB ID para comparación más precisa",
                                  font=('Segoe UI', 8),
                                  bg=colors['bg_card'], fg=colors['text_secondary'])
        smart_info_label.pack()

        # Botón corregir URLs
        self.fix_urls_btn = tk.Button(upload_frame, text="🔧 Corregir URLs TVPro",
                                     command=self.fix_tvpro_urls_with_confirmation,
                                     bg='#e74c3c', fg='white',
                                     font=('Segoe UI', 9), bd=0,
                                     activebackground='#c0392b')
        self.fix_urls_btn.grid(row=5, column=0, columnspan=3, pady=(0, 10))

        # Barra de progreso
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(upload_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.grid(row=6, column=0, columnspan=3, pady=(0, 15))

        # Label de estado
        self.status_label = tk.Label(upload_frame, text="Selecciona un archivo M3U para comenzar",
                                    font=('Segoe UI', 9),
                                    fg=colors['text_secondary'], bg=colors['bg_card'])
        self.status_label.grid(row=7, column=0, columnspan=3, pady=(0, 15))

    def create_comparison_panels(self, parent, colors):
        """Crear paneles de comparación lado a lado estilo NVIDIA + Gemini"""
        # Panel izquierdo - M3U cargado
        left_panel = tk.Frame(parent, bg=colors['bg_card'],
                             relief='solid', bd=2, highlightbackground=colors['border'])
        left_panel.grid(row=2, column=0, sticky='nsew', padx=(5, 2), pady=5)
        left_panel.columnconfigure(0, weight=1)
        left_panel.rowconfigure(1, weight=1)

        # Contenedor del título izquierdo
        left_title_container = tk.Frame(left_panel, bg=colors['bg_card'])
        left_title_container.grid(row=0, column=0, pady=15, sticky='ew')

        # Título panel izquierdo estilo NVIDIA
        left_title = tk.Label(left_title_container, text="📁 M3U CARGADO",
                             font=('Segoe UI', 14, 'bold'),
                             fg=colors['border'], bg=colors['bg_card'])
        left_title.pack()

        # Línea decorativa verde
        left_line = tk.Frame(left_title_container, height=2, bg=colors['border'])
        left_line.pack(fill='x', pady=(5, 0))

        # Área de contenido M3U
        self.m3u_content_frame = tk.Frame(left_panel, bg=colors['bg_card'])
        self.m3u_content_frame.grid(row=1, column=0, sticky='nsew', padx=15, pady=(0, 15))
        self.m3u_content_frame.columnconfigure(0, weight=1)
        self.m3u_content_frame.rowconfigure(0, weight=1)

        # Área de texto para M3U
        self.m3u_text = scrolledtext.ScrolledText(self.m3u_content_frame,
                                                 bg=colors['bg_secondary'],
                                                 fg=colors['text_primary'],
                                                 font=('Consolas', 9),
                                                 wrap=tk.WORD,
                                                 insertbackground=colors['text_primary'],
                                                 selectbackground=colors['accent'],
                                                 relief='solid', bd=1,
                                                 highlightbackground=colors['border'])
        self.m3u_text.grid(row=0, column=0, sticky='nsew')

        # Configurar tags para colores
        self.m3u_text.tag_configure("series", font=('Consolas', 10, 'bold'), foreground=colors['border'])
        self.m3u_text.tag_configure("season", foreground=colors['text_primary'])
        self.m3u_text.tag_configure("episode", foreground=colors['text_secondary'])
        self.m3u_text.tag_configure("new", foreground=colors['success'])

        # Panel derecho - Base de datos actual
        right_panel = tk.Frame(parent, bg=colors['bg_card'],
                              relief='solid', bd=2, highlightbackground=colors['border'])
        right_panel.grid(row=2, column=1, sticky='nsew', padx=(2, 5), pady=5)
        right_panel.columnconfigure(0, weight=1)
        right_panel.rowconfigure(1, weight=1)

        # Contenedor del título derecho
        right_title_container = tk.Frame(right_panel, bg=colors['bg_card'])
        right_title_container.grid(row=0, column=0, pady=15, sticky='ew')

        # Título panel derecho estilo Gemini
        right_title = tk.Label(right_title_container, text="💾 BASE DE DATOS ACTUAL",
                              font=('Segoe UI', 14, 'bold'),
                              fg=colors['accent'], bg=colors['bg_card'])
        right_title.pack()

        # Línea decorativa celeste
        right_line = tk.Frame(right_title_container, height=2, bg=colors['accent'])
        right_line.pack(fill='x', pady=(5, 0))

        # Área de contenido BD
        self.db_content_frame = tk.Frame(right_panel, bg=colors['bg_card'])
        self.db_content_frame.grid(row=1, column=0, sticky='nsew', padx=15, pady=(0, 15))
        self.db_content_frame.columnconfigure(0, weight=1)
        self.db_content_frame.rowconfigure(0, weight=1)

        # Área de texto para BD
        self.db_text = scrolledtext.ScrolledText(self.db_content_frame,
                                                bg=colors['bg_secondary'],
                                                fg=colors['text_primary'],
                                                font=('Consolas', 9),
                                                wrap=tk.WORD,
                                                insertbackground=colors['text_primary'],
                                                selectbackground=colors['accent'],
                                                relief='solid', bd=1,
                                                highlightbackground=colors['border'])
        self.db_text.grid(row=0, column=0, sticky='nsew')

        # Configurar tags para colores
        self.db_text.tag_configure("series", font=('Consolas', 10, 'bold'), foreground=colors['border'])
        self.db_text.tag_configure("season", foreground=colors['text_primary'])
        self.db_text.tag_configure("episode", foreground=colors['text_secondary'])
        self.db_text.tag_configure("missing", foreground=colors['warning'])
        self.db_text.tag_configure("complete", foreground=colors['success'])

        # Mensajes iniciales
        self.m3u_text.insert(tk.END, "📁 Carga un archivo M3U para ver su contenido aquí\n\n", "series")
        self.m3u_text.insert(tk.END, "El análisis mostrará:\n", "episode")
        self.m3u_text.insert(tk.END, "📺 Series encontradas\n", "series")
        self.m3u_text.insert(tk.END, "📋 Temporadas por serie\n", "season")
        self.m3u_text.insert(tk.END, "🎬 Episodios por temporada\n", "episode")
        self.m3u_text.config(state='disabled')

        self.db_text.insert(tk.END, "💾 Contenido actual de tu base de datos\n\n", "series")
        self.db_text.insert(tk.END, "Se mostrará automáticamente al cargar el M3U:\n", "episode")
        self.db_text.insert(tk.END, "✅ Series que ya tienes\n", "complete")
        self.db_text.insert(tk.END, "⚠️ Series con episodios faltantes\n", "missing")
        self.db_text.insert(tk.END, "❌ Series que no tienes\n", "missing")
        self.db_text.config(state='disabled')

    def create_actions_panel(self, parent, colors):
        """Crear panel de acciones detectadas"""
        actions_frame = tk.Frame(parent, bg=colors['bg_card'],
                                relief='solid', bd=2, highlightbackground=colors['border'])
        actions_frame.grid(row=3, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
        actions_frame.columnconfigure(1, weight=1)

        # Título del panel
        actions_title = tk.Label(actions_frame, text="⚡ ACCIONES DETECTADAS",
                                font=('Segoe UI', 12, 'bold'),
                                fg=colors['border'], bg=colors['bg_card'])
        actions_title.grid(row=0, column=0, columnspan=3, pady=15)

        # Área de resumen de acciones
        self.actions_text = scrolledtext.ScrolledText(actions_frame, height=6,
                                                     bg=colors['bg_secondary'],
                                                     fg=colors['text_primary'],
                                                     font=('Consolas', 9),
                                                     wrap=tk.WORD,
                                                     insertbackground=colors['text_primary'],
                                                     selectbackground=colors['accent'],
                                                     relief='solid', bd=1,
                                                     highlightbackground=colors['border'])
        self.actions_text.grid(row=1, column=0, columnspan=3, sticky='ew', padx=15, pady=(0, 15))

        # Configurar tags
        self.actions_text.tag_configure("new_series", foreground=colors['success'])
        self.actions_text.tag_configure("missing_episodes", foreground=colors['warning'])
        self.actions_text.tag_configure("new_season", foreground=colors['border'])
        self.actions_text.tag_configure("summary", font=('Consolas', 10, 'bold'), foreground=colors['text_primary'])

        # Botones de acción
        button_frame = tk.Frame(actions_frame, bg=colors['bg_card'])
        button_frame.grid(row=2, column=0, columnspan=3, pady=(0, 15))

        # Botón confirmar todo estilo NVIDIA
        self.confirm_all_btn = tk.Button(button_frame, text="✅ Confirmar Todo",
                                        command=self.confirm_all_updates,
                                        bg=colors['success'], fg=colors['text_primary'],
                                        font=('Segoe UI', 12, 'bold'), bd=2, relief='solid',
                                        activebackground='#1e6b2e',
                                        highlightbackground=colors['success'],
                                        state='disabled')
        self.confirm_all_btn.pack(side='left', padx=10, ipady=10, ipadx=25)

        # Botón revisar estilo Gemini
        self.review_btn = tk.Button(button_frame, text="🔍 Revisar Selección",
                                   command=self.review_selection,
                                   bg=colors['warning'], fg=colors['bg_primary'],
                                   font=('Segoe UI', 12, 'bold'), bd=2, relief='solid',
                                   activebackground='#b8730a',
                                   highlightbackground=colors['warning'],
                                   state='disabled')
        self.review_btn.pack(side='left', padx=10, ipady=10, ipadx=25)

        # Botón cancelar estilo NVIDIA
        cancel_btn = tk.Button(button_frame, text="❌ Cancelar",
                              command=self.close_comparator,
                              bg=colors['error'], fg=colors['text_primary'],
                              font=('Segoe UI', 12, 'bold'), bd=2, relief='solid',
                              activebackground='#d42c20',
                              highlightbackground=colors['error'])
        cancel_btn.pack(side='left', padx=10, ipady=10, ipadx=25)

        # Mensaje inicial
        self.actions_text.insert(tk.END, "🔄 Esperando análisis del archivo M3U...\n\n", "summary")
        self.actions_text.insert(tk.END, "Una vez cargado el archivo, aquí aparecerán:\n", "summary")
        self.actions_text.insert(tk.END, "🆕 Series nuevas para agregar\n", "new_series")
        self.actions_text.insert(tk.END, "⚠️ Episodios faltantes para actualizar\n", "missing_episodes")
        self.actions_text.insert(tk.END, "📺 Temporadas nuevas para importar\n", "new_season")
        self.actions_text.config(state='disabled')

    def browse_m3u_file(self):
        """Examinar archivo M3U"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo M3U",
            filetypes=[("Archivos M3U", "*.m3u"), ("Todos los archivos", "*.*")]
        )

        if file_path:
            self.file_path_var.set(file_path)
            self.analyze_btn.config(state='normal')
            self.status_label.config(text=f"Archivo seleccionado: {file_path.split('/')[-1]}")

    def close_comparator(self):
        """Cerrar ventana del comparador"""
        if hasattr(self, 'current_comparator_window'):
            self.current_comparator_window.destroy()

    def analyze_m3u_content(self):
        """Analizar contenido del M3U y comparar con BD"""
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showwarning("Advertencia", "Selecciona un archivo M3U primero")
            return

        # Deshabilitar botón durante análisis
        self.analyze_btn.config(state='disabled')
        self.status_label.config(text="🔍 Analizando archivo M3U...")
        self.progress_var.set(0)

        def analyze_worker():
            try:
                # Importar el analizador de M3U
                from robust_m3u_importer import RobustM3UImporter

                # Crear instancia del importador (solo para análisis)
                importer = RobustM3UImporter()

                # Paso 1: Leer y parsear M3U (20%)
                self.status_label.config(text="📖 Leyendo archivo M3U...")
                self.progress_var.set(20)

                m3u_data = importer.parse_m3u_file_only(file_path)

                if not m3u_data:
                    self.status_label.config(text="❌ Error: No se pudo leer el archivo M3U")
                    self.analyze_btn.config(state='normal')
                    return

                # Paso 2: Analizar estructura de series (30%)
                self.status_label.config(text="📊 Analizando estructura de series...")
                self.progress_var.set(30)

                m3u_series_structure = self.analyze_m3u_series_structure(m3u_data)

                # Paso 3: Obtener/actualizar caché de BD (50%)
                self.status_label.config(text="💾 Cargando caché de base de datos...")
                self.progress_var.set(50)

                db_complete_structure = self.get_or_update_db_cache()

                # Paso 4: Comparar estructuras (80%)
                if self.smart_mode_var.get():
                    # Modo inteligente con TMDB
                    self.status_label.config(text="🧠 Comparando contenido (modo inteligente TMDB)...")
                    self.progress_var.set(70)

                    # Obtener datos TMDB para series del M3U
                    m3u_series_with_tmdb = self.enrich_m3u_with_tmdb(m3u_series_structure)
                    self.progress_var.set(80)

                    # Obtener estructura BD con TMDB
                    db_structure_with_tmdb = self.get_db_structure_with_tmdb()

                    # Comparar usando TMDB
                    comparison_results = self.compare_series_structures_by_tmdb(m3u_series_with_tmdb, db_structure_with_tmdb)

                    # Mostrar resultados modo inteligente
                    self.display_comparison_results_smart(m3u_series_with_tmdb, db_structure_with_tmdb, comparison_results)
                else:
                    # Modo normal optimizado
                    self.status_label.config(text="🔄 Comparando contenido (optimizado)...")
                    comparison_results = self.compare_series_structures_optimized(m3u_series_structure, db_complete_structure)

                    # Mostrar resultados modo normal
                    self.display_comparison_results_optimized(m3u_series_structure, db_complete_structure, comparison_results)

                # Paso 5: Mostrar resultados (100%)
                self.status_label.config(text="✅ Análisis completado")
                self.progress_var.set(100)

                # Habilitar botones de acción
                self.confirm_all_btn.config(state='normal')
                self.review_btn.config(state='normal')

            except Exception as e:
                self.status_label.config(text=f"❌ Error durante análisis: {str(e)}")
                messagebox.showerror("Error", f"Error analizando M3U:\n{str(e)}")
            finally:
                self.analyze_btn.config(state='normal')

        # Ejecutar en hilo separado
        thread = threading.Thread(target=analyze_worker, daemon=True)
        thread.start()

    def analyze_m3u_series_structure(self, m3u_data):
        """Analizar estructura de series del M3U"""
        series_structure = {}

        for entry in m3u_data:
            # Los datos ya vienen parseados del robust importer
            series_name = entry.get('series_name')
            if series_name:
                season_num = entry.get('season_number', 1)
                episode_num = entry.get('episode_number', 1)

                # Crear estructura simplificada para mostrar
                if series_name not in series_structure:
                    series_structure[series_name] = {
                        'seasons': {},
                        'total_episodes': 0
                    }

                if season_num not in series_structure[series_name]['seasons']:
                    series_structure[series_name]['seasons'][season_num] = []

                series_structure[series_name]['seasons'][season_num].append({
                    'episode_num': episode_num,
                    'title': entry.get('title', ''),
                    'url': entry.get('url', ''),
                    'original_entry': entry
                })

                series_structure[series_name]['total_episodes'] += 1

        return series_structure

    def extract_series_info_from_entry(self, entry):
        """Extraer información de serie de una entrada M3U"""
        try:
            # Usar la lógica más robusta del m3u_series_importer
            from m3u_series_importer import M3USeriesImporter

            importer = M3USeriesImporter()
            title = entry.get('title', '')

            # Usar parse_series_info que maneja múltiples patrones
            series_data = importer.parse_series_info(title)

            if series_data and series_data.get('series_name'):
                # Solo considerar como serie si tiene un nombre válido
                series_name = series_data['series_name'].strip()
                if len(series_name) > 2:  # Filtrar nombres muy cortos
                    return {
                        'series_name': series_name,
                        'season_num': series_data['season_number'],
                        'episode_num': series_data['episode_number'],
                        'is_series': True
                    }

            return None

        except Exception as e:
            print(f"Error extrayendo info de serie: {e}")
            return None

    def enrich_m3u_series_with_tmdb(self, m3u_series_structure):
        """Enriquecer series del M3U con información TMDB"""
        try:
            from tmdb_assigner import TMDBAssigner

            tmdb_assigner = TMDBAssigner()
            if not tmdb_assigner.connect():
                print("⚠️ No se pudo conectar a TMDB, usando comparación por nombre")
                return self.add_fallback_tmdb_info(m3u_series_structure)

            enriched_structure = {}
            total_series = len(m3u_series_structure)

            print(f"🔍 Buscando TMDB para {total_series} series...")

            for i, (series_name, seasons) in enumerate(m3u_series_structure.items(), 1):
                try:
                    print(f"   {i}/{total_series} - Buscando: {series_name}")

                    # Buscar en TMDB con múltiples idiomas
                    search_results = tmdb_assigner.search_tmdb_series(series_name)

                    tmdb_info = {
                        'original_name': series_name,
                        'tmdb_id': None,
                        'tmdb_name': series_name,
                        'tmdb_year': None,
                        'tmdb_rating': 0
                    }

                    if search_results:
                        # Tomar el mejor resultado
                        best_match = search_results[0]
                        tmdb_info.update({
                            'tmdb_id': best_match.get('id'),
                            'tmdb_name': best_match.get('name', series_name),
                            'tmdb_year': best_match.get('first_air_date', '')[:4] if best_match.get('first_air_date') else None,
                            'tmdb_rating': best_match.get('vote_average', 0)
                        })

                        print(f"      ✅ TMDB ID: {tmdb_info['tmdb_id']} - {tmdb_info['tmdb_name']}")
                    else:
                        print(f"      ❌ No encontrado en TMDB")

                    # Agregar información TMDB a la estructura
                    enriched_structure[series_name] = {
                        'seasons': seasons,
                        'tmdb_info': tmdb_info
                    }

                    # Rate limiting
                    import time
                    time.sleep(0.3)

                except Exception as e:
                    print(f"      ❌ Error buscando {series_name}: {e}")
                    # Agregar sin TMDB en caso de error
                    enriched_structure[series_name] = {
                        'seasons': seasons,
                        'tmdb_info': {
                            'original_name': series_name,
                            'tmdb_id': None,
                            'tmdb_name': series_name,
                            'tmdb_year': None,
                            'tmdb_rating': 0
                        }
                    }

            tmdb_assigner.disconnect()
            print(f"✅ Búsqueda TMDB completada")
            return enriched_structure

        except Exception as e:
            print(f"❌ Error enriqueciendo con TMDB: {e}")
            return self.add_fallback_tmdb_info(m3u_series_structure)

    def add_fallback_tmdb_info(self, m3u_series_structure):
        """Agregar información TMDB vacía como fallback"""
        enriched_structure = {}

        for series_name, seasons in m3u_series_structure.items():
            enriched_structure[series_name] = {
                'seasons': seasons,
                'tmdb_info': {
                    'original_name': series_name,
                    'tmdb_id': None,
                    'tmdb_name': series_name,
                    'tmdb_year': None,
                    'tmdb_rating': 0
                }
            }

        return enriched_structure

    def get_or_update_db_cache(self):
        """Obtener caché de BD o actualizarlo si es necesario"""
        import time

        # Verificar si el caché existe y es reciente (5 minutos)
        cache_max_age = 300  # 5 minutos en segundos
        current_time = time.time()

        if (self.db_cache is not None and
            self.cache_timestamp is not None and
            (current_time - self.cache_timestamp) < cache_max_age):

            print("⚡ Usando caché de BD (datos recientes)")
            self.status_label.config(text="⚡ Usando caché de BD...")
            return self.db_cache

        # Caché no existe o está desactualizado, crear nuevo
        print("🔄 Actualizando caché de BD...")
        self.status_label.config(text="🔄 Actualizando caché de BD...")

        # Obtener estructura completa
        db_structure = self.get_complete_database_structure()

        if db_structure:
            # Guardar en caché
            self.db_cache = db_structure
            self.cache_timestamp = current_time
            print(f"✅ Caché actualizado con {len(db_structure['series_list'])} series")

        return db_structure

    def invalidate_db_cache(self):
        """Invalidar caché de BD (llamar después de importar nuevas series)"""
        self.db_cache = None
        self.cache_timestamp = None
        print("🗑️ Caché de BD invalidado")

    def get_cache_info(self):
        """Obtener información del caché actual"""
        if self.db_cache is None:
            return {
                'status': 'empty',
                'series_count': 0,
                'age_seconds': 0,
                'size_mb': 0
            }

        import time
        import sys

        current_time = time.time()
        age_seconds = current_time - (self.cache_timestamp or current_time)

        # Calcular tamaño aproximado del caché
        cache_size = sys.getsizeof(self.db_cache)
        for series_list in self.db_cache.values():
            if isinstance(series_list, list):
                cache_size += sum(sys.getsizeof(item) for item in series_list)
            elif isinstance(series_list, dict):
                cache_size += sum(sys.getsizeof(item) for item in series_list.values())

        return {
            'status': 'active',
            'series_count': len(self.db_cache.get('series_list', [])),
            'age_seconds': int(age_seconds),
            'size_mb': round(cache_size / (1024 * 1024), 2)
        }

    def preload_db_cache(self):
        """Precargar caché de BD en segundo plano"""
        def preload_worker():
            try:
                print("🔄 Precargando caché de BD en segundo plano...")
                self.get_or_update_db_cache()
                print("✅ Caché precargado exitosamente")
            except Exception as e:
                print(f"❌ Error precargando caché: {e}")

        import threading
        thread = threading.Thread(target=preload_worker, daemon=True)
        thread.start()

    def get_complete_database_structure(self):
        """Obtener estructura completa de BD con una sola consulta optimizada"""
        try:
            from db_connection import DatabaseConnection

            db = DatabaseConnection()
            if not db.connect():
                return {}

            print("💾 Ejecutando consulta optimizada con caché...")

            # Consulta optimizada para caché (sin columnas que no existen)
            query = """
            SELECT
                ss.id as series_id,
                ss.title as series_title,
                ss.tmdb_id,
                ss.year,
                COUNT(DISTINCT se.season_num) as total_seasons,
                COUNT(se.id) as total_episodes,
                GROUP_CONCAT(
                    DISTINCT CONCAT(
                        'S', LPAD(COALESCE(se.season_num, 1), 2, '0'),
                        'E', LPAD(COALESCE(se.episode_num, 1), 2, '0'),
                        ':', COALESCE(s.stream_display_name, 'Sin título')
                    )
                    ORDER BY se.season_num, se.episode_num
                    SEPARATOR '|'
                ) as episodes_data,
                MIN(se.id) as first_episode_id,
                MAX(se.id) as last_episode_id
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE ss.title IS NOT NULL
            GROUP BY ss.id, ss.title, ss.tmdb_id, ss.year
            ORDER BY ss.title
            """

            results = db.execute_query(query)
            db.disconnect()

            if not results:
                print("⚠️ No se encontraron series en la base de datos")
                return {}

            print(f"✅ Consultadas {len(results)} series de la BD")

            # Procesar resultados en estructura optimizada
            db_structure = {
                'by_name': {},      # Búsqueda por nombre exacto
                'by_tmdb': {},      # Búsqueda por TMDB ID
                'by_name_fuzzy': {},# Búsqueda fuzzy por nombre
                'series_list': []   # Lista completa para referencia
            }

            import time
            current_time = time.time()

            for row in results:
                try:
                    series_title = row.get('series_title', '').strip()
                    if not series_title:
                        print(f"⚠️ Serie sin título, saltando...")
                        continue

                    series_info = {
                        'series_id': row.get('series_id'),
                        'title': series_title,
                        'tmdb_id': row.get('tmdb_id'),
                        'year': row.get('year'),
                        'total_seasons': row.get('total_seasons', 0) or 0,
                        'total_episodes': row.get('total_episodes', 0) or 0,
                        'episodes_detail': self.parse_episodes_data(row.get('episodes_data', '')),
                        'first_episode_id': row.get('first_episode_id'),
                        'last_episode_id': row.get('last_episode_id'),
                        'cache_timestamp': current_time
                    }

                    # Indexar por nombre exacto
                    db_structure['by_name'][series_title] = series_info

                    # Indexar por TMDB ID si existe
                    if row.get('tmdb_id'):
                        db_structure['by_tmdb'][row['tmdb_id']] = series_info

                    # Indexar por nombre fuzzy (sin espacios, minúsculas)
                    try:
                        fuzzy_name = self.normalize_series_name(series_title)
                        if fuzzy_name:  # Solo si el nombre fuzzy no está vacío
                            db_structure['by_name_fuzzy'][fuzzy_name] = series_info
                    except Exception as e:
                        print(f"⚠️ Error normalizando '{series_title}': {e}")

                    # Agregar a lista completa
                    db_structure['series_list'].append(series_info)

                except Exception as e:
                    print(f"⚠️ Error procesando serie: {e}")
                    continue

            print(f"📊 Estructura indexada:")
            print(f"   📺 Por nombre: {len(db_structure['by_name'])} series")
            print(f"   🎬 Por TMDB: {len(db_structure['by_tmdb'])} series")
            print(f"   🔍 Por fuzzy: {len(db_structure['by_name_fuzzy'])} series")

            # DEBUG: Mostrar algunas claves
            if db_structure['by_name']:
                print(f"   🔍 Muestra de claves by_name:")
                sample_keys = list(db_structure['by_name'].keys())[:3]
                for i, key in enumerate(sample_keys, 1):
                    print(f"      {i}. '{key}'")

            return db_structure

        except Exception as e:
            print(f"❌ Error obteniendo estructura completa: {e}")
            return {}

    def parse_episodes_data(self, episodes_data):
        """Parsear datos de episodios concatenados"""
        if not episodes_data:
            return {}

        seasons = {}
        episodes_list = episodes_data.split('|')

        for episode_str in episodes_list:
            try:
                # Formato: S01E01:Título del episodio
                parts = episode_str.split(':', 1)
                if len(parts) != 2:
                    continue

                season_episode = parts[0]  # S01E01
                title = parts[1]           # Título del episodio

                # Extraer temporada y episodio
                if season_episode.startswith('S') and 'E' in season_episode:
                    season_part, episode_part = season_episode[1:].split('E')
                    season_num = int(season_part)
                    episode_num = int(episode_part)

                    if season_num not in seasons:
                        seasons[season_num] = []

                    seasons[season_num].append({
                        'episode_num': episode_num,
                        'title': title
                    })

            except Exception as e:
                print(f"⚠️ Error parseando episodio '{episode_str}': {e}")
                continue

        return seasons

    def normalize_series_name(self, name):
        """Normalizar nombre de serie para comparación fuzzy"""
        try:
            if not name or not isinstance(name, str):
                return ""

            # Convertir a minúsculas, remover espacios y caracteres especiales
            import re
            normalized = re.sub(r'[^\w]', '', name.lower().strip())

            # Remover artículos comunes
            articles = ['the', 'el', 'la', 'los', 'las', 'un', 'una', 'de', 'del', 'y', 'and']
            for article in articles:
                if normalized.startswith(article) and len(normalized) > len(article):
                    normalized = normalized[len(article):]
                    break

            return normalized.strip()

        except Exception as e:
            print(f"⚠️ Error normalizando nombre '{name}': {e}")
            return ""

    def get_database_series_structure_with_tmdb(self):
        """Obtener estructura de BD con información TMDB"""
        try:
            from db_connection import DatabaseConnection

            db = DatabaseConnection()
            if not db.connect():
                return {}

            # Consulta para obtener series con TMDB y episodios
            query = """
            SELECT
                ss.id as series_id,
                ss.title as series_title,
                ss.tmdb_id,
                ss.year,
                se.season_num,
                se.episode_num,
                s.stream_display_name,
                se.id as episode_id
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE ss.title IS NOT NULL
            ORDER BY ss.title, se.season_num, se.episode_num
            """

            results = db.execute_query(query)
            db.disconnect()

            if not results:
                return {}

            # Organizar por TMDB ID y por nombre
            series_structure = {}

            for row in results:
                series_id = row['series_id']
                series_title = row['series_title']
                tmdb_id = row['tmdb_id']
                season_num = row['season_num'] or 1
                episode_num = row['episode_num'] or 0

                # Crear clave única para la serie
                series_key = f"tmdb_{tmdb_id}" if tmdb_id else f"name_{series_title}"

                if series_key not in series_structure:
                    series_structure[series_key] = {
                        'series_info': {
                            'series_id': series_id,
                            'title': series_title,
                            'tmdb_id': tmdb_id,
                            'year': row['year']
                        },
                        'seasons': {}
                    }

                # Agregar episodios
                if season_num not in series_structure[series_key]['seasons']:
                    series_structure[series_key]['seasons'][season_num] = []

                # Solo agregar si hay episodio
                if row['episode_id']:
                    series_structure[series_key]['seasons'][season_num].append({
                        'episode_num': episode_num,
                        'title': row['stream_display_name'],
                        'episode_id': row['episode_id']
                    })

            return series_structure

        except Exception as e:
            print(f"Error obteniendo estructura de BD con TMDB: {e}")
            return {}

    def compare_series_structures_by_tmdb(self, m3u_series_with_tmdb, db_series_structure):
        """Comparar estructuras usando TMDB ID como referencia principal"""
        comparison_results = {
            'new_series': [],           # Series que no existen en BD
            'missing_episodes': [],     # Episodios faltantes en series existentes
            'new_seasons': [],          # Temporadas nuevas en series existentes
            'complete_series': [],      # Series que están completas
            'name_differences': [],     # Series iguales con nombres diferentes
            'total_new_episodes': 0,
            'total_new_series': 0,
            'total_new_seasons': 0,
            'total_name_differences': 0
        }

        print(f"🔄 Comparando {len(m3u_series_with_tmdb)} series del M3U con BD...")

        # Analizar cada serie del M3U
        for series_name, series_data in m3u_series_with_tmdb.items():
            m3u_seasons = series_data['seasons']
            tmdb_info = series_data['tmdb_info']
            tmdb_id = tmdb_info.get('tmdb_id')

            print(f"   🔍 Analizando: {series_name} (TMDB: {tmdb_id})")

            # Buscar en BD por TMDB ID primero, luego por nombre
            db_series_key = None
            db_series_data = None
            match_type = None

            if tmdb_id:
                # Buscar por TMDB ID
                tmdb_key = f"tmdb_{tmdb_id}"
                if tmdb_key in db_series_structure:
                    db_series_key = tmdb_key
                    db_series_data = db_series_structure[tmdb_key]
                    match_type = "tmdb_id"
                    print(f"      ✅ Encontrada por TMDB ID: {db_series_data['series_info']['title']}")

            if not db_series_data:
                # Buscar por nombre exacto
                name_key = f"name_{series_name}"
                if name_key in db_series_structure:
                    db_series_key = name_key
                    db_series_data = db_series_structure[name_key]
                    match_type = "exact_name"
                    print(f"      ✅ Encontrada por nombre exacto")

            if not db_series_data:
                # Serie completamente nueva
                comparison_results['new_series'].append({
                    'series_name': series_name,
                    'tmdb_info': tmdb_info,
                    'seasons': m3u_seasons,
                    'total_episodes': sum(len(episodes) for episodes in m3u_seasons.values())
                })
                comparison_results['total_new_series'] += 1
                comparison_results['total_new_episodes'] += sum(len(episodes) for episodes in m3u_seasons.values())
                print(f"      🆕 Serie nueva")

            else:
                # Serie existe, comparar contenido
                db_seasons = db_series_data['seasons']
                db_series_info = db_series_data['series_info']

                # Verificar si hay diferencia de nombres
                if match_type == "tmdb_id" and series_name != db_series_info['title']:
                    comparison_results['name_differences'].append({
                        'm3u_name': series_name,
                        'db_name': db_series_info['title'],
                        'tmdb_id': tmdb_id,
                        'tmdb_name': tmdb_info.get('tmdb_name', series_name)
                    })
                    comparison_results['total_name_differences'] += 1
                    print(f"      🔄 Misma serie, nombres diferentes: '{series_name}' vs '{db_series_info['title']}'")

                # Comparar temporadas y episodios
                series_missing = []
                series_new_seasons = []

                for season_num, m3u_episodes in m3u_seasons.items():

                    if season_num not in db_seasons:
                        # Temporada nueva
                        series_new_seasons.append({
                            'season_num': season_num,
                            'episodes': m3u_episodes,
                            'episode_count': len(m3u_episodes)
                        })
                        comparison_results['total_new_seasons'] += 1
                        comparison_results['total_new_episodes'] += len(m3u_episodes)
                        print(f"         🆕 Temporada nueva: S{season_num:02d}")

                    else:
                        # Temporada existe, comparar episodios
                        db_episodes = db_seasons[season_num]
                        db_episode_nums = {ep['episode_num'] for ep in db_episodes}

                        missing_episodes = []
                        for m3u_episode in m3u_episodes:
                            if m3u_episode['episode_num'] not in db_episode_nums:
                                missing_episodes.append(m3u_episode)

                        if missing_episodes:
                            series_missing.append({
                                'season_num': season_num,
                                'missing_episodes': missing_episodes,
                                'missing_count': len(missing_episodes)
                            })
                            comparison_results['total_new_episodes'] += len(missing_episodes)
                            print(f"         ⚠️ S{season_num:02d}: {len(missing_episodes)} episodios faltantes")

                # Agregar a resultados si hay diferencias
                if series_missing:
                    comparison_results['missing_episodes'].append({
                        'series_name': series_name,
                        'db_series_name': db_series_info['title'],
                        'tmdb_info': tmdb_info,
                        'missing_by_season': series_missing,
                        'match_type': match_type
                    })

                if series_new_seasons:
                    comparison_results['new_seasons'].append({
                        'series_name': series_name,
                        'db_series_name': db_series_info['title'],
                        'tmdb_info': tmdb_info,
                        'new_seasons': series_new_seasons,
                        'match_type': match_type
                    })

                # Si no hay diferencias, está completa
                if not series_missing and not series_new_seasons:
                    comparison_results['complete_series'].append({
                        'series_name': series_name,
                        'db_series_name': db_series_info['title'],
                        'tmdb_info': tmdb_info,
                        'match_type': match_type
                    })
                    print(f"         ✅ Serie completa")

        print(f"✅ Comparación completada")
        return comparison_results

    def compare_series_structures_optimized(self, m3u_series_structure, db_complete_structure):
        """Comparación optimizada usando consulta SQL única"""
        comparison_results = {
            'new_series': [],           # Series que no existen en BD
            'missing_episodes': [],     # Episodios faltantes en series existentes
            'new_seasons': [],          # Temporadas nuevas en series existentes
            'complete_series': [],      # Series que están completas
            'name_differences': [],     # Series iguales con nombres diferentes
            'total_new_episodes': 0,
            'total_new_series': 0,
            'total_new_seasons': 0,
            'total_name_differences': 0
        }

        # Mostrar información del caché
        cache_info = self.get_cache_info()
        print(f"🔄 Comparación optimizada de {len(m3u_series_structure)} series...")
        print(f"⚡ Caché BD: {cache_info['series_count']} series, {cache_info['age_seconds']}s antigüedad, {cache_info['size_mb']}MB")

        # DEBUG: Verificar estructura de BD
        print(f"🔍 DEBUG - Estructura BD:")
        print(f"   📊 Tipo: {type(db_complete_structure)}")
        if isinstance(db_complete_structure, dict):
            print(f"   🔑 Claves: {list(db_complete_structure.keys())}")
            if 'by_name' in db_complete_structure:
                print(f"   📺 by_name: {len(db_complete_structure['by_name'])} entradas")
                # Mostrar algunas claves
                sample_keys = list(db_complete_structure['by_name'].keys())[:3]
                for i, key in enumerate(sample_keys, 1):
                    print(f"      {i}. '{key}'")
            else:
                print(f"   ❌ 'by_name' no encontrado")
        else:
            print(f"   ❌ No es diccionario")

        # DEBUG: Verificar estructura M3U
        print(f"🔍 DEBUG - Estructura M3U:")
        print(f"   📊 Tipo: {type(m3u_series_structure)}")
        print(f"   📺 Series: {len(m3u_series_structure)}")
        sample_m3u = list(m3u_series_structure.keys())[:3]
        for i, key in enumerate(sample_m3u, 1):
            print(f"      {i}. '{key}'")

        # Analizar cada serie del M3U
        for series_name, m3u_seasons in m3u_series_structure.items():
            try:
                print(f"   🔍 Analizando: {series_name}")

                # Verificar que la estructura de BD esté correcta
                if not isinstance(db_complete_structure, dict):
                    print(f"      ❌ Error: estructura de BD inválida")
                    continue

                if 'by_name' not in db_complete_structure:
                    print(f"      ❌ Error: índice 'by_name' no encontrado")
                    continue

                # Buscar en BD usando múltiples métodos
                db_series_info = None
                match_type = None

                # 1. Búsqueda por nombre exacto (más rápida)
                try:
                    if series_name in db_complete_structure['by_name']:
                        db_series_info = db_complete_structure['by_name'][series_name]
                        match_type = "exact_name"
                        print(f"      ✅ Encontrada por nombre exacto")
                except Exception as e:
                    print(f"      ⚠️ Error en búsqueda exacta: {e}")

                # 2. Búsqueda fuzzy por nombre normalizado
                if not db_series_info:
                    try:
                        fuzzy_name = self.normalize_series_name(series_name)
                        if fuzzy_name and 'by_name_fuzzy' in db_complete_structure:
                            if fuzzy_name in db_complete_structure['by_name_fuzzy']:
                                db_series_info = db_complete_structure['by_name_fuzzy'][fuzzy_name]
                                match_type = "fuzzy_name"
                                print(f"      ✅ Encontrada por nombre fuzzy: '{db_series_info['title']}'")
                    except Exception as e:
                        print(f"      ⚠️ Error en búsqueda fuzzy: {e}")

                # 3. Búsqueda por similitud de nombres
                if not db_series_info:
                    try:
                        if 'series_list' in db_complete_structure:
                            db_series_info = self.find_similar_series(series_name, db_complete_structure['series_list'])
                            if db_series_info:
                                match_type = "similar_name"
                                print(f"      ✅ Encontrada por similitud: '{db_series_info['title']}'")
                    except Exception as e:
                        print(f"      ⚠️ Error en búsqueda por similitud: {e}")

                if not db_series_info:
                    # Serie completamente nueva
                    total_episodes = sum(len(episodes) for episodes in m3u_seasons.values())
                    comparison_results['new_series'].append({
                        'series_name': series_name,
                        'seasons': m3u_seasons,
                        'total_episodes': total_episodes
                    })
                    comparison_results['total_new_series'] += 1
                    comparison_results['total_new_episodes'] += total_episodes
                    print(f"      🆕 Serie nueva")

                else:
                    # Serie existe, comparar contenido
                    db_seasons = db_series_info['episodes_detail']

                    # Verificar si hay diferencia de nombres
                    if match_type in ["fuzzy_name", "similar_name"] and series_name != db_series_info['title']:
                        comparison_results['name_differences'].append({
                            'm3u_name': series_name,
                            'db_name': db_series_info['title'],
                            'tmdb_id': db_series_info.get('tmdb_id'),
                            'match_type': match_type
                        })
                        comparison_results['total_name_differences'] += 1
                        print(f"      🔄 Nombres diferentes: '{series_name}' vs '{db_series_info['title']}'")

                    # Comparar temporadas y episodios
                    series_missing = []
                    series_new_seasons = []

                    for season_num, m3u_episodes in m3u_seasons.items():

                        if season_num not in db_seasons:
                            # Temporada nueva
                            series_new_seasons.append({
                                'season_num': season_num,
                                'episodes': m3u_episodes,
                                'episode_count': len(m3u_episodes)
                            })
                            comparison_results['total_new_seasons'] += 1
                            comparison_results['total_new_episodes'] += len(m3u_episodes)
                            print(f"         🆕 Temporada nueva: S{season_num:02d}")

                        else:
                            # Temporada existe, comparar episodios
                            db_episodes = db_seasons[season_num]
                            db_episode_nums = {ep['episode_num'] for ep in db_episodes}

                            missing_episodes = []
                            for m3u_episode in m3u_episodes:
                                if m3u_episode['episode_num'] not in db_episode_nums:
                                    missing_episodes.append(m3u_episode)

                            if missing_episodes:
                                series_missing.append({
                                    'season_num': season_num,
                                    'missing_episodes': missing_episodes,
                                    'missing_count': len(missing_episodes)
                                })
                                comparison_results['total_new_episodes'] += len(missing_episodes)
                                print(f"         ⚠️ S{season_num:02d}: {len(missing_episodes)} episodios faltantes")

                    # Agregar a resultados si hay diferencias
                    if series_missing:
                        comparison_results['missing_episodes'].append({
                            'series_name': series_name,
                            'db_series_name': db_series_info['title'],
                            'missing_by_season': series_missing,
                            'match_type': match_type
                        })

                    if series_new_seasons:
                        comparison_results['new_seasons'].append({
                            'series_name': series_name,
                            'db_series_name': db_series_info['title'],
                            'new_seasons': series_new_seasons,
                            'match_type': match_type
                        })

                    # Si no hay diferencias, está completa
                    if not series_missing and not series_new_seasons:
                        comparison_results['complete_series'].append({
                            'series_name': series_name,
                            'db_series_name': db_series_info['title'],
                            'match_type': match_type
                        })
                        print(f"         ✅ Serie completa")

            except Exception as e:
                print(f"      ❌ Error procesando serie '{series_name}': {e}")
                # Agregar como serie nueva en caso de error
                total_episodes = sum(len(episodes) for episodes in m3u_seasons.values())
                comparison_results['new_series'].append({
                    'series_name': series_name,
                    'seasons': m3u_seasons,
                    'total_episodes': total_episodes,
                    'error': str(e)
                })
                comparison_results['total_new_series'] += 1
                comparison_results['total_new_episodes'] += total_episodes
                continue

        print(f"✅ Comparación optimizada completada")
        return comparison_results

    def find_similar_series(self, m3u_name, db_series_list):
        """Encontrar serie similar usando algoritmos de similitud"""
        # Implementación básica de similitud
        # En el futuro se puede mejorar con algoritmos más sofisticados

        m3u_normalized = self.normalize_series_name(m3u_name)
        best_match = None
        best_score = 0

        for db_series in db_series_list:
            db_normalized = self.normalize_series_name(db_series['title'])

            # Calcular similitud simple (caracteres en común)
            if len(m3u_normalized) > 0 and len(db_normalized) > 0:
                common_chars = set(m3u_normalized) & set(db_normalized)
                score = len(common_chars) / max(len(m3u_normalized), len(db_normalized))

                # Solo considerar si hay al menos 60% de similitud
                if score > 0.6 and score > best_score:
                    best_score = score
                    best_match = db_series

        return best_match if best_score > 0.6 else None

    def display_comparison_results_optimized(self, m3u_series_structure, db_complete_structure, comparison_results):
        """Mostrar resultados de comparación optimizada"""
        colors = self.current_comparator_window.gemini_colors

        # Limpiar paneles
        self.m3u_text.config(state='normal')
        self.m3u_text.delete(1.0, tk.END)

        self.db_text.config(state='normal')
        self.db_text.delete(1.0, tk.END)

        self.actions_text.config(state='normal')
        self.actions_text.delete(1.0, tk.END)

        # Panel M3U - Mostrar contenido
        self.m3u_text.insert(tk.END, f"📁 CONTENIDO DEL M3U ANALIZADO\n", "series")
        self.m3u_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        total_series = len(m3u_series_structure)
        total_episodes = sum(sum(len(episodes) for episodes in seasons.values()) for seasons in m3u_series_structure.values())

        self.m3u_text.insert(tk.END, f"📊 RESUMEN:\n", "series")
        self.m3u_text.insert(tk.END, f"   📺 Series: {total_series}\n", "season")
        self.m3u_text.insert(tk.END, f"   🎬 Episodios: {total_episodes}\n\n", "season")

        for series_name, seasons in m3u_series_structure.items():
            self.m3u_text.insert(tk.END, f"📺 {series_name}\n", "series")

            for season_num in sorted(seasons.keys()):
                episodes = seasons[season_num]
                self.m3u_text.insert(tk.END, f"   └ S{season_num:02d} ({len(episodes)} episodios)\n", "season")

            self.m3u_text.insert(tk.END, "\n")

        # Panel BD - Mostrar comparación optimizada
        self.db_text.insert(tk.END, f"💾 COMPARACIÓN OPTIMIZADA CON BD\n", "series")
        self.db_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        db_total_series = len(db_complete_structure['series_list'])
        db_total_episodes = sum(series['total_episodes'] for series in db_complete_structure['series_list'])

        self.db_text.insert(tk.END, f"📊 BASE DE DATOS ACTUAL:\n", "series")
        self.db_text.insert(tk.END, f"   📺 Series: {db_total_series}\n", "season")
        self.db_text.insert(tk.END, f"   🎬 Episodios: {db_total_episodes}\n\n", "season")

        # Mostrar estado de cada serie del M3U
        for series_name, seasons in m3u_series_structure.items():
            # Buscar en BD
            found_series = None
            match_type = None

            if series_name in db_complete_structure['by_name']:
                found_series = db_complete_structure['by_name'][series_name]
                match_type = "exact"
            else:
                fuzzy_name = self.normalize_series_name(series_name)
                if fuzzy_name in db_complete_structure['by_name_fuzzy']:
                    found_series = db_complete_structure['by_name_fuzzy'][fuzzy_name]
                    match_type = "fuzzy"

            if found_series:
                if match_type == "fuzzy" and series_name != found_series['title']:
                    self.db_text.insert(tk.END, f"🔄 {series_name} → {found_series['title']}\n", "series")
                    self.db_text.insert(tk.END, f"   📊 Nombres diferentes detectados\n", "missing")
                else:
                    self.db_text.insert(tk.END, f"📺 {series_name}\n", "series")

                # Mostrar estadísticas
                m3u_total = sum(len(episodes) for episodes in seasons.values())
                db_total = found_series['total_episodes']

                if m3u_total > db_total:
                    self.db_text.insert(tk.END, f"   ⚠️ BD: {db_total} eps, M3U: {m3u_total} eps (+{m3u_total - db_total})\n", "missing")
                elif m3u_total == db_total:
                    self.db_text.insert(tk.END, f"   ✅ Completa: {db_total} episodios\n", "complete")
                else:
                    self.db_text.insert(tk.END, f"   ✅ BD: {db_total} eps, M3U: {m3u_total} eps\n", "complete")
            else:
                self.db_text.insert(tk.END, f"❌ {series_name}\n", "missing")
                self.db_text.insert(tk.END, f"   🆕 Serie nueva\n", "missing")

            self.db_text.insert(tk.END, "\n")

        # Panel de acciones - Mostrar resumen optimizado
        self.actions_text.insert(tk.END, f"⚡ ANÁLISIS OPTIMIZADO COMPLETADO\n", "summary")
        self.actions_text.insert(tk.END, f"{'='*60}\n\n", "summary")

        # Estadísticas generales
        self.actions_text.insert(tk.END, f"📊 ESTADÍSTICAS:\n", "summary")
        self.actions_text.insert(tk.END, f"   🆕 Series nuevas: {comparison_results['total_new_series']}\n", "new_series")
        self.actions_text.insert(tk.END, f"   📺 Temporadas nuevas: {comparison_results['total_new_seasons']}\n", "new_season")
        self.actions_text.insert(tk.END, f"   🎬 Episodios a agregar: {comparison_results['total_new_episodes']}\n", "missing_episodes")
        self.actions_text.insert(tk.END, f"   🔄 Nombres diferentes: {comparison_results['total_name_differences']}\n", "missing_episodes")
        self.actions_text.insert(tk.END, f"   ✅ Series completas: {len(comparison_results['complete_series'])}\n\n", "summary")

        # Mostrar información de optimización y caché
        cache_info = self.get_cache_info()
        self.actions_text.insert(tk.END, f"⚡ OPTIMIZACIÓN CON CACHÉ:\n", "summary")
        self.actions_text.insert(tk.END, f"   🚀 Consulta SQL única (sin TMDB API)\n", "summary")
        self.actions_text.insert(tk.END, f"   📊 Comparación en memoria\n", "summary")
        self.actions_text.insert(tk.END, f"   🔍 Búsqueda fuzzy integrada\n", "summary")
        self.actions_text.insert(tk.END, f"   💾 Caché: {cache_info['series_count']} series, {cache_info['size_mb']}MB\n", "summary")
        self.actions_text.insert(tk.END, f"   ⏱️ Edad del caché: {cache_info['age_seconds']} segundos\n\n", "summary")

        # Detalles de acciones (como antes)
        if comparison_results['name_differences']:
            self.actions_text.insert(tk.END, f"🔄 NOMBRES DIFERENTES ({len(comparison_results['name_differences'])}):\n", "missing_episodes")
            for diff in comparison_results['name_differences']:
                self.actions_text.insert(tk.END, f"   📺 M3U: '{diff['m3u_name']}' → BD: '{diff['db_name']}'\n", "missing_episodes")
                self.actions_text.insert(tk.END, f"      🔍 Método: {diff['match_type']}\n", "summary")
            self.actions_text.insert(tk.END, "\n")

        if comparison_results['new_series']:
            self.actions_text.insert(tk.END, f"🆕 SERIES NUEVAS ({len(comparison_results['new_series'])}):\n", "new_series")
            for series in comparison_results['new_series']:
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']} ({series['total_episodes']} episodios)\n", "new_series")
            self.actions_text.insert(tk.END, "\n")

        if comparison_results['missing_episodes']:
            self.actions_text.insert(tk.END, f"⚠️ EPISODIOS FALTANTES ({len(comparison_results['missing_episodes'])}):\n", "missing_episodes")
            for series in comparison_results['missing_episodes']:
                total_missing = sum(season['missing_count'] for season in series['missing_by_season'])
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']} (+{total_missing} episodios)\n", "missing_episodes")
            self.actions_text.insert(tk.END, "\n")

        if not comparison_results['total_new_episodes']:
            self.actions_text.insert(tk.END, f"✅ ¡Tu base de datos está actualizada!\n", "summary")
            self.actions_text.insert(tk.END, f"No se detectaron diferencias con el M3U cargado.\n", "summary")

        # Deshabilitar edición
        self.m3u_text.config(state='disabled')
        self.db_text.config(state='disabled')
        self.actions_text.config(state='disabled')

        # Guardar resultados para uso posterior
        self.current_comparison_results = comparison_results
        self.current_m3u_structure = m3u_series_structure

    def fix_tvpro_urls(self):
        """Corregir URLs de tvpro.tech con escape inconsistente"""
        try:
            from db_connection import DatabaseConnection

            print("🔧 INICIANDO CORRECCIÓN DE URLs TVPRO.TECH")
            print("=" * 60)

            db = DatabaseConnection()
            if not db.connect():
                print("❌ No se pudo conectar a la base de datos")
                return False

            # 1. Verificar cuántos registros necesitan corrección
            check_query = """
            SELECT COUNT(*) as total
            FROM streams
            WHERE type = 5
            AND stream_source LIKE '%tvpro.tech%'
            AND stream_source LIKE '%http:\\\\/\\\\/%'
            """

            result = db.execute_query(check_query)
            if not result:
                print("❌ Error verificando registros")
                db.disconnect()
                return False

            total_to_fix = result[0]['total']
            print(f"📊 Registros a corregir: {total_to_fix}")

            if total_to_fix == 0:
                print("✅ No hay registros que necesiten corrección")
                db.disconnect()
                return True

            # 2. Mostrar algunos ejemplos
            examples_query = """
            SELECT id, stream_display_name, stream_source
            FROM streams
            WHERE type = 5
            AND stream_source LIKE '%tvpro.tech%'
            AND stream_source LIKE '%http:\\\\/\\\\/%'
            LIMIT 3
            """

            examples = db.execute_query(examples_query)
            if examples:
                print("\n📋 EJEMPLOS A CORREGIR:")
                for i, row in enumerate(examples, 1):
                    print(f"   {i}. ID: {row['id']}")
                    print(f"      Nombre: {row['stream_display_name']}")
                    print(f"      URL: {row['stream_source']}")
                    print()

            # 3. Aplicar corrección
            print("🔧 Aplicando corrección...")

            fix_query = """
            UPDATE streams
            SET stream_source = REPLACE(stream_source, 'http:\\\\/\\\\/', 'http:\\/\\/')
            WHERE type = 5
            AND stream_source LIKE '%tvpro.tech%'
            AND stream_source LIKE '%http:\\\\/\\\\/%'
            """

            success = db.execute_update(fix_query)

            if success:
                print(f"✅ Corrección aplicada exitosamente")

                # 4. Verificar resultado
                verify_result = db.execute_query(check_query)
                remaining = verify_result[0]['total'] if verify_result else -1

                print(f"📊 Registros restantes con problema: {remaining}")

                if remaining == 0:
                    print("🎉 ¡Todas las URLs han sido corregidas!")
                else:
                    print(f"⚠️ Aún quedan {remaining} registros con problema")

            else:
                print("❌ Error aplicando la corrección")

            db.disconnect()
            return success

        except Exception as e:
            print(f"❌ Error durante la corrección: {e}")
            return False

    def fix_tvpro_urls_with_confirmation(self):
        """Corregir URLs con confirmación del usuario"""
        import tkinter.messagebox as msgbox

        # Confirmar acción
        result = msgbox.askyesno(
            "Confirmar Corrección",
            "¿Estás seguro de que quieres corregir las URLs de TVPro.tech?\n\n"
            "Esta acción modificará la base de datos y corregirá el formato de escape "
            "inconsistente en las URLs.\n\n"
            "Recomendado: Hacer backup antes de continuar.",
            icon='warning'
        )

        if result:
            # Ejecutar corrección
            success = self.fix_tvpro_urls()

            if success:
                msgbox.showinfo(
                    "Corrección Completada",
                    "✅ Las URLs de TVPro.tech han sido corregidas exitosamente.\n\n"
                    "Se recomienda invalidar el caché para reflejar los cambios.",
                    icon='info'
                )

                # Invalidar caché automáticamente
                self.invalidate_db_cache()

            else:
                msgbox.showerror(
                    "Error en Corrección",
                    "❌ Hubo un error al corregir las URLs.\n\n"
                    "Revisa la consola para más detalles.",
                    icon='error'
                )

    def display_comparison_results_with_tmdb(self, m3u_series_with_tmdb, db_series_structure, comparison_results):
        """Mostrar resultados de comparación con información TMDB"""
        colors = self.current_comparator_window.gemini_colors

        # Limpiar paneles
        self.m3u_text.config(state='normal')
        self.m3u_text.delete(1.0, tk.END)

        self.db_text.config(state='normal')
        self.db_text.delete(1.0, tk.END)

        self.actions_text.config(state='normal')
        self.actions_text.delete(1.0, tk.END)

        # Panel M3U - Mostrar contenido con TMDB
        self.m3u_text.insert(tk.END, f"📁 CONTENIDO DEL M3U CON TMDB\n", "series")
        self.m3u_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        total_series = len(m3u_series_with_tmdb)
        total_episodes = sum(sum(len(episodes) for episodes in data['seasons'].values()) for data in m3u_series_with_tmdb.values())

        self.m3u_text.insert(tk.END, f"📊 RESUMEN:\n", "series")
        self.m3u_text.insert(tk.END, f"   📺 Series: {total_series}\n", "season")
        self.m3u_text.insert(tk.END, f"   🎬 Episodios: {total_episodes}\n\n", "season")

        for series_name, series_data in m3u_series_with_tmdb.items():
            seasons = series_data['seasons']
            tmdb_info = series_data['tmdb_info']

            # Mostrar nombre con información TMDB
            if tmdb_info.get('tmdb_id'):
                self.m3u_text.insert(tk.END, f"📺 {series_name}\n", "series")
                self.m3u_text.insert(tk.END, f"   🎬 TMDB: {tmdb_info['tmdb_name']} ({tmdb_info['tmdb_year']}) - ID: {tmdb_info['tmdb_id']}\n", "new")
                self.m3u_text.insert(tk.END, f"   ⭐ Rating: {tmdb_info['tmdb_rating']:.1f}\n", "episode")
            else:
                self.m3u_text.insert(tk.END, f"📺 {series_name}\n", "series")
                self.m3u_text.insert(tk.END, f"   ❌ No encontrado en TMDB\n", "episode")

            for season_num in sorted(seasons.keys()):
                episodes = seasons[season_num]
                self.m3u_text.insert(tk.END, f"   └ S{season_num:02d} ({len(episodes)} episodios)\n", "season")

            self.m3u_text.insert(tk.END, "\n")

        # Panel BD - Mostrar comparación con TMDB
        self.db_text.insert(tk.END, f"💾 COMPARACIÓN CON BASE DE DATOS\n", "series")
        self.db_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        # Mostrar estado de cada serie del M3U
        for series_name, series_data in m3u_series_with_tmdb.items():
            m3u_seasons = series_data['seasons']
            tmdb_info = series_data['tmdb_info']
            tmdb_id = tmdb_info.get('tmdb_id')

            # Buscar en BD
            found_in_db = False
            db_series_name = None

            if tmdb_id:
                tmdb_key = f"tmdb_{tmdb_id}"
                if tmdb_key in db_series_structure:
                    found_in_db = True
                    db_series_name = db_series_structure[tmdb_key]['series_info']['title']

            if not found_in_db:
                name_key = f"name_{series_name}"
                if name_key in db_series_structure:
                    found_in_db = True
                    db_series_name = series_name

            if found_in_db:
                if db_series_name != series_name:
                    self.db_text.insert(tk.END, f"🔄 {series_name} → {db_series_name}\n", "series")
                    self.db_text.insert(tk.END, f"   📊 Misma serie, nombres diferentes (TMDB: {tmdb_id})\n", "missing")
                else:
                    self.db_text.insert(tk.END, f"📺 {series_name}\n", "series")

                # Mostrar estado de temporadas (simplificado)
                total_m3u_eps = sum(len(episodes) for episodes in m3u_seasons.values())
                self.db_text.insert(tk.END, f"   ✅ Existe en BD ({total_m3u_eps} episodios en M3U)\n", "complete")
            else:
                self.db_text.insert(tk.END, f"❌ {series_name}\n", "missing")
                self.db_text.insert(tk.END, f"   🆕 Serie nueva (TMDB: {tmdb_id or 'No encontrado'})\n", "missing")

            self.db_text.insert(tk.END, "\n")

        # Panel de acciones - Mostrar resumen con TMDB
        self.actions_text.insert(tk.END, f"⚡ RESUMEN CON ANÁLISIS TMDB\n", "summary")
        self.actions_text.insert(tk.END, f"{'='*60}\n\n", "summary")

        # Estadísticas generales
        self.actions_text.insert(tk.END, f"📊 ESTADÍSTICAS:\n", "summary")
        self.actions_text.insert(tk.END, f"   🆕 Series nuevas: {comparison_results['total_new_series']}\n", "new_series")
        self.actions_text.insert(tk.END, f"   📺 Temporadas nuevas: {comparison_results['total_new_seasons']}\n", "new_season")
        self.actions_text.insert(tk.END, f"   🎬 Episodios a agregar: {comparison_results['total_new_episodes']}\n", "missing_episodes")
        self.actions_text.insert(tk.END, f"   🔄 Nombres diferentes: {comparison_results['total_name_differences']}\n", "missing_episodes")
        self.actions_text.insert(tk.END, f"   ✅ Series completas: {len(comparison_results['complete_series'])}\n\n", "summary")

        # Diferencias de nombres (nuevo)
        if comparison_results['name_differences']:
            self.actions_text.insert(tk.END, f"🔄 MISMAS SERIES, NOMBRES DIFERENTES ({len(comparison_results['name_differences'])}):\n", "missing_episodes")
            for diff in comparison_results['name_differences']:
                self.actions_text.insert(tk.END, f"   📺 M3U: '{diff['m3u_name']}' → BD: '{diff['db_name']}'\n", "missing_episodes")
                self.actions_text.insert(tk.END, f"      🎬 TMDB: {diff['tmdb_name']} (ID: {diff['tmdb_id']})\n", "summary")
            self.actions_text.insert(tk.END, "\n")

        # Detalles de acciones (como antes pero con info TMDB)
        if comparison_results['new_series']:
            self.actions_text.insert(tk.END, f"🆕 SERIES NUEVAS ({len(comparison_results['new_series'])}):\n", "new_series")
            for series in comparison_results['new_series']:
                tmdb_info = series['tmdb_info']
                tmdb_text = f" (TMDB: {tmdb_info['tmdb_id']})" if tmdb_info.get('tmdb_id') else " (Sin TMDB)"
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']}{tmdb_text} ({series['total_episodes']} episodios)\n", "new_series")
            self.actions_text.insert(tk.END, "\n")

        if comparison_results['missing_episodes']:
            self.actions_text.insert(tk.END, f"⚠️ EPISODIOS FALTANTES ({len(comparison_results['missing_episodes'])}):\n", "missing_episodes")
            for series in comparison_results['missing_episodes']:
                total_missing = sum(season['missing_count'] for season in series['missing_by_season'])
                match_info = f" [{series['match_type']}]" if series.get('match_type') else ""
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']}{match_info} (+{total_missing} episodios)\n", "missing_episodes")
            self.actions_text.insert(tk.END, "\n")

        if not comparison_results['total_new_episodes']:
            self.actions_text.insert(tk.END, f"✅ ¡Tu base de datos está actualizada!\n", "summary")
            self.actions_text.insert(tk.END, f"No se detectaron diferencias con el M3U cargado.\n", "summary")

        # Deshabilitar edición
        self.m3u_text.config(state='disabled')
        self.db_text.config(state='disabled')
        self.actions_text.config(state='disabled')

        # Guardar resultados para uso posterior
        self.current_comparison_results = comparison_results
        self.current_m3u_structure = m3u_series_with_tmdb

    def get_database_series_structure(self):
        """Obtener estructura actual de series en la base de datos"""
        try:
            from db_connection import DatabaseConnection

            db = DatabaseConnection()
            if not db.connect():
                return {}

            # Consulta para obtener todas las series con sus episodios
            query = """
            SELECT
                ss.title as series_title,
                se.season_num,
                se.episode_num,
                s.stream_display_name,
                se.id as episode_id
            FROM streams_series ss
            LEFT JOIN streams_episodes se ON ss.id = se.series_id
            LEFT JOIN streams s ON se.stream_id = s.id
            WHERE ss.title IS NOT NULL
            ORDER BY ss.title, se.season_num, se.episode_num
            """

            results = db.execute_query(query)
            db.disconnect()

            if not results:
                return {}

            # Organizar en estructura jerárquica
            series_structure = {}

            for row in results:
                series_title = row['series_title']
                season_num = row['season_num'] or 1
                episode_num = row['episode_num'] or 0

                if series_title not in series_structure:
                    series_structure[series_title] = {}

                if season_num not in series_structure[series_title]:
                    series_structure[series_title][season_num] = []

                # Solo agregar si hay episodio
                if row['episode_id']:
                    series_structure[series_title][season_num].append({
                        'episode_num': episode_num,
                        'title': row['stream_display_name'],
                        'episode_id': row['episode_id']
                    })

            return series_structure

        except Exception as e:
            print(f"Error obteniendo estructura de BD: {e}")
            return {}

    def compare_series_structures(self, m3u_structure, db_structure):
        """Comparar estructuras y detectar diferencias"""
        comparison_results = {
            'new_series': [],           # Series que no existen en BD
            'missing_episodes': [],     # Episodios faltantes en series existentes
            'new_seasons': [],          # Temporadas nuevas en series existentes
            'complete_series': [],      # Series que están completas
            'total_new_episodes': 0,
            'total_new_series': 0,
            'total_new_seasons': 0
        }

        # Analizar cada serie del M3U
        for series_name, m3u_seasons in m3u_structure.items():

            if series_name not in db_structure:
                # Serie completamente nueva
                comparison_results['new_series'].append({
                    'series_name': series_name,
                    'seasons': m3u_seasons,
                    'total_episodes': sum(len(episodes) for episodes in m3u_seasons.values())
                })
                comparison_results['total_new_series'] += 1
                comparison_results['total_new_episodes'] += sum(len(episodes) for episodes in m3u_seasons.values())

            else:
                # Serie existe, comparar temporadas y episodios
                db_seasons = db_structure[series_name]
                series_missing = []
                series_new_seasons = []

                for season_num, m3u_episodes in m3u_seasons.items():

                    if season_num not in db_seasons:
                        # Temporada nueva
                        series_new_seasons.append({
                            'season_num': season_num,
                            'episodes': m3u_episodes,
                            'episode_count': len(m3u_episodes)
                        })
                        comparison_results['total_new_seasons'] += 1
                        comparison_results['total_new_episodes'] += len(m3u_episodes)

                    else:
                        # Temporada existe, comparar episodios
                        db_episodes = db_seasons[season_num]
                        db_episode_nums = {ep['episode_num'] for ep in db_episodes}

                        missing_episodes = []
                        for m3u_episode in m3u_episodes:
                            if m3u_episode['episode_num'] not in db_episode_nums:
                                missing_episodes.append(m3u_episode)

                        if missing_episodes:
                            series_missing.append({
                                'season_num': season_num,
                                'missing_episodes': missing_episodes,
                                'missing_count': len(missing_episodes)
                            })
                            comparison_results['total_new_episodes'] += len(missing_episodes)

                # Agregar a resultados si hay diferencias
                if series_missing:
                    comparison_results['missing_episodes'].append({
                        'series_name': series_name,
                        'missing_by_season': series_missing
                    })

                if series_new_seasons:
                    comparison_results['new_seasons'].append({
                        'series_name': series_name,
                        'new_seasons': series_new_seasons
                    })

                # Si no hay diferencias, está completa
                if not series_missing and not series_new_seasons:
                    comparison_results['complete_series'].append(series_name)

        return comparison_results

    def display_comparison_results(self, m3u_structure, db_structure, comparison_results):
        """Mostrar resultados de la comparación en los paneles"""
        colors = self.current_comparator_window.gemini_colors

        # Limpiar paneles
        self.m3u_text.config(state='normal')
        self.m3u_text.delete(1.0, tk.END)

        self.db_text.config(state='normal')
        self.db_text.delete(1.0, tk.END)

        self.actions_text.config(state='normal')
        self.actions_text.delete(1.0, tk.END)

        # Panel M3U - Mostrar contenido del archivo
        self.m3u_text.insert(tk.END, f"📁 CONTENIDO DEL M3U CARGADO\n", "series")
        self.m3u_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        total_series = len(m3u_structure)
        total_episodes = sum(sum(len(episodes) for episodes in seasons.values()) for seasons in m3u_structure.values())

        self.m3u_text.insert(tk.END, f"📊 RESUMEN:\n", "series")
        self.m3u_text.insert(tk.END, f"   📺 Series: {total_series}\n", "season")
        self.m3u_text.insert(tk.END, f"   🎬 Episodios: {total_episodes}\n\n", "season")

        for series_name, seasons in m3u_structure.items():
            self.m3u_text.insert(tk.END, f"📺 {series_name}\n", "series")

            for season_num in sorted(seasons.keys()):
                episodes = seasons[season_num]
                self.m3u_text.insert(tk.END, f"   └ S{season_num:02d} ({len(episodes)} episodios)\n", "season")

                # Mostrar algunos episodios como ejemplo
                for i, episode in enumerate(episodes[:3]):
                    self.m3u_text.insert(tk.END, f"      └ E{episode['episode_num']:02d}: {episode['title'][:40]}...\n", "episode")

                if len(episodes) > 3:
                    self.m3u_text.insert(tk.END, f"      └ ... y {len(episodes) - 3} más\n", "episode")

            self.m3u_text.insert(tk.END, "\n")

        # Panel BD - Mostrar contenido actual
        self.db_text.insert(tk.END, f"💾 CONTENIDO ACTUAL EN BASE DE DATOS\n", "series")
        self.db_text.insert(tk.END, f"{'='*50}\n\n", "episode")

        db_total_series = len(db_structure)
        db_total_episodes = sum(sum(len(episodes) for episodes in seasons.values()) for seasons in db_structure.values())

        self.db_text.insert(tk.END, f"📊 RESUMEN ACTUAL:\n", "series")
        self.db_text.insert(tk.END, f"   📺 Series: {db_total_series}\n", "season")
        self.db_text.insert(tk.END, f"   🎬 Episodios: {db_total_episodes}\n\n", "season")

        # Mostrar estado de cada serie del M3U en la BD
        for series_name in m3u_structure.keys():
            if series_name in db_structure:
                self.db_text.insert(tk.END, f"📺 {series_name}\n", "series")

                m3u_seasons = m3u_structure[series_name]
                db_seasons = db_structure[series_name]

                for season_num in sorted(m3u_seasons.keys()):
                    m3u_count = len(m3u_seasons[season_num])

                    if season_num in db_seasons:
                        db_count = len(db_seasons[season_num])
                        if db_count == m3u_count:
                            self.db_text.insert(tk.END, f"   ✅ S{season_num:02d} ({db_count}/{m3u_count}) Completa\n", "complete")
                        else:
                            self.db_text.insert(tk.END, f"   ⚠️ S{season_num:02d} ({db_count}/{m3u_count}) Faltan {m3u_count - db_count}\n", "missing")
                    else:
                        self.db_text.insert(tk.END, f"   🆕 S{season_num:02d} (0/{m3u_count}) Nueva temporada\n", "missing")
            else:
                self.db_text.insert(tk.END, f"❌ {series_name} - Serie no existe\n", "missing")

            self.db_text.insert(tk.END, "\n")

        # Panel de acciones - Mostrar resumen de cambios
        self.actions_text.insert(tk.END, f"⚡ RESUMEN DE ACCIONES DETECTADAS\n", "summary")
        self.actions_text.insert(tk.END, f"{'='*60}\n\n", "summary")

        # Estadísticas generales
        self.actions_text.insert(tk.END, f"📊 ESTADÍSTICAS:\n", "summary")
        self.actions_text.insert(tk.END, f"   🆕 Series nuevas: {comparison_results['total_new_series']}\n", "new_series")
        self.actions_text.insert(tk.END, f"   📺 Temporadas nuevas: {comparison_results['total_new_seasons']}\n", "new_season")
        self.actions_text.insert(tk.END, f"   🎬 Episodios a agregar: {comparison_results['total_new_episodes']}\n", "missing_episodes")
        self.actions_text.insert(tk.END, f"   ✅ Series completas: {len(comparison_results['complete_series'])}\n\n", "summary")

        # Detalles de acciones
        if comparison_results['new_series']:
            self.actions_text.insert(tk.END, f"🆕 SERIES NUEVAS ({len(comparison_results['new_series'])}):\n", "new_series")
            for series in comparison_results['new_series']:
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']} ({series['total_episodes']} episodios)\n", "new_series")
            self.actions_text.insert(tk.END, "\n")

        if comparison_results['missing_episodes']:
            self.actions_text.insert(tk.END, f"⚠️ EPISODIOS FALTANTES ({len(comparison_results['missing_episodes'])}):\n", "missing_episodes")
            for series in comparison_results['missing_episodes']:
                total_missing = sum(season['missing_count'] for season in series['missing_by_season'])
                self.actions_text.insert(tk.END, f"   📺 {series['series_name']} (+{total_missing} episodios)\n", "missing_episodes")
            self.actions_text.insert(tk.END, "\n")

        if comparison_results['new_seasons']:
            self.actions_text.insert(tk.END, f"📺 TEMPORADAS NUEVAS ({len(comparison_results['new_seasons'])}):\n", "new_season")
            for series in comparison_results['new_seasons']:
                for season in series['new_seasons']:
                    self.actions_text.insert(tk.END, f"   📺 {series['series_name']} S{season['season_num']:02d} ({season['episode_count']} episodios)\n", "new_season")
            self.actions_text.insert(tk.END, "\n")

        if not comparison_results['total_new_episodes']:
            self.actions_text.insert(tk.END, f"✅ ¡Tu base de datos está actualizada!\n", "summary")
            self.actions_text.insert(tk.END, f"No se detectaron diferencias con el M3U cargado.\n", "summary")

        # Deshabilitar edición
        self.m3u_text.config(state='disabled')
        self.db_text.config(state='disabled')
        self.actions_text.config(state='disabled')

        # Guardar resultados para uso posterior
        self.current_comparison_results = comparison_results
        self.current_m3u_structure = m3u_structure

    def confirm_all_updates(self):
        """Confirmar todas las actualizaciones detectadas"""
        if not hasattr(self, 'current_comparison_results'):
            return

        results = self.current_comparison_results
        total_changes = results['total_new_episodes']

        if total_changes == 0:
            messagebox.showinfo("Información", "No hay cambios para aplicar")
            return

        # Confirmar con el usuario
        message = f"¿Confirmar todas las actualizaciones?\n\n"
        message += f"🆕 Series nuevas: {results['total_new_series']}\n"
        message += f"📺 Temporadas nuevas: {results['total_new_seasons']}\n"
        message += f"🎬 Episodios a agregar: {results['total_new_episodes']}\n\n"
        message += "Esta acción importará todo el contenido nuevo a tu base de datos."

        if messagebox.askyesno("Confirmar Actualizaciones", message):
            self.execute_updates()

    def review_selection(self):
        """Abrir ventana de revisión para selección manual"""
        messagebox.showinfo("Próximamente", "Función de revisión manual en desarrollo")

    def execute_updates(self):
        """Ejecutar las actualizaciones confirmadas"""
        messagebox.showinfo("Próximamente", "Función de ejecución de updates en desarrollo")

    def enrich_m3u_with_tmdb(self, m3u_series_structure):
        """Enriquecer estructura M3U con datos TMDB"""
        print("🧠 Enriqueciendo M3U con datos TMDB...")
        enriched_structure = {}

        try:
            from robust_m3u_importer import RobustM3UImporter
            importer = RobustM3UImporter()

            for series_name, seasons in m3u_series_structure.items():
                print(f"   🔍 Obteniendo TMDB para: {series_name}")

                # Obtener datos TMDB
                tmdb_data = importer.get_tmdb_info(series_name)

                enriched_structure[series_name] = {
                    'seasons': seasons,
                    'tmdb_data': tmdb_data,
                    'tmdb_id': tmdb_data.get('tmdb_id', 0) if tmdb_data else 0,
                    'tmdb_name': tmdb_data.get('title', series_name) if tmdb_data else series_name
                }

        except Exception as e:
            print(f"Error enriqueciendo con TMDB: {e}")
            # Fallback sin TMDB
            for series_name, seasons in m3u_series_structure.items():
                enriched_structure[series_name] = {
                    'seasons': seasons,
                    'tmdb_data': None,
                    'tmdb_id': 0,
                    'tmdb_name': series_name
                }

        print(f"✅ Enriquecimiento completado: {len(enriched_structure)} series")
        return enriched_structure

    def get_db_structure_with_tmdb(self):
        """Obtener estructura de BD con datos TMDB"""
        print("🔍 Obteniendo estructura BD con TMDB...")

        try:
            # Usar la estructura completa existente que ya tiene TMDB
            if hasattr(self, 'db_complete_structure') and self.db_complete_structure:
                print(f"✅ Usando estructura BD existente: {len(self.db_complete_structure)} series")
                return self.db_complete_structure
            else:
                # Si no existe, obtenerla
                print("📥 Obteniendo estructura BD completa...")
                self.db_complete_structure = self.get_complete_db_structure()
                return self.db_complete_structure

        except Exception as e:
            print(f"❌ Error obteniendo estructura BD con TMDB: {e}")
            return {}

    def display_comparison_results_smart(self, m3u_series_with_tmdb, db_structure_with_tmdb, comparison_results):
        """Mostrar resultados de comparación modo inteligente"""
        # Por ahora usar el mismo display que el optimizado
        # Se puede personalizar más adelante para mostrar info específica de TMDB
        self.display_comparison_results_optimized(m3u_series_with_tmdb, db_structure_with_tmdb, comparison_results)

    def create_category_assignment_content(self, window):
        """Crear contenido de la ventana de asignación de categorías"""
        try:
            # Título principal
            title_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            title_frame.pack(fill='x', padx=20, pady=(20, 10))

            title_label = tk.Label(title_frame, text="🏷️ ASIGNACIÓN DE CATEGORÍAS PARA SERIES",
                                 font=('Segoe UI', 16, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
            title_label.pack()

            # Mostrar nombre del archivo
            file_name = os.path.basename(window.m3u_file_path) if hasattr(window, 'm3u_file_path') else "archivo M3U"
            desc_label = tk.Label(title_frame, text=f"Selecciona una categoría para las series del archivo: {file_name}",
                                font=('Segoe UI', 10),
                                fg=self.colors['text_secondary'], bg=self.colors['bg_primary'])
            desc_label.pack(pady=(5, 0))

            # Frame principal con dos columnas
            main_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # Configurar grid
            main_frame.columnconfigure(0, weight=1)
            main_frame.columnconfigure(1, weight=1)
            main_frame.rowconfigure(0, weight=1)

            # Columna izquierda: Categorías disponibles
            self.create_categories_list_panel(main_frame, window)

            # Columna derecha: Series para asignar
            self.create_series_assignment_panel(main_frame, window)

            # Frame de botones
            button_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            button_frame.pack(fill='x', padx=20, pady=(0, 20))

            # Botón cerrar
            close_btn = tk.Button(button_frame, text="❌ Cerrar",
                                command=window.destroy,
                                bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['border'])
            close_btn.pack(side='right', ipady=8, ipadx=20)

            # Botón aplicar
            apply_btn = tk.Button(button_frame, text="✅ Aplicar Asignación",
                                command=lambda: self.apply_category_assignment(window),
                                bg=self.colors['accent'], fg='white',
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['accent_hover'])
            apply_btn.pack(side='right', padx=(0, 10), ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando contenido de categorías: {e}")

    def create_categories_list_panel(self, parent, window):
        """Crear panel de lista de categorías"""
        try:
            # Frame de categorías
            categories_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            categories_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))

            # Título
            cat_title = tk.Label(categories_frame, text="📂 CATEGORÍAS DISPONIBLES",
                               font=('Segoe UI', 12, 'bold'),
                               fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            cat_title.pack(pady=(15, 10))

            # Frame con scroll para categorías
            scroll_frame = tk.Frame(categories_frame, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            # Canvas y scrollbar
            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Cargar y mostrar categorías
            self.load_categories_list(scrollable_frame, window)

        except Exception as e:
            print(f"❌ Error creando panel de categorías: {e}")

    def load_categories_list(self, parent, window):
        """Cargar lista de categorías desde la base de datos"""
        try:
            from category_manager import CategoryManager

            cat_manager = CategoryManager()
            if not cat_manager.connect():
                error_label = tk.Label(parent, text="❌ Error conectando a BD",
                                     fg=self.colors['error'], bg=self.colors['bg_card'])
                error_label.pack(pady=20)
                return

            categories = cat_manager.get_series_categories()

            if not categories:
                no_cat_label = tk.Label(parent, text="⚠️ No se encontraron categorías",
                                      fg=self.colors['warning'], bg=self.colors['bg_card'])
                no_cat_label.pack(pady=20)
                return

            # Variable para categoría seleccionada
            if not hasattr(window, 'selected_category'):
                window.selected_category = tk.IntVar()

            # Crear radio buttons para cada categoría
            for i, category in enumerate(categories):
                cat_frame = tk.Frame(parent, bg=self.colors['bg_card'])
                cat_frame.pack(fill='x', pady=2)

                radio = tk.Radiobutton(cat_frame,
                                     text=f"{category['name']} (ID: {category['id']})",
                                     variable=window.selected_category,
                                     value=category['id'],
                                     font=('Segoe UI', 10),
                                     fg=self.colors['text_primary'],
                                     bg=self.colors['bg_card'],
                                     selectcolor=self.colors['bg_secondary'],
                                     activebackground=self.colors['bg_card'],
                                     activeforeground=self.colors['accent'])
                radio.pack(anchor='w', padx=10, pady=2)

                # Seleccionar la primera por defecto
                if i == 0:
                    window.selected_category.set(category['id'])

        except Exception as e:
            print(f"❌ Error cargando categorías: {e}")
            error_label = tk.Label(parent, text=f"❌ Error: {str(e)}",
                                 fg=self.colors['error'], bg=self.colors['bg_card'])
            error_label.pack(pady=20)

    def create_series_assignment_panel(self, parent, window):
        """Crear panel de series para asignar"""
        try:
            # Frame de series
            series_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            series_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))

            # Título
            series_title = tk.Label(series_frame, text="📺 SERIES DETECTADAS",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            series_title.pack(pady=(15, 10))

            # Frame para mostrar series
            window.series_display_frame = tk.Frame(series_frame, bg=self.colors['bg_card'])
            window.series_display_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            # Cargar series automáticamente si hay archivo
            if hasattr(window, 'm3u_file_path'):
                self.auto_load_series_for_assignment(window)
            else:
                # Mensaje de error si no hay archivo
                error_msg = tk.Label(window.series_display_frame,
                                   text="❌ No se encontró archivo M3U\nPor favor selecciona un archivo en el panel principal",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['error'], bg=self.colors['bg_card'],
                                   justify='center')
                error_msg.pack(expand=True)

        except Exception as e:
            print(f"❌ Error creando panel de series: {e}")

    def auto_load_series_for_assignment(self, window):
        """Cargar series automáticamente del archivo ya seleccionado"""
        try:
            file_path = window.m3u_file_path

            # Mostrar mensaje de carga
            loading_label = tk.Label(window.series_display_frame,
                                   text="⏳ Analizando series del archivo M3U...",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['accent'], bg=self.colors['bg_card'])
            loading_label.pack(expand=True)

            # Procesar en hilo separado
            def process_m3u():
                try:
                    from robust_m3u_importer import RobustM3UImporter

                    importer = RobustM3UImporter()
                    m3u_data = importer.parse_m3u_file_only(file_path)

                    if m3u_data:
                        # Analizar estructura de series
                        series_structure = self.analyze_m3u_series_structure(m3u_data)

                        # Actualizar UI en hilo principal
                        window.after(0, lambda: self.display_series_for_assignment(
                            window.series_display_frame, series_structure, window))
                    else:
                        window.after(0, lambda: self.show_assignment_error(
                            window.series_display_frame, "No se pudieron analizar las series del archivo"))

                except Exception as e:
                    window.after(0, lambda: self.show_assignment_error(
                        window.series_display_frame, f"Error procesando M3U: {str(e)}"))

            import threading
            thread = threading.Thread(target=process_m3u, daemon=True)
            thread.start()

        except Exception as e:
            print(f"❌ Error cargando series automáticamente: {e}")
            self.show_assignment_error(window.series_display_frame, f"Error: {str(e)}")



    def display_series_for_assignment(self, parent, series_structure, window):
        """Mostrar series disponibles para asignación"""
        try:
            # Limpiar frame
            for widget in parent.winfo_children():
                widget.destroy()

            if not series_structure:
                no_series_label = tk.Label(parent, text="⚠️ No se encontraron series en el archivo",
                                         fg=self.colors['warning'], bg=self.colors['bg_card'])
                no_series_label.pack(expand=True)
                return

            # Título
            title_label = tk.Label(parent, text=f"📺 {len(series_structure)} series encontradas:",
                                 font=('Segoe UI', 11, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            title_label.pack(pady=(0, 10))

            # Frame con scroll
            scroll_frame = tk.Frame(parent, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True)

            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Guardar estructura para uso posterior
            window.series_structure = series_structure

            # Mostrar cada serie
            for i, (series_name, series_data) in enumerate(series_structure.items()):
                series_frame = tk.Frame(scrollable_frame, bg=self.colors['bg_secondary'], relief='flat', bd=1)
                series_frame.pack(fill='x', pady=2, padx=5)

                # Nombre de la serie
                name_label = tk.Label(series_frame, text=f"📺 {series_name}",
                                    font=('Segoe UI', 10, 'bold'),
                                    fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
                name_label.pack(anchor='w', padx=10, pady=(5, 2))

                # Información adicional
                episode_count = series_data.get('total_episodes', 0)
                season_count = len(series_data.get('seasons', {}))
                info_label = tk.Label(series_frame, text=f"   📊 {episode_count} episodios en {season_count} temporada(s)",
                                    font=('Segoe UI', 9),
                                    fg=self.colors['text_secondary'], bg=self.colors['bg_secondary'])
                info_label.pack(anchor='w', padx=10, pady=(0, 5))

        except Exception as e:
            print(f"❌ Error mostrando series para asignación: {e}")
            self.show_assignment_error(parent, f"Error mostrando series: {str(e)}")

    def show_assignment_error(self, parent, error_msg):
        """Mostrar error en el panel de asignación"""
        try:
            # Limpiar frame
            for widget in parent.winfo_children():
                widget.destroy()

            error_label = tk.Label(parent, text=f"❌ {error_msg}",
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 wraplength=300, justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error de asignación: {e}")

    def apply_category_assignment(self, window):
        """Aplicar asignación de categoría a las series"""
        try:
            # Verificar que hay categoría seleccionada
            if not hasattr(window, 'selected_category'):
                tk.messagebox.showerror("Error", "No hay categoría seleccionada")
                return

            selected_cat_id = window.selected_category.get()
            if not selected_cat_id:
                tk.messagebox.showerror("Error", "Debe seleccionar una categoría")
                return

            # Verificar que hay series cargadas
            if not hasattr(window, 'series_structure') or not window.series_structure:
                tk.messagebox.showerror("Error", "No hay series cargadas para asignar")
                return

            # Confirmar acción
            from category_manager import CategoryManager
            cat_manager = CategoryManager()
            cat_manager.connect()
            category_name = cat_manager.get_category_name_by_id(selected_cat_id)

            series_count = len(window.series_structure)

            result = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Asignar {series_count} series a la categoría:\n\n"
                f"'{category_name}' (ID: {selected_cat_id})?\n\n"
                f"Esta acción se aplicará cuando se importen las series."
            )

            if result:
                # Guardar asignación para uso en importación
                self.default_category_id = selected_cat_id
                self.log_message(f"✅ Categoría por defecto establecida: {category_name} (ID: {selected_cat_id})")

                tk.messagebox.showinfo(
                    "Asignación Guardada",
                    f"La categoría '{category_name}' se usará como predeterminada\n"
                    f"para las próximas importaciones de series."
                )

                window.destroy()

        except Exception as e:
            print(f"❌ Error aplicando asignación de categoría: {e}")
            tk.messagebox.showerror("Error", f"Error aplicando asignación: {str(e)}")

    def create_bouquet_assignment_content(self, window):
        """Crear contenido de la ventana de asignación de bouquets"""
        try:
            # Título principal
            title_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            title_frame.pack(fill='x', padx=20, pady=(20, 10))

            title_label = tk.Label(title_frame, text="📋 ASIGNACIÓN DE BOUQUETS PARA SERIES",
                                 font=('Segoe UI', 16, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_primary'])
            title_label.pack()

            # Mostrar nombre del archivo
            file_name = os.path.basename(window.m3u_file_path) if hasattr(window, 'm3u_file_path') else "archivo M3U"
            desc_label = tk.Label(title_frame, text=f"Selecciona un bouquet para las series del archivo: {file_name}",
                                font=('Segoe UI', 10),
                                fg=self.colors['text_secondary'], bg=self.colors['bg_primary'])
            desc_label.pack(pady=(5, 0))

            # Frame principal con dos columnas
            main_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # Configurar grid
            main_frame.columnconfigure(0, weight=1)
            main_frame.columnconfigure(1, weight=1)
            main_frame.rowconfigure(0, weight=1)

            # Columna izquierda: Bouquets disponibles
            self.create_bouquets_list_panel(main_frame, window)

            # Columna derecha: Series para asignar
            self.create_series_bouquet_panel(main_frame, window)

            # Frame de botones
            button_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            button_frame.pack(fill='x', padx=20, pady=(0, 20))

            # Botón cerrar
            close_btn = tk.Button(button_frame, text="❌ Cerrar",
                                command=window.destroy,
                                bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['border'])
            close_btn.pack(side='right', ipady=8, ipadx=20)

            # Botón aplicar
            apply_btn = tk.Button(button_frame, text="✅ Aplicar Asignación",
                                command=lambda: self.apply_bouquet_assignment(window),
                                bg='#9b59b6', fg='white',
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground='#8e44ad')
            apply_btn.pack(side='right', padx=(0, 10), ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando contenido de bouquets: {e}")

    def create_bouquets_list_panel(self, parent, window):
        """Crear panel de lista de bouquets"""
        try:
            # Frame de bouquets
            bouquets_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            bouquets_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))

            # Título
            bouquet_title = tk.Label(bouquets_frame, text="📋 BOUQUETS DISPONIBLES",
                                   font=('Segoe UI', 12, 'bold'),
                                   fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            bouquet_title.pack(pady=(15, 10))

            # Frame con scroll para bouquets
            scroll_frame = tk.Frame(bouquets_frame, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            # Canvas y scrollbar
            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Cargar y mostrar bouquets
            self.load_bouquets_list(scrollable_frame, window)

        except Exception as e:
            print(f"❌ Error creando panel de bouquets: {e}")

    def load_bouquets_list(self, parent, window):
        """Cargar lista de bouquets desde la base de datos"""
        try:
            from bouquet_manager import BouquetManager

            bouquet_manager = BouquetManager()
            if not bouquet_manager.connect():
                error_label = tk.Label(parent, text="❌ Error conectando a BD",
                                     fg=self.colors['error'], bg=self.colors['bg_card'])
                error_label.pack(pady=20)
                return

            bouquets = bouquet_manager.get_series_bouquets()

            if not bouquets:
                no_bouquet_label = tk.Label(parent, text="⚠️ No se encontraron bouquets",
                                          fg=self.colors['warning'], bg=self.colors['bg_card'])
                no_bouquet_label.pack(pady=20)
                return

            # Variable para bouquet seleccionado
            if not hasattr(window, 'selected_bouquet'):
                window.selected_bouquet = tk.IntVar()

            # Crear radio buttons para cada bouquet
            for i, bouquet in enumerate(bouquets):
                bouquet_frame = tk.Frame(parent, bg=self.colors['bg_card'])
                bouquet_frame.pack(fill='x', pady=2)

                radio = tk.Radiobutton(bouquet_frame,
                                     text=f"{bouquet['name']} (ID: {bouquet['id']}) - {bouquet['series_count']} series",
                                     variable=window.selected_bouquet,
                                     value=bouquet['id'],
                                     font=('Segoe UI', 10),
                                     fg=self.colors['text_primary'],
                                     bg=self.colors['bg_card'],
                                     selectcolor=self.colors['bg_secondary'],
                                     activebackground=self.colors['bg_card'],
                                     activeforeground=self.colors['accent'],
                                     wraplength=300)
                radio.pack(anchor='w', padx=10, pady=2)

                # Seleccionar el primer bouquet por defecto
                if i == 0:
                    window.selected_bouquet.set(bouquet['id'])

        except Exception as e:
            print(f"❌ Error cargando bouquets: {e}")
            error_label = tk.Label(parent, text=f"❌ Error: {str(e)}",
                                 fg=self.colors['error'], bg=self.colors['bg_card'])
            error_label.pack(pady=20)

    def create_series_bouquet_panel(self, parent, window):
        """Crear panel de series para asignar a bouquet"""
        try:
            # Frame de series
            series_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief='flat', bd=1)
            series_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))

            # Título
            series_title = tk.Label(series_frame, text="📺 SERIES DETECTADAS",
                                  font=('Segoe UI', 12, 'bold'),
                                  fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            series_title.pack(pady=(15, 10))

            # Frame para mostrar series
            window.series_bouquet_frame = tk.Frame(series_frame, bg=self.colors['bg_card'])
            window.series_bouquet_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

            # Cargar series automáticamente si hay archivo
            if hasattr(window, 'm3u_file_path'):
                self.auto_load_series_for_bouquet(window)
            else:
                # Mensaje de error si no hay archivo
                error_msg = tk.Label(window.series_bouquet_frame,
                                   text="❌ No se encontró archivo M3U\nPor favor selecciona un archivo en el panel principal",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['error'], bg=self.colors['bg_card'],
                                   justify='center')
                error_msg.pack(expand=True)

        except Exception as e:
            print(f"❌ Error creando panel de series para bouquet: {e}")

    def auto_load_series_for_bouquet(self, window):
        """Cargar series automáticamente para asignación de bouquet"""
        try:
            file_path = window.m3u_file_path

            # Mostrar mensaje de carga
            loading_label = tk.Label(window.series_bouquet_frame,
                                   text="⏳ Analizando series del archivo M3U...",
                                   font=('Segoe UI', 10),
                                   fg=self.colors['accent'], bg=self.colors['bg_card'])
            loading_label.pack(expand=True)

            # Procesar en hilo separado
            def process_m3u():
                try:
                    from robust_m3u_importer import RobustM3UImporter

                    importer = RobustM3UImporter()
                    m3u_data = importer.parse_m3u_file_only(file_path)

                    if m3u_data:
                        # Analizar estructura de series
                        series_structure = self.analyze_m3u_series_structure(m3u_data)

                        # Actualizar UI en hilo principal
                        window.after(0, lambda: self.display_series_for_bouquet(
                            window.series_bouquet_frame, series_structure, window))
                    else:
                        window.after(0, lambda: self.show_bouquet_error(
                            window.series_bouquet_frame, "No se pudieron analizar las series del archivo"))

                except Exception as e:
                    window.after(0, lambda: self.show_bouquet_error(
                        window.series_bouquet_frame, f"Error procesando M3U: {str(e)}"))

            import threading
            thread = threading.Thread(target=process_m3u, daemon=True)
            thread.start()

        except Exception as e:
            print(f"❌ Error cargando series para bouquet: {e}")
            self.show_bouquet_error(window.series_bouquet_frame, f"Error: {str(e)}")

    def display_series_for_bouquet(self, parent, series_structure, window):
        """Mostrar series disponibles para asignación de bouquet"""
        try:
            # Limpiar frame
            for widget in parent.winfo_children():
                widget.destroy()

            if not series_structure:
                no_series_label = tk.Label(parent, text="⚠️ No se encontraron series en el archivo",
                                         fg=self.colors['warning'], bg=self.colors['bg_card'])
                no_series_label.pack(expand=True)
                return

            # Título
            title_label = tk.Label(parent, text=f"📺 {len(series_structure)} series encontradas:",
                                 font=('Segoe UI', 11, 'bold'),
                                 fg=self.colors['text_primary'], bg=self.colors['bg_card'])
            title_label.pack(pady=(0, 10))

            # Frame con scroll
            scroll_frame = tk.Frame(parent, bg=self.colors['bg_card'])
            scroll_frame.pack(fill='both', expand=True)

            canvas = tk.Canvas(scroll_frame, bg=self.colors['bg_card'], highlightthickness=0)
            scrollbar = tk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_card'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Guardar estructura para uso posterior
            window.series_structure = series_structure

            # Mostrar cada serie
            for i, (series_name, series_data) in enumerate(series_structure.items()):
                series_frame = tk.Frame(scrollable_frame, bg=self.colors['bg_secondary'], relief='flat', bd=1)
                series_frame.pack(fill='x', pady=2, padx=5)

                # Nombre de la serie
                name_label = tk.Label(series_frame, text=f"📺 {series_name}",
                                    font=('Segoe UI', 10, 'bold'),
                                    fg=self.colors['text_primary'], bg=self.colors['bg_secondary'])
                name_label.pack(anchor='w', padx=10, pady=(5, 2))

                # Información adicional
                episode_count = series_data.get('total_episodes', 0)
                season_count = len(series_data.get('seasons', {}))
                info_label = tk.Label(series_frame, text=f"   📊 {episode_count} episodios en {season_count} temporada(s)",
                                    font=('Segoe UI', 9),
                                    fg=self.colors['text_secondary'], bg=self.colors['bg_secondary'])
                info_label.pack(anchor='w', padx=10, pady=(0, 5))

        except Exception as e:
            print(f"❌ Error mostrando series para bouquet: {e}")
            self.show_bouquet_error(parent, f"Error mostrando series: {str(e)}")

    def show_bouquet_error(self, parent, error_msg):
        """Mostrar error en el panel de asignación de bouquet"""
        try:
            # Limpiar frame
            for widget in parent.winfo_children():
                widget.destroy()

            error_label = tk.Label(parent, text=f"❌ {error_msg}",
                                 fg=self.colors['error'], bg=self.colors['bg_card'],
                                 wraplength=300, justify='center')
            error_label.pack(expand=True)

        except Exception as e:
            print(f"❌ Error mostrando error de bouquet: {e}")

    def apply_bouquet_assignment(self, window):
        """Aplicar asignación de bouquet a las series"""
        try:
            # Verificar que hay bouquet seleccionado
            if not hasattr(window, 'selected_bouquet'):
                tk.messagebox.showerror("Error", "No hay bouquet seleccionado")
                return

            selected_bouquet_id = window.selected_bouquet.get()
            if not selected_bouquet_id:
                tk.messagebox.showerror("Error", "Debe seleccionar un bouquet")
                return

            # Verificar que hay series cargadas
            if not hasattr(window, 'series_structure') or not window.series_structure:
                tk.messagebox.showerror("Error", "No hay series cargadas para asignar")
                return

            # Confirmar acción
            from bouquet_manager import BouquetManager
            bouquet_manager = BouquetManager()
            bouquet_manager.connect()
            bouquet_name = bouquet_manager.get_bouquet_name_by_id(selected_bouquet_id)

            series_count = len(window.series_structure)

            result = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Asignar {series_count} series al bouquet:\n\n"
                f"'{bouquet_name}' (ID: {selected_bouquet_id})?\n\n"
                f"Esta acción se aplicará cuando se importen las series."
            )

            if result:
                # Guardar asignación para uso en importación
                self.default_bouquet_id = selected_bouquet_id
                self.log_message(f"✅ Bouquet por defecto establecido: {bouquet_name} (ID: {selected_bouquet_id})")

                tk.messagebox.showinfo(
                    "Asignación Guardada",
                    f"El bouquet '{bouquet_name}' se usará como predeterminado\n"
                    f"para las próximas importaciones de series."
                )

                window.destroy()

        except Exception as e:
            print(f"❌ Error aplicando asignación de bouquet: {e}")
            tk.messagebox.showerror("Error", f"Error aplicando asignación: {str(e)}")

    def open_movie_category_assignment(self):
        """Abrir ventana de asignación de categorías para películas"""
        try:
            # Verificar que hay un archivo M3U cargado
            file_path = self.movie_file_var.get()
            if not file_path:
                tk.messagebox.showwarning(
                    "Archivo Requerido",
                    "Primero debes seleccionar un archivo M3U de películas\n"
                    "antes de asignar categorías."
                )
                return

            if not os.path.exists(file_path):
                tk.messagebox.showerror(
                    "Archivo No Encontrado",
                    "El archivo M3U seleccionado no existe.\n"
                    "Por favor selecciona un archivo válido."
                )
                return

            # Crear ventana de asignación con el archivo ya cargado
            self.create_movie_category_window(file_path)

        except Exception as e:
            print(f"❌ Error abriendo asignación de categorías para películas: {e}")
            self.movie_log_message(f"❌ Error: {str(e)}")

    def open_movie_bouquet_assignment(self):
        """Abrir ventana de asignación de bouquets para películas"""
        try:
            # Verificar que hay un archivo M3U cargado
            file_path = self.movie_file_var.get()
            if not file_path:
                tk.messagebox.showwarning(
                    "Archivo Requerido",
                    "Primero debes seleccionar un archivo M3U de películas\n"
                    "antes de asignar bouquets."
                )
                return

            if not os.path.exists(file_path):
                tk.messagebox.showerror(
                    "Archivo No Encontrado",
                    "El archivo M3U seleccionado no existe.\n"
                    "Por favor selecciona un archivo válido."
                )
                return

            # Crear ventana de asignación con el archivo ya cargado
            self.create_movie_bouquet_window(file_path)

        except Exception as e:
            print(f"❌ Error abriendo asignación de bouquets para películas: {e}")
            self.movie_log_message(f"❌ Error: {str(e)}")

    def create_movie_category_window(self, file_path):
        """Crear ventana de asignación de categorías para películas"""
        try:
            # Crear ventana
            category_window = tk.Toplevel(self.root)
            category_window.title("🏷️ Asignación de Categorías - Películas")
            category_window.geometry("900x700")
            category_window.configure(bg=self.colors['bg_primary'])

            # Hacer modal
            category_window.transient(self.root)
            category_window.grab_set()

            # Centrar ventana
            category_window.update_idletasks()
            x = (category_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (category_window.winfo_screenheight() // 2) - (700 // 2)
            category_window.geometry(f"900x700+{x}+{y}")

            # Guardar referencia al archivo
            category_window.m3u_file_path = file_path

            # Crear contenido
            self.create_movie_category_assignment_content(category_window)

        except Exception as e:
            print(f"❌ Error creando ventana de categorías para películas: {e}")

    def create_movie_bouquet_window(self, file_path):
        """Crear ventana de asignación de bouquets para películas"""
        try:
            # Crear ventana
            bouquet_window = tk.Toplevel(self.root)
            bouquet_window.title("📋 Asignación de Bouquets - Películas")
            bouquet_window.geometry("900x700")
            bouquet_window.configure(bg=self.colors['bg_primary'])

            # Hacer modal
            bouquet_window.transient(self.root)
            bouquet_window.grab_set()

            # Centrar ventana
            bouquet_window.update_idletasks()
            x = (bouquet_window.winfo_screenwidth() // 2) - (900 // 2)
            y = (bouquet_window.winfo_screenheight() // 2) - (700 // 2)
            bouquet_window.geometry(f"900x700+{x}+{y}")

            # Guardar referencia al archivo
            bouquet_window.m3u_file_path = file_path

            # Crear contenido
            self.create_movie_bouquet_assignment_content(bouquet_window)

        except Exception as e:
            print(f"❌ Error creando ventana de bouquets para películas: {e}")

    def create_movie_category_assignment_content(self, window):
        """Crear contenido para asignación de categorías de películas"""
        try:
            # Título
            title_label = tk.Label(window, text="🏷️ Asignación de Categorías para Películas",
                                 font=('Segoe UI', 16, 'bold'),
                                 bg=self.colors['bg_primary'], fg=self.colors['text_primary'])
            title_label.pack(pady=(20, 10))

            # Descripción
            desc_label = tk.Label(window,
                                text="Selecciona una categoría por defecto para todas las películas del archivo M3U",
                                font=('Segoe UI', 10),
                                bg=self.colors['bg_primary'], fg=self.colors['text_secondary'])
            desc_label.pack(pady=(0, 20))

            # Frame principal
            main_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # Frame para categorías
            categories_frame = tk.LabelFrame(main_frame, text="📋 Categorías Disponibles",
                                           font=('Segoe UI', 12, 'bold'),
                                           bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            categories_frame.pack(fill='both', expand=True, pady=(0, 20))

            # Cargar categorías de películas
            self.load_movie_categories_for_assignment(window, categories_frame)

            # Frame de botones
            button_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            button_frame.pack(fill='x', padx=20, pady=(0, 20))

            # Botón cerrar
            close_btn = tk.Button(button_frame, text="❌ Cerrar",
                                command=window.destroy,
                                bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['border'])
            close_btn.pack(side='right', ipady=8, ipadx=20)

            # Botón aplicar
            apply_btn = tk.Button(button_frame, text="✅ Aplicar Asignación",
                                command=lambda: self.apply_movie_category_assignment(window),
                                bg=self.colors['warning'], fg='white',
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground='#e67e22')
            apply_btn.pack(side='right', padx=(0, 10), ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando contenido de categorías para películas: {e}")

    def load_movie_categories_for_assignment(self, window, parent_frame):
        """Cargar categorías de películas para asignación"""
        try:
            from movie_category_manager import MovieCategoryManager

            cat_manager = MovieCategoryManager()
            if not cat_manager.connect():
                error_label = tk.Label(parent_frame, text="❌ Error conectando a la base de datos",
                                     font=('Segoe UI', 12), fg='red',
                                     bg=self.colors['bg_secondary'])
                error_label.pack(pady=20)
                return

            categories = cat_manager.get_movie_categories()

            if not categories:
                error_label = tk.Label(parent_frame,
                                     text="❌ No se encontraron categorías de películas\n"
                                          "Asegúrate de que existen categorías con category_type='movie'",
                                     font=('Segoe UI', 12), fg='red',
                                     bg=self.colors['bg_secondary'])
                error_label.pack(pady=20)
                return

            # Variable para categoría seleccionada
            window.selected_movie_category = tk.IntVar()

            # Crear scrollable frame
            canvas = tk.Canvas(parent_frame, bg=self.colors['bg_secondary'])
            scrollbar = tk.Scrollbar(parent_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_secondary'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Agregar categorías como radio buttons
            for i, category in enumerate(categories):
                cat_frame = tk.Frame(scrollable_frame, bg=self.colors['bg_secondary'])
                cat_frame.pack(fill='x', padx=10, pady=2)

                radio = tk.Radiobutton(cat_frame,
                                     text=f"{category['name']} (ID: {category['id']})",
                                     variable=window.selected_movie_category,
                                     value=category['id'],
                                     font=('Segoe UI', 11),
                                     bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                     selectcolor=self.colors['bg_tertiary'],
                                     activebackground=self.colors['bg_secondary'])
                radio.pack(side='left', fill='x', expand=True)

            canvas.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
            scrollbar.pack(side="right", fill="y", pady=10)

            cat_manager.disconnect()

        except Exception as e:
            print(f"❌ Error cargando categorías de películas: {e}")
            error_label = tk.Label(parent_frame, text=f"❌ Error: {str(e)}",
                                 font=('Segoe UI', 12), fg='red',
                                 bg=self.colors['bg_secondary'])
            error_label.pack(pady=20)

    def apply_movie_category_assignment(self, window):
        """Aplicar asignación de categoría a las películas"""
        try:
            # Verificar que hay categoría seleccionada
            if not hasattr(window, 'selected_movie_category'):
                tk.messagebox.showerror("Error", "No hay categoría seleccionada")
                return

            selected_cat_id = window.selected_movie_category.get()
            if not selected_cat_id:
                tk.messagebox.showerror("Error", "Debe seleccionar una categoría")
                return

            # Confirmar acción
            from movie_category_manager import MovieCategoryManager
            cat_manager = MovieCategoryManager()
            cat_manager.connect()
            category_name = cat_manager.get_category_name_by_id(selected_cat_id)

            result = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Establecer como categoría por defecto para películas:\n\n"
                f"'{category_name}' (ID: {selected_cat_id})?\n\n"
                f"Esta categoría se aplicará a todas las películas importadas."
            )

            if result:
                # Guardar asignación para uso en importación
                self.default_movie_category_id = selected_cat_id
                self.movie_log_message(f"✅ Categoría por defecto establecida: {category_name} (ID: {selected_cat_id})")

                tk.messagebox.showinfo(
                    "Asignación Guardada",
                    f"La categoría '{category_name}' se usará como predeterminada\n"
                    f"para las próximas importaciones de películas."
                )

                window.destroy()

        except Exception as e:
            print(f"❌ Error aplicando asignación de categoría para películas: {e}")
            tk.messagebox.showerror("Error", f"Error aplicando asignación: {str(e)}")

    def create_movie_bouquet_assignment_content(self, window):
        """Crear contenido para asignación de bouquets de películas"""
        try:
            # Título
            title_label = tk.Label(window, text="📋 Asignación de Bouquets para Películas",
                                 font=('Segoe UI', 16, 'bold'),
                                 bg=self.colors['bg_primary'], fg=self.colors['text_primary'])
            title_label.pack(pady=(20, 10))

            # Descripción
            desc_label = tk.Label(window,
                                text="Selecciona un bouquet por defecto para todas las películas del archivo M3U",
                                font=('Segoe UI', 10),
                                bg=self.colors['bg_primary'], fg=self.colors['text_secondary'])
            desc_label.pack(pady=(0, 20))

            # Frame principal
            main_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

            # Frame para bouquets
            bouquets_frame = tk.LabelFrame(main_frame, text="📋 Bouquets Disponibles",
                                         font=('Segoe UI', 12, 'bold'),
                                         bg=self.colors['bg_secondary'], fg=self.colors['text_primary'])
            bouquets_frame.pack(fill='both', expand=True, pady=(0, 20))

            # Cargar bouquets
            self.load_movie_bouquets_for_assignment(window, bouquets_frame)

            # Frame de botones
            button_frame = tk.Frame(window, bg=self.colors['bg_primary'])
            button_frame.pack(fill='x', padx=20, pady=(0, 20))

            # Botón cerrar
            close_btn = tk.Button(button_frame, text="❌ Cerrar",
                                command=window.destroy,
                                bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground=self.colors['border'])
            close_btn.pack(side='right', ipady=8, ipadx=20)

            # Botón aplicar
            apply_btn = tk.Button(button_frame, text="✅ Aplicar Asignación",
                                command=lambda: self.apply_movie_bouquet_assignment(window),
                                bg='#9b59b6', fg='white',
                                font=('Segoe UI', 10, 'bold'), bd=0,
                                activebackground='#8e44ad')
            apply_btn.pack(side='right', padx=(0, 10), ipady=8, ipadx=20)

        except Exception as e:
            print(f"❌ Error creando contenido de bouquets para películas: {e}")

    def load_movie_bouquets_for_assignment(self, window, parent_frame):
        """Cargar bouquets para asignación de películas"""
        try:
            from bouquet_manager import BouquetManager

            bouquet_manager = BouquetManager()
            if not bouquet_manager.connect():
                error_label = tk.Label(parent_frame, text="❌ Error conectando a la base de datos",
                                     font=('Segoe UI', 12), fg='red',
                                     bg=self.colors['bg_secondary'])
                error_label.pack(pady=20)
                return

            bouquets = bouquet_manager.get_movie_bouquets()

            if not bouquets:
                error_label = tk.Label(parent_frame,
                                     text="❌ No se encontraron bouquets disponibles",
                                     font=('Segoe UI', 12), fg='red',
                                     bg=self.colors['bg_secondary'])
                error_label.pack(pady=20)
                return

            # Variable para bouquet seleccionado
            window.selected_movie_bouquet = tk.IntVar()

            # Crear scrollable frame
            canvas = tk.Canvas(parent_frame, bg=self.colors['bg_secondary'])
            scrollbar = tk.Scrollbar(parent_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['bg_secondary'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Agregar bouquets como radio buttons
            for i, bouquet in enumerate(bouquets):
                bouquet_frame = tk.Frame(scrollable_frame, bg=self.colors['bg_secondary'])
                bouquet_frame.pack(fill='x', padx=10, pady=2)

                movies_count = bouquet.get('movies_count', 0)
                radio = tk.Radiobutton(bouquet_frame,
                                     text=f"{bouquet['name']} (ID: {bouquet['id']}) - {movies_count} películas",
                                     variable=window.selected_movie_bouquet,
                                     value=bouquet['id'],
                                     font=('Segoe UI', 11),
                                     bg=self.colors['bg_secondary'], fg=self.colors['text_primary'],
                                     selectcolor=self.colors['bg_tertiary'],
                                     activebackground=self.colors['bg_secondary'])
                radio.pack(side='left', fill='x', expand=True)

            canvas.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
            scrollbar.pack(side="right", fill="y", pady=10)

            bouquet_manager.disconnect()

        except Exception as e:
            print(f"❌ Error cargando bouquets para películas: {e}")
            error_label = tk.Label(parent_frame, text=f"❌ Error: {str(e)}",
                                 font=('Segoe UI', 12), fg='red',
                                 bg=self.colors['bg_secondary'])
            error_label.pack(pady=20)

    def apply_movie_bouquet_assignment(self, window):
        """Aplicar asignación de bouquet a las películas"""
        try:
            # Verificar que hay bouquet seleccionado
            if not hasattr(window, 'selected_movie_bouquet'):
                tk.messagebox.showerror("Error", "No hay bouquet seleccionado")
                return

            selected_bouquet_id = window.selected_movie_bouquet.get()
            if not selected_bouquet_id:
                tk.messagebox.showerror("Error", "Debe seleccionar un bouquet")
                return

            # Confirmar acción
            from bouquet_manager import BouquetManager
            bouquet_manager = BouquetManager()
            bouquet_manager.connect()
            bouquet = bouquet_manager.get_bouquet_by_id(selected_bouquet_id)
            bouquet_name = bouquet['name'] if bouquet else f"ID {selected_bouquet_id}"

            result = tk.messagebox.askyesno(
                "Confirmar Asignación",
                f"¿Establecer como bouquet por defecto para películas:\n\n"
                f"'{bouquet_name}' (ID: {selected_bouquet_id})?\n\n"
                f"Este bouquet se aplicará a todas las películas importadas."
            )

            if result:
                # Guardar asignación para uso en importación
                self.default_movie_bouquet_id = selected_bouquet_id
                self.movie_log_message(f"✅ Bouquet por defecto establecido: {bouquet_name} (ID: {selected_bouquet_id})")

                tk.messagebox.showinfo(
                    "Asignación Guardada",
                    f"El bouquet '{bouquet_name}' se usará como predeterminado\n"
                    f"para las próximas importaciones de películas."
                )

                window.destroy()

        except Exception as e:
            print(f"❌ Error aplicando asignación de bouquet para películas: {e}")
            tk.messagebox.showerror("Error", f"Error aplicando asignación: {str(e)}")

    # ==================== FUNCIONES DE DUPLICADOS DE PELÍCULAS ====================

    def detect_movie_duplicates_manual(self):
        """Detectar películas duplicadas con direct_source=1 y direct_proxy=1 - Modo Manual"""
        self.update_data_display("🔍 DETECTANDO PELÍCULAS DUPLICADAS (MODO MANUAL)...", "header")
        self.update_data_display("Buscando películas con direct_source=1 y direct_proxy=1...", "info")

        def detect_worker():
            try:
                from db_connection import DatabaseConnection
                db = DatabaseConnection()

                if not db.connect():
                    self.update_data_display("❌ Error conectando a la base de datos", "error")
                    return

                # Buscar películas con direct_source=1 y direct_proxy=1
                query = """
                SELECT id, stream_display_name, stream_source, added, tmdb_id,
                       direct_source, direct_proxy
                FROM streams
                WHERE type = 2
                AND direct_source = 1
                AND direct_proxy = 1
                ORDER BY stream_display_name, added DESC
                """

                movies = db.execute_query(query)

                if not movies:
                    self.update_data_display("✅ No se encontraron películas con los criterios especificados", "success")
                    db.disconnect()
                    return

                self.update_data_display(f"📊 Encontradas {len(movies)} películas con direct_source=1 y direct_proxy=1", "info")

                # Agrupar por nombre similar para encontrar duplicados
                duplicates = self.find_movie_duplicates(movies)

                if not duplicates:
                    self.update_data_display("✅ No se encontraron duplicados", "success")
                    db.disconnect()
                    return

                # Mostrar duplicados encontrados
                self.update_data_display(f"\n🎬 DUPLICADOS ENCONTRADOS: {len(duplicates)} grupos", "header")

                for i, group in enumerate(duplicates, 1):
                    self.update_data_display(f"\n--- GRUPO {i} ({len(group)} películas) ---", "warning")

                    for j, movie in enumerate(group, 1):
                        self.update_data_display(f"  {j}. ID: {movie['id']}", "info")
                        self.update_data_display(f"     📽️ Título: {movie['stream_display_name']}", "info")
                        self.update_data_display(f"     📅 Agregada: {movie['added']}", "info")
                        self.update_data_display(f"     🎭 TMDB ID: {movie['tmdb_id'] or 'Sin asignar'}", "info")
                        self.update_data_display(f"     🔗 URL: {movie['stream_source'][:60]}...", "info")
                        self.update_data_display("", "info")

                self.update_data_display("\n💡 Usa 'Limpiar Duplicados' para procesamiento automático", "accent")
                db.disconnect()

            except Exception as e:
                self.update_data_display(f"❌ Error detectando duplicados: {str(e)}", "error")
                print(f"Error en detect_movie_duplicates_manual: {e}")

        # Ejecutar en thread separado
        import threading
        thread = threading.Thread(target=detect_worker)
        thread.daemon = True
        thread.start()

    def clean_movie_duplicates_auto(self):
        """Limpiar películas duplicadas automáticamente - Mantener la más reciente"""
        self.update_data_display("🤖 LIMPIEZA AUTOMÁTICA DE DUPLICADOS...", "header")
        self.update_data_display("Procesando películas duplicadas automáticamente...", "info")

        def clean_worker():
            try:
                from db_connection import DatabaseConnection
                db = DatabaseConnection()

                if not db.connect():
                    self.update_data_display("❌ Error conectando a la base de datos", "error")
                    return

                # Buscar películas con direct_source=1 y direct_proxy=1
                query = """
                SELECT id, stream_display_name, stream_source, added, tmdb_id,
                       direct_source, direct_proxy
                FROM streams
                WHERE type = 2
                AND direct_source = 1
                AND direct_proxy = 1
                ORDER BY stream_display_name, added DESC
                """

                movies = db.execute_query(query)

                if not movies:
                    self.update_data_display("✅ No se encontraron películas para procesar", "success")
                    db.disconnect()
                    return

                self.update_data_display(f"📊 Analizando {len(movies)} películas...", "info")

                # Agrupar por nombre similar para encontrar duplicados
                duplicates = self.find_movie_duplicates(movies)

                if not duplicates:
                    self.update_data_display("✅ No se encontraron duplicados para limpiar", "success")
                    db.disconnect()
                    return

                # Procesar cada grupo de duplicados
                total_removed = 0

                for i, group in enumerate(duplicates, 1):
                    self.update_data_display(f"\n🔄 Procesando grupo {i}/{len(duplicates)}...", "info")

                    # Ordenar por timestamp (más reciente primero)
                    group_sorted = sorted(group, key=lambda x: x['added'], reverse=True)

                    # Mantener el más reciente (primero en la lista)
                    keep_movie = group_sorted[0]
                    to_remove = group_sorted[1:]

                    self.update_data_display(f"  ✅ Mantener: {keep_movie['stream_display_name']} (ID: {keep_movie['id']})", "success")

                    # Eliminar los duplicados más antiguos
                    for movie in to_remove:
                        # Verificar que no sea un symlink antes de eliminar
                        if not self.is_symlink_movie(movie['stream_source']):
                            delete_query = "DELETE FROM streams WHERE id = %s"
                            db.execute_query(delete_query, (movie['id'],), fetch=False)
                            total_removed += 1
                            self.update_data_display(f"  🗑️ Eliminado: {movie['stream_display_name']} (ID: {movie['id']})", "warning")
                        else:
                            self.update_data_display(f"  🔗 Symlink preservado: {movie['stream_display_name']} (ID: {movie['id']})", "accent")

                self.update_data_display(f"\n✅ LIMPIEZA COMPLETADA", "header")
                self.update_data_display(f"📊 Películas eliminadas: {total_removed}", "success")
                self.update_data_display(f"📊 Grupos procesados: {len(duplicates)}", "info")

                db.disconnect()

            except Exception as e:
                self.update_data_display(f"❌ Error en limpieza automática: {str(e)}", "error")
                print(f"Error en clean_movie_duplicates_auto: {e}")

        # Ejecutar en thread separado
        import threading
        thread = threading.Thread(target=clean_worker)
        thread.daemon = True
        thread.start()

    def find_movie_duplicates(self, movies):
        """Encontrar grupos de películas duplicadas basado en similitud de nombres"""
        import re
        from difflib import SequenceMatcher

        def normalize_title(title):
            """Normalizar título para comparación"""
            if not title:
                return ""

            # Convertir a minúsculas
            normalized = title.lower().strip()

            # Remover información de calidad
            normalized = re.sub(r'\b(hd|4k|1080p|720p|480p|bluray|web-dl|webrip|dvdrip|cam|ts)\b', '', normalized)

            # Remover información de audio
            normalized = re.sub(r'\b(dts|ac3|aac|mp3|dolby|atmos)\b', '', normalized)

            # Remover información de codec
            normalized = re.sub(r'\b(x264|x265|h264|h265|hevc|avc)\b', '', normalized)

            # Remover años entre paréntesis
            normalized = re.sub(r'\(\d{4}\)', '', normalized)

            # Remover caracteres especiales
            normalized = re.sub(r'[^\w\s]', ' ', normalized)

            # Remover espacios múltiples
            normalized = re.sub(r'\s+', ' ', normalized).strip()

            return normalized

        def calculate_similarity(title1, title2):
            """Calcular similitud entre dos títulos"""
            norm1 = normalize_title(title1)
            norm2 = normalize_title(title2)

            if not norm1 or not norm2:
                return 0.0

            return SequenceMatcher(None, norm1, norm2).ratio()

        duplicates = []
        processed = set()
        similarity_threshold = 0.85

        for i, movie1 in enumerate(movies):
            if movie1['id'] in processed:
                continue

            movie_group = [movie1]
            processed.add(movie1['id'])

            # Comparar con el resto
            for j, movie2 in enumerate(movies[i+1:], i+1):
                if movie2['id'] in processed:
                    continue

                similarity = calculate_similarity(
                    movie1['stream_display_name'],
                    movie2['stream_display_name']
                )

                if similarity >= similarity_threshold:
                    movie_group.append(movie2)
                    processed.add(movie2['id'])

            # Si hay más de una película en el grupo, son duplicados
            if len(movie_group) > 1:
                duplicates.append(movie_group)

        return duplicates

    def is_symlink_movie(self, stream_source):
        """Verificar si una película es un symlink (simplificado)"""
        # Implementación básica - puede mejorarse según la lógica específica
        # Por ahora, asumimos que los symlinks tienen ciertos patrones en la URL
        if not stream_source:
            return False

        # Patrones que podrían indicar symlinks
        symlink_patterns = [
            'symlink',
            'link',
            'redirect',
            'proxy'
        ]

        stream_lower = stream_source.lower()
        return any(pattern in stream_lower for pattern in symlink_patterns)

def main():
    """Función principal"""
    try:
        app = XUIManagerFinal()
        app.run()
    except Exception as e:
        print(f"Error iniciando la aplicación: {e}")

if __name__ == "__main__":
    main()
