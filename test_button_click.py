#!/usr/bin/env python3
"""
🔍 SIMULACIÓN DE CLICK EN BOTÓN DUPLICADOS MANUAL
Simula exactamente lo que debería pasar cuando haces clic
"""

import sys
import os
import threading
import time

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_button_click():
    """Simular el click en el botón de duplicados manual"""
    print("🔍 SIMULANDO CLICK EN BOTÓN 'DUPLICADOS MANUAL'")
    print("=" * 60)
    
    try:
        # Importar la clase principal
        from xui_manager_final import XUIManagerFinal
        
        # Crear una instancia mínima (sin UI completa)
        print("📱 Creando instancia de XUIManagerFinal...")
        app = XUIManagerFinal()
        
        # Simular el panel de datos como una lista simple
        app.data_messages = []
        
        # Redefinir update_data_display para capturar mensajes
        def mock_update_data_display(message, tag=None):
            app.data_messages.append(f"[{tag or 'info'}] {message}")
            print(f"📝 MENSAJE: [{tag or 'info'}] {message}")
        
        app.update_data_display = mock_update_data_display
        
        print("✅ Instancia creada correctamente")
        print("\n🔍 EJECUTANDO detect_movie_duplicates_manual()...")
        print("-" * 40)
        
        # Llamar a la función directamente
        app.detect_movie_duplicates_manual()
        
        # Esperar un poco para que el thread termine
        print("\n⏳ Esperando que termine el procesamiento...")
        time.sleep(5)
        
        print("\n📋 MENSAJES CAPTURADOS:")
        print("-" * 40)
        
        if app.data_messages:
            for i, message in enumerate(app.data_messages, 1):
                print(f"{i:2d}. {message}")
        else:
            print("❌ NO SE CAPTURARON MENSAJES")
            print("💡 Esto indica que hay un problema en la función")
        
        return len(app.data_messages) > 0
        
    except Exception as e:
        print(f"❌ ERROR EN SIMULACIÓN: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_directly():
    """Probar la consulta de base de datos directamente"""
    print("\n🎯 PROBANDO CONSULTA DIRECTA A BASE DE DATOS")
    print("=" * 60)
    
    try:
        from db_connection import DatabaseConnection
        
        db = DatabaseConnection()
        if not db.connect():
            print("❌ Error conectando a la base de datos")
            return False
        
        print("✅ Conexión exitosa")
        
        # Consulta exacta de la función
        query = """
        SELECT id, stream_display_name, stream_source, added, tmdb_id, 
               direct_source, direct_proxy, movie_symlink
        FROM streams 
        WHERE type = 2 
        AND direct_source = 1 
        AND direct_proxy = 1
        ORDER BY stream_display_name, added DESC
        LIMIT 10
        """
        
        print("🔍 Ejecutando consulta...")
        movies = db.execute_query(query)
        
        if movies:
            print(f"✅ Consulta exitosa - {len(movies)} películas encontradas")
            
            # Mostrar algunas películas
            print("\n📽️ PELÍCULAS ENCONTRADAS:")
            for i, movie in enumerate(movies[:5], 1):
                print(f"{i:2d}. ID: {movie['id']}")
                print(f"    Título: {movie['stream_display_name'][:60]}...")
                print(f"    Agregada: {movie['added']}")
                print(f"    Symlink: {movie.get('movie_symlink', 'N/A')}")
                print()
            
            # Probar la función de duplicados
            print("🔍 Probando detección de duplicados...")
            from xui_manager_final import XUIManagerFinal
            app = XUIManagerFinal()
            
            duplicates = app.find_movie_duplicates(movies)
            
            if duplicates:
                print(f"🎯 DUPLICADOS ENCONTRADOS: {len(duplicates)} grupos")
                for i, group in enumerate(duplicates, 1):
                    print(f"  Grupo {i}: {len(group)} películas")
                    for movie in group:
                        print(f"    - {movie['stream_display_name'][:50]}...")
            else:
                print("ℹ️ No se encontraron duplicados en esta muestra")
                print("💡 Esto es normal si las películas tienen nombres muy diferentes")
            
        else:
            print("⚠️ No se encontraron películas")
        
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 TEST DE FUNCIONALIDAD DE BOTÓN DUPLICADOS")
    print("=" * 60)
    
    # Test 1: Simular click en botón
    button_ok = simulate_button_click()
    
    # Test 2: Probar consulta directa
    db_ok = test_database_directly()
    
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE PRUEBAS:")
    print(f"🔘 Simulación de botón: {'✅ OK' if button_ok else '❌ FALLO'}")
    print(f"🎯 Consulta directa: {'✅ OK' if db_ok else '❌ FALLO'}")
    
    if button_ok and db_ok:
        print("\n🎉 TODO FUNCIONA - El problema puede estar en la UI")
        print("💡 Verifica que estés haciendo clic en el botón correcto")
    elif db_ok and not button_ok:
        print("\n⚠️ LA CONSULTA FUNCIONA PERO EL BOTÓN NO")
        print("💡 Hay un problema en la función detect_movie_duplicates_manual")
    else:
        print("\n❌ HAY PROBLEMAS FUNDAMENTALES")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
