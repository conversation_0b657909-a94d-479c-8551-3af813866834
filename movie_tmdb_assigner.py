#!/usr/bin/env python3
"""
🎬 MOVIE TMDB ASSIGNER
Asignador de TMDB para películas sin metadatos
Busca y asigna información de TMDB automática o manualmente
"""

from db_connection import DatabaseConnection
import requests
import json
import time
import re

class MovieTMDBAssigner:
    def __init__(self):
        self.db = DatabaseConnection()
        self.tmdb_api_key = "201066b4b17391d478e55247f43eed64"
        self.tmdb_base_url = "https://api.themoviedb.org/3"
        
    def connect(self):
        """Conectar a la base de datos"""
        return self.db.connect()
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        if self.db:
            self.db.disconnect()
    
    def find_movies_without_tmdb(self, limit=100):
        """Encontrar películas sin TMDB ID"""
        try:
            query = """
            SELECT 
                s.id,
                s.stream_display_name as title,
                s.stream_icon as poster_url,
                s.added,
                s.category_id,
                s.tmdb_id
            FROM streams s
            WHERE s.type = 2 
            AND (s.tmdb_id IS NULL OR s.tmdb_id = 0 OR s.tmdb_id = '')
            ORDER BY s.added DESC, s.stream_display_name
            LIMIT %s
            """
            
            movies_without_tmdb = self.db.execute_query(query, (limit,))
            
            # Procesar resultados para extraer año del título si es posible
            processed_movies = []
            for movie in movies_without_tmdb or []:
                processed_movie = dict(movie)
                
                # Intentar extraer año del título
                title = movie['title']
                year_match = re.search(r'\((\d{4})\)', title)
                if year_match:
                    processed_movie['year'] = int(year_match.group(1))
                    processed_movie['clean_title'] = re.sub(r'\s*\(\d{4}\)\s*', '', title).strip()
                else:
                    processed_movie['year'] = None
                    processed_movie['clean_title'] = title
                
                processed_movies.append(processed_movie)
            
            return processed_movies
            
        except Exception as e:
            print(f"❌ Error buscando películas sin TMDB: {e}")
            return []
    
    def generate_title_variations(self, title):
        """Generar variaciones del título para búsqueda"""
        variations = [title]
        
        # Limpiar título
        clean_title = re.sub(r'[^\w\s]', ' ', title)
        clean_title = re.sub(r'\s+', ' ', clean_title).strip()
        if clean_title != title:
            variations.append(clean_title)
        
        # Remover palabras comunes
        common_words = ['la', 'el', 'los', 'las', 'de', 'del', 'y', 'e', 'o', 'u', 'the', 'a', 'an', 'and', 'or']
        words = clean_title.lower().split()
        filtered_words = [w for w in words if w not in common_words]
        if len(filtered_words) != len(words):
            variations.append(' '.join(filtered_words))
        
        # Remover duplicados manteniendo orden
        seen = set()
        unique_variations = []
        for var in variations:
            if var.lower() not in seen:
                seen.add(var.lower())
                unique_variations.append(var)
        
        return unique_variations[:3]  # Máximo 3 variaciones
    
    def search_tmdb_movies(self, title, year=None):
        """Buscar película en TMDB con múltiples idiomas y variaciones de título"""
        try:
            # Generar variaciones del título para búsqueda
            search_variations = self.generate_title_variations(title)

            # Idiomas a probar en orden de prioridad
            languages = ['es-ES', 'es-MX', 'en-US']
            all_results = []
            seen_ids = set()

            print(f"🔍 Buscando película '{title}' con {len(search_variations)} variaciones...")

            for variation in search_variations:
                print(f"   🔤 Variación: '{variation}'")

                for lang in languages:
                    try:
                        # Construir URL de búsqueda
                        search_url = f"{self.tmdb_base_url}/search/movie"
                        params = {
                            'api_key': self.tmdb_api_key,
                            'query': variation,
                            'language': lang
                        }

                        if year:
                            params['year'] = year

                        response = requests.get(search_url, params=params, timeout=10)

                        if response.status_code == 200:
                            data = response.json()
                            results = data.get('results', [])

                            # Filtrar duplicados por ID y agregar información de búsqueda
                            for result in results:
                                if result['id'] not in seen_ids:
                                    result['search_language'] = lang
                                    result['search_variation'] = variation
                                    all_results.append(result)
                                    seen_ids.add(result['id'])

                            if results:
                                print(f"      ✅ {len(results)} resultados en {lang}")
                        else:
                            print(f"      ⚠️ Error API: {response.status_code}")

                        # Pausa entre requests para respetar rate limit
                        time.sleep(0.25)

                    except Exception as e:
                        print(f"      ❌ Error en {lang}: {e}")
                        continue

                # Si ya encontramos resultados con esta variación, no necesitamos más
                if len(all_results) >= 10:
                    break

            print(f"   📊 Total únicos encontrados: {len(all_results)}")

            # Ordenar resultados por popularidad/rating
            all_results.sort(key=lambda x: x.get('vote_average', 0), reverse=True)

            return all_results

        except Exception as e:
            print(f"❌ Error buscando en TMDB: {e}")
            return []
    
    def get_tmdb_movie_details(self, tmdb_id, preferred_language='es-ES'):
        """Obtener detalles completos de una película de TMDB con idioma preferido"""
        try:
            # Intentar con idioma preferido primero
            languages_to_try = [preferred_language]

            # Agregar otros idiomas como fallback
            fallback_languages = ['es-ES', 'es-MX', 'en-US']
            for lang in fallback_languages:
                if lang not in languages_to_try:
                    languages_to_try.append(lang)

            for lang in languages_to_try:
                try:
                    print(f"   📊 Obteniendo detalles en {lang}...")

                    details_url = f"{self.tmdb_base_url}/movie/{tmdb_id}"
                    params = {
                        'api_key': self.tmdb_api_key,
                        'language': lang
                    }

                    response = requests.get(details_url, params=params, timeout=10)

                    if response.status_code == 200:
                        details = response.json()

                        # Verificar que tenga información útil
                        if details.get('title') and details.get('overview'):
                            print(f"      ✅ Detalles obtenidos en {lang}")
                            details['details_language'] = lang
                            return details
                        elif details.get('title'):
                            # Al menos tiene título, usar como fallback
                            print(f"      ⚠️ Detalles parciales en {lang}")
                            details['details_language'] = lang
                            # Continuar buscando mejor opción, pero guardar esta
                            fallback_details = details
                    else:
                        print(f"      ❌ Error API: {response.status_code}")

                    time.sleep(0.25)  # Rate limiting

                except Exception as e:
                    print(f"      ❌ Error en idioma {lang}: {e}")
                    continue

            # Si llegamos aquí, usar fallback si existe
            if 'fallback_details' in locals():
                return fallback_details

            return None

        except Exception as e:
            print(f"❌ Error obteniendo detalles: {e}")
            return None
    
    def assign_tmdb_to_movie(self, movie_id, tmdb_data):
        """Asignar datos TMDB a una película"""
        try:
            # Preparar datos para actualizar
            update_data = {
                'tmdb_id': tmdb_data.get('id'),
                'plot': tmdb_data.get('overview', '')[:1000],  # Limitar longitud
                'rating': round(tmdb_data.get('vote_average', 0), 1),
                'release_date': tmdb_data.get('release_date', ''),
                'genre': ', '.join([g['name'] for g in tmdb_data.get('genres', [])[:3]]),
                'runtime': tmdb_data.get('runtime', 0) or 0
            }

            # Manejar imágenes
            poster_path = tmdb_data.get('poster_path')
            backdrop_path = tmdb_data.get('backdrop_path')
            
            if poster_path:
                update_data['poster_url'] = f"https://image.tmdb.org/t/p/w500{poster_path}"
                update_data['poster_url_big'] = f"https://image.tmdb.org/t/p/w780{poster_path}"
            else:
                update_data['poster_url'] = ''
                update_data['poster_url_big'] = ''
            
            if backdrop_path:
                update_data['backdrop_path'] = f"https://image.tmdb.org/t/p/w1280{backdrop_path}"
            else:
                update_data['backdrop_path'] = ''
            
            # Construir query de actualización
            update_query = """
            UPDATE streams SET 
                tmdb_id = %s,
                stream_info = %s,
                rating = %s,
                release_date = %s,
                genre = %s,
                backdrop_path = %s,
                stream_icon = %s,
                runtime = %s
            WHERE id = %s AND type = 2
            """
            
            values = (
                update_data['tmdb_id'],
                update_data['plot'],
                update_data['rating'],
                update_data['release_date'],
                update_data['genre'],
                update_data['backdrop_path'],
                update_data['poster_url'],
                update_data['runtime'],
                movie_id
            )
            
            self.db.cursor.execute(update_query, values)
            self.db.connection.commit()
            
            return True
            
        except Exception as e:
            print(f"❌ Error asignando TMDB: {e}")
            print(f"   📋 Movie ID: {movie_id}")
            print(f"   📋 TMDB ID: {tmdb_data.get('id', 'N/A')}")
            print(f"   📋 Título: {tmdb_data.get('title', 'N/A')}")
            import traceback
            traceback.print_exc()
            self.db.connection.rollback()
            return False
    
    def get_tmdb_statistics(self):
        """Obtener estadísticas de TMDB para películas"""
        try:
            # Total de películas
            total_query = "SELECT COUNT(*) as total FROM streams WHERE type = 2"
            total_result = self.db.execute_query(total_query)
            total_movies = total_result[0]['total'] if total_result else 0
            
            # Películas con TMDB
            with_tmdb_query = """
            SELECT COUNT(*) as with_tmdb 
            FROM streams 
            WHERE type = 2 AND tmdb_id IS NOT NULL AND tmdb_id != 0 AND tmdb_id != ''
            """
            with_tmdb_result = self.db.execute_query(with_tmdb_query)
            with_tmdb = with_tmdb_result[0]['with_tmdb'] if with_tmdb_result else 0
            
            # Calcular estadísticas
            without_tmdb = total_movies - with_tmdb
            tmdb_percentage = (with_tmdb / total_movies * 100) if total_movies > 0 else 0
            
            return {
                'total_movies': total_movies,
                'with_tmdb': with_tmdb,
                'without_tmdb': without_tmdb,
                'tmdb_percentage': tmdb_percentage
            }
            
        except Exception as e:
            print(f"❌ Error obteniendo estadísticas: {e}")
            return {
                'total_movies': 0,
                'with_tmdb': 0,
                'without_tmdb': 0,
                'tmdb_percentage': 0
            }

    def auto_assign_tmdb(self, movies_list, max_requests=50):
        """Asignación automática de TMDB para múltiples películas"""
        assigned_count = 0
        skipped_count = 0
        error_count = 0

        print(f"🤖 Iniciando asignación automática para {len(movies_list)} películas...")
        print(f"⚠️ Límite de requests: {max_requests}")

        for i, movie in enumerate(movies_list[:max_requests], 1):
            try:
                movie_id = movie['id']
                title = movie['clean_title']
                year = movie['year']

                print(f"\n{i}/{min(len(movies_list), max_requests)} - 🔍 Buscando: {title}")

                # Buscar en TMDB
                search_results = self.search_tmdb_movies(title, year)

                if search_results:
                    # Tomar el primer resultado (más relevante)
                    best_match = search_results[0]
                    tmdb_id = best_match['id']
                    match_title = best_match.get('title', 'Sin título')
                    match_year = best_match.get('release_date', '')[:4] if best_match.get('release_date') else 'N/A'
                    search_lang = best_match.get('search_language', 'es-ES')

                    print(f"   ✅ Encontrado: {match_title} ({match_year}) - TMDB ID: {tmdb_id} [{search_lang}]")

                    # Obtener detalles completos usando el idioma de búsqueda
                    tmdb_details = self.get_tmdb_movie_details(tmdb_id, search_lang)

                    if tmdb_details:
                        print(f"   📊 Metadatos obtenidos: {tmdb_details.get('title', 'N/A')}")
                        print(f"      📅 Año: {tmdb_details.get('release_date', 'N/A')[:4]}")
                        print(f"      ⭐ Rating: {tmdb_details.get('vote_average', 0)}")
                        print(f"      🎭 Géneros: {len(tmdb_details.get('genres', []))}")

                        # Asignar a la película
                        if self.assign_tmdb_to_movie(movie_id, tmdb_details):
                            assigned_count += 1
                            print(f"   ✅ Asignado exitosamente con metadatos completos")
                        else:
                            error_count += 1
                            print(f"   ❌ Error asignando a base de datos")
                    else:
                        error_count += 1
                        print(f"   ❌ Error obteniendo detalles de TMDB")
                else:
                    skipped_count += 1
                    print(f"   ⚠️ No encontrado en TMDB")

                # Pausa para respetar rate limit de TMDB
                time.sleep(0.25)  # 4 requests por segundo

            except Exception as e:
                error_count += 1
                print(f"   ❌ Error procesando: {e}")

        return {
            'assigned': assigned_count,
            'skipped': skipped_count,
            'errors': error_count,
            'total_processed': min(len(movies_list), max_requests)
        }

    def get_movie_details(self, movie_id):
        """Obtener detalles de una película específica"""
        try:
            query = """
            SELECT * FROM streams
            WHERE id = %s AND type = 2
            """

            result = self.db.execute_query(query, (movie_id,))
            return result[0] if result else None

        except Exception as e:
            print(f"❌ Error obteniendo detalles: {e}")
            return None

def main():
    """Función principal para pruebas"""
    print("🎬 MOVIE TMDB ASSIGNER")
    print("=" * 50)
    
    assigner = MovieTMDBAssigner()
    
    if not assigner.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    try:
        # Obtener estadísticas
        stats = assigner.get_tmdb_statistics()
        print(f"📊 ESTADÍSTICAS TMDB PELÍCULAS:")
        print(f"   🎬 Total películas: {stats['total_movies']}")
        print(f"   ✅ Con TMDB: {stats['with_tmdb']}")
        print(f"   ❌ Sin TMDB: {stats['without_tmdb']}")
        print(f"   📈 Porcentaje TMDB: {stats['tmdb_percentage']:.1f}%")
        
        # Buscar películas sin TMDB
        movies_without_tmdb = assigner.find_movies_without_tmdb(5)
        
        if movies_without_tmdb:
            print(f"\n🔍 PELÍCULAS SIN TMDB (primeras 5):")
            
            for i, movie in enumerate(movies_without_tmdb, 1):
                year_info = f" ({movie['year']})" if movie['year'] else ""
                print(f"   {i}. {movie['clean_title']}{year_info}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        assigner.disconnect()

if __name__ == "__main__":
    main()
