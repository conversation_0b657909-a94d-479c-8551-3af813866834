#!/usr/bin/env python3
"""
Test script para verificar las mejoras de compactación en herramientas administrativas
"""

import tkinter as tk
from tkinter import ttk

def setup_colors():
    """Configurar colores estilo NVIDIA + Gemini"""
    return {
        'bg_primary': '#0d1117',        # Negro GitHub
        'bg_secondary': '#161b22',      # Gris muy oscuro
        'bg_tertiary': '#21262d',       # Gris oscuro
        'bg_card': '#1c2128',           # Cards oscuros
        'accent': '#00d4ff',            # <PERSON> Gemini
        'accent_hover': '#00b8e6',      # Celeste más oscuro
        'nvidia_green': '#76b900',      # Verde NVIDIA
        'nvidia_green_hover': '#5a8a00', # Verde NVIDIA hover
        'text_primary': '#f0f6fc',      # Blanco suave
        'text_secondary': '#8b949e',    # <PERSON><PERSON> claro
        'success': '#238636',           # Verde éxito
        'warning': '#d29922',           # <PERSON>illo
        'error': '#f85149',             # Rojo
        'border': '#76b900',            # Bordes verdes NVIDIA
        'border_focus': '#00d4ff'       # Bordes activos celestes
    }

def create_compact_admin_tools():
    """Crear herramientas administrativas compactas"""
    root = tk.Tk()
    root.title("🔧 Test: Herramientas Administrativas Ultra Compactas")
    root.geometry("200x800")
    root.configure(bg='#0d1117')
    
    colors = setup_colors()
    
    # Frame principal con scroll
    main_frame = tk.Frame(root, bg=colors['bg_primary'])
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Canvas y scrollbar
    canvas = tk.Canvas(main_frame, bg=colors['bg_primary'], highlightthickness=0)
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg=colors['bg_primary'])
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # Título compacto
    title_container = tk.Frame(scrollable_frame, bg=colors['bg_card'])
    title_container.pack(fill='x', pady=(8, 10))
    
    title = tk.Label(title_container, text="🔧 HERRAMIENTAS",
                    font=('Segoe UI', 12, 'bold'),
                    fg=colors['nvidia_green'], bg=colors['bg_card'])
    title.pack()
    
    # Línea decorativa
    title_line = tk.Frame(title_container, height=2, bg=colors['nvidia_green'])
    title_line.pack(fill='x', pady=(3, 0))
    
    # Configuración de botones ultra compactos
    button_config = {
        'font': ('Segoe UI', 8, 'bold'),
        'bd': 1,
        'relief': 'solid',
        'width': 16,
        'pady': 3
    }
    
    # Botones de herramientas compactos
    buttons_data = [
        ("🔍 Episodios Huérfanos", colors['accent'], colors['bg_primary']),
        ("👻 Episodios Fantasmas", colors['warning'], colors['bg_primary']),
        ("🔄 Episodios Duplicados", colors['error'], colors['text_primary']),
        ("🧹 Procesar Duplicados", colors['nvidia_green'], colors['bg_primary']),
        ("🏠 Asignar Series", colors['success'], colors['text_primary']),
        ("📊 Cargar TMDB", colors['bg_card'], colors['text_primary']),
        ("🤖 Auto TMDB", colors['accent'], colors['bg_primary']),
        ("🔍 Manual TMDB", colors['warning'], colors['bg_primary']),
    ]
    
    for text, bg_color, fg_color in buttons_data:
        btn = tk.Button(scrollable_frame, text=text,
                       bg=bg_color, fg=fg_color,
                       activebackground=bg_color,
                       highlightbackground=bg_color,
                       **button_config)
        btn.pack(pady=2, padx=5, fill='x')

    # Separador compacto
    separator = tk.Frame(scrollable_frame, height=2, bg=colors['nvidia_green'])
    separator.pack(fill='x', padx=5, pady=8)

    # Botón limpiar
    clear_btn = tk.Button(scrollable_frame, text="🧹 Limpiar",
                         bg=colors['bg_card'], fg=colors['text_primary'],
                         activebackground=colors['bg_tertiary'],
                         highlightbackground=colors['nvidia_green'],
                         **button_config)
    clear_btn.pack(pady=2, padx=5, fill='x')

    # Separador
    separator2 = tk.Frame(scrollable_frame, height=2, bg=colors['nvidia_green'])
    separator2.pack(fill='x', padx=5, pady=8)
    
    # Sección películas compacta
    movies_container = tk.Frame(scrollable_frame, bg=colors['bg_card'])
    movies_container.pack(fill='x', pady=(5, 3))
    
    movies_label = tk.Label(movies_container, text="🎬 PELÍCULAS",
                           font=('Segoe UI', 10, 'bold'),
                           fg=colors['accent'], bg=colors['bg_card'])
    movies_label.pack()
    
    movies_line = tk.Frame(movies_container, height=2, bg=colors['accent'])
    movies_line.pack(fill='x', pady=(2, 0))
    
    # Botones de películas
    movies_buttons = [
        ("🎬 Películas sin TMDB", colors['error'], colors['text_primary']),
        ("🤖 Auto TMDB Movies", colors['nvidia_green'], colors['bg_primary']),
        ("🔍 Manual TMDB Movies", colors['warning'], colors['bg_primary']),
    ]
    
    for text, bg_color, fg_color in movies_buttons:
        btn = tk.Button(scrollable_frame, text=text,
                       bg=bg_color, fg=fg_color,
                       activebackground=bg_color,
                       highlightbackground=bg_color,
                       **button_config)
        btn.pack(pady=2, padx=5, fill='x')

    # Separador final
    separator3 = tk.Frame(scrollable_frame, height=2, bg=colors['nvidia_green'])
    separator3.pack(fill='x', padx=5, pady=8)

    # Botón volver compacto
    back_btn = tk.Button(scrollable_frame, text="🔙 Panel Principal",
                        bg=colors['nvidia_green'], fg=colors['bg_primary'],
                        font=('Segoe UI', 9, 'bold'),
                        activebackground=colors['nvidia_green_hover'],
                        relief='solid', bd=2,
                        highlightbackground=colors['nvidia_green'],
                        pady=5)
    back_btn.pack(pady=8, padx=5, fill='x')
    
    # Scroll con mouse
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    print("🔧✨ HERRAMIENTAS ADMINISTRATIVAS ULTRA COMPACTAS")
    print("=" * 55)
    print("✅ Verificaciones:")
    print("   🎯 Espaciado ultra reducido (padx=5)")
    print("   📏 Botones más pequeños (width=16)")
    print("   📐 Columna más estrecha (140px)")
    print("   🎨 Colores NVIDIA + Gemini")
    print("   📦 Layout ultra compacto")
    print("   🖱️ Scroll funcional")
    print("   ⚡ Sin threads largos")
    print("   🚀 Máximo aprovechamiento del espacio")
    print("\n🎯 Ventana abierta - Verifica la ultra compactación")
    
    return root

if __name__ == "__main__":
    root = create_compact_admin_tools()
    root.mainloop()
