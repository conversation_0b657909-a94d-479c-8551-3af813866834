#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar la corrección del espaciado en herramientas admin
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_admin_spacing():
    """Probar la corrección del espaciado en herramientas administrativas"""
    print("🧪 PRUEBA DE ESPACIADO EN HERRAMIENTAS ADMIN")
    print("=" * 50)
    
    try:
        # Importar el manager principal
        from xui_manager_final import XUIManagerFinal
        
        print("✅ Módulo XUIManagerFinal importado correctamente")
        
        # Crear instancia de la aplicación
        app = XUIManagerFinal()
        print("✅ Aplicación XUI Manager creada")
        
        # Abrir herramientas administrativas
        app.open_admin_tools()
        print("✅ Panel de herramientas administrativas abierto")
        
        # Verificar configuración de columnas
        if hasattr(app, 'admin_panel'):
            # Obtener información de la configuración de grid
            grid_info = app.admin_panel.grid_slaves()
            print(f"📊 Elementos en grid: {len(grid_info)}")
            
            # Verificar configuración de la primera columna (botones)
            column_config = app.admin_panel.grid_columnconfigure(0)
            print(f"📏 Configuración columna 0 (botones): {column_config}")
            
            # Mostrar mensaje de éxito
            success_msg = """
🎉 ESPACIADO CORREGIDO EXITOSAMENTE

✅ Cambios aplicados:
   • minsize reducido de 140px a 100px
   • padx de contenedor reducido de 15px a 8px  
   • padx de botones reducido de 5px a 3px
   • pady de botones reducido de 2px a 1px
   • pady de separadores reducido de 8px a 5px

📏 La columna de herramientas ahora es más compacta
🎯 Mejor aprovechamiento del espacio horizontal
            """
            
            print(success_msg)
            
            # Mostrar ventana con mensaje de éxito
            messagebox.showinfo("Espaciado Corregido", 
                              "✅ El espaciado de herramientas admin ha sido optimizado.\n\n"
                              "La columna de botones ahora es más compacta y aprovecha mejor el espacio.")
            
            # Ejecutar la aplicación para verificación visual
            print("\n🖥️ Ventana abierta para verificación visual")
            print("📋 Verifica que:")
            print("   • La columna de herramientas es más estrecha")
            print("   • Los botones tienen menos espaciado")
            print("   • Hay más espacio para las otras columnas")
            print("   • La interfaz se ve más equilibrada")
            
            app.root.mainloop()
            
        else:
            print("❌ Panel administrativo no encontrado")
            return False
            
    except ImportError as e:
        print(f"❌ Error importando módulo: {e}")
        return False
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def verify_code_changes():
    """Verificar que los cambios de código están aplicados"""
    print("\n🔍 VERIFICANDO CAMBIOS EN CÓDIGO")
    print("=" * 40)
    
    try:
        with open('xui_manager_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verificar cambios específicos
        checks = [
            ('minsize=100', 'Ancho mínimo de columna reducido'),
            ('padx=(0, 8)', 'Padding de contenedor reducido'),
            ('pady=1, padx=3', 'Padding de botones reducido'),
            ('pady=5)', 'Padding de separadores reducido')
        ]
        
        all_good = True
        for check, description in checks:
            if check in content:
                print(f"✅ {description}: {check}")
            else:
                print(f"❌ {description}: {check} NO ENCONTRADO")
                all_good = False
        
        if all_good:
            print("\n🎉 TODOS LOS CAMBIOS APLICADOS CORRECTAMENTE")
        else:
            print("\n⚠️ ALGUNOS CAMBIOS PUEDEN ESTAR FALTANDO")
            
        return all_good
        
    except Exception as e:
        print(f"❌ Error verificando código: {e}")
        return False

def main():
    """Función principal"""
    print("🛠️ VERIFICADOR DE ESPACIADO HERRAMIENTAS ADMIN")
    print("=" * 55)
    
    # Verificar cambios en código
    code_ok = verify_code_changes()
    
    if code_ok:
        print("\n🚀 Iniciando prueba visual...")
        # Probar espaciado
        test_ok = test_admin_spacing()
        
        if test_ok:
            print("\n✅ PRUEBA COMPLETADA EXITOSAMENTE")
            return 0
        else:
            print("\n❌ PRUEBA FALLÓ")
            return 1
    else:
        print("\n❌ CAMBIOS DE CÓDIGO NO VERIFICADOS")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
