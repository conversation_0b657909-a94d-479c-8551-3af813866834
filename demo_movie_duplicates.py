#!/usr/bin/env python3
"""
🎬 DEMOSTRACIÓN DE FUNCIONALIDAD DE DUPLICADOS DE PELÍCULAS
Muestra cómo funcionan los nuevos botones agregados
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_duplicate_detection():
    """Demostrar la detección de duplicados"""
    print("🎬 DEMOSTRACIÓN: DETECCIÓN DE DUPLICADOS DE PELÍCULAS")
    print("=" * 60)
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        # Crear instancia de la aplicación
        app = XUIManagerFinal()
        
        # Datos de prueba que simulan películas duplicadas
        test_movies = [
            {
                'id': 1,
                'stream_display_name': 'Avatar (2009) 1080p BluRay x264',
                'stream_source': 'http://server1.com/movies/avatar_2009_1080p.mp4',
                'added': '2024-01-01 10:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 2,
                'stream_display_name': 'Avatar 2009 720p WEB-DL H264',
                'stream_source': 'http://server2.com/content/avatar_720p.mp4',
                'added': '2024-01-02 11:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 1  # Este es un symlink, se preservará
            },
            {
                'id': 3,
                'stream_display_name': 'Avatar (2009) 4K UHD BluRay',
                'stream_source': 'http://server3.com/4k/avatar_4k.mp4',
                'added': '2024-01-03 12:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 4,
                'stream_display_name': 'Titanic (1997) 1080p BluRay',
                'stream_source': 'http://server1.com/movies/titanic_1997.mp4',
                'added': '2024-01-04 13:00:00',
                'tmdb_id': 597,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 5,
                'stream_display_name': 'Titanic 1997 720p DVDRip',
                'stream_source': 'http://server2.com/old/titanic_dvd.mp4',
                'added': '2024-01-05 14:00:00',
                'tmdb_id': 597,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 1  # Este es un symlink, se preservará
            }
        ]
        
        print(f"📊 DATOS DE PRUEBA: {len(test_movies)} películas")
        print("-" * 40)
        
        for movie in test_movies:
            print(f"ID {movie['id']}: {movie['stream_display_name']}")
            print(f"  📅 Agregada: {movie['added']}")
            print(f"  🎭 TMDB ID: {movie['tmdb_id']}")
            print()
        
        print("🔍 EJECUTANDO DETECCIÓN DE DUPLICADOS...")
        print("-" * 40)
        
        # Usar la función de detección de duplicados
        duplicates = app.find_movie_duplicates(test_movies)
        
        if duplicates:
            # Calcular estadísticas como en la aplicación real
            total_duplicates = sum(len(group) for group in duplicates)
            total_to_remove = sum(len(group) - 1 for group in duplicates)
            symlinks_count = sum(1 for group in duplicates for movie in group if movie.get('movie_symlink') == 1)

            print(f"📊 ESTADÍSTICAS DE DUPLICADOS:")
            print(f"🎬 Total de grupos duplicados: {len(duplicates)}")
            print(f"🔢 Total de películas duplicadas: {total_duplicates}")
            print(f"🗑️ Películas que se eliminarían: {total_to_remove}")
            print(f"🔗 Symlinks que se preservarían: {symlinks_count}")
            print()

            print(f"🎬 DETALLE DE DUPLICADOS ENCONTRADOS:")
            print()

            for i, group in enumerate(duplicates, 1):
                print(f"📁 GRUPO {i} ({len(group)} películas duplicadas):")

                # Ordenar por fecha para mostrar cuál se mantendría
                group_sorted = sorted(group, key=lambda x: x['added'], reverse=True)

                for j, movie in enumerate(group_sorted):
                    # Determinar el estado como en la aplicación real
                    if j == 0:  # La más reciente
                        status = "🟢 MANTENER (más reciente)"
                    elif movie.get('movie_symlink') == 1:
                        status = "🔗 PRESERVAR (symlink)"
                    else:
                        status = "🔴 ELIMINAR (más antigua)"

                    print(f"  {j+1}. {status}")
                    print(f"     ID: {movie['id']}")
                    print(f"     Título: {movie['stream_display_name']}")
                    print(f"     Fecha: {movie['added']}")
                    print(f"     Symlink: {'Sí' if movie.get('movie_symlink') == 1 else 'No'}")
                    print(f"     URL: {movie['stream_source'][:50]}...")
                    print()

                print("-" * 30)

            print(f"💡 RESUMEN FINAL:")
            print(f"• Se mantendrían {len(duplicates)} películas (las más recientes)")
            print(f"• Se preservarían {symlinks_count} symlinks automáticamente")
            print(f"• Se eliminarían {total_to_remove - symlinks_count} películas duplicadas")
        else:
            print("✅ No se encontraron duplicados")
        
        print("\n🔗 PROBANDO DETECCIÓN DE SYMLINKS...")
        print("-" * 40)

        test_symlink_values = [
            {'movie_symlink': 0, 'name': 'Película normal'},
            {'movie_symlink': 1, 'name': 'Película symlink'},
            {'movie_symlink': 0, 'name': 'Otra película normal'},
            {'movie_symlink': 1, 'name': 'Otro symlink'}
        ]

        for test_case in test_symlink_values:
            is_symlink = app.is_symlink_movie(test_case['movie_symlink'])
            status = "🔗 SYMLINK (preservar)" if is_symlink else "📁 NORMAL (puede eliminar)"
            print(f"{status}")
            print(f"  {test_case['name']} (movie_symlink: {test_case['movie_symlink']})")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR EN DEMOSTRACIÓN: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_confirmation_flow():
    """Demostrar el flujo de confirmación del modo automático"""
    print("\n🤖 DEMOSTRACIÓN: FLUJO DE CONFIRMACIÓN AUTOMÁTICA")
    print("=" * 60)

    print("📋 PLAN DE LIMPIEZA AUTOMÁTICA:")
    print()

    print("🎬 GRUPO 1:")
    print("  ✅ MANTENER: Avatar (2009) 4K UHD BluRay")
    print("     📅 Fecha: 2024-01-03 12:00:00 (más reciente)")
    print("  🔗 PRESERVAR: Avatar 2009 720p WEB-DL (symlink)")
    print("  🗑️ ELIMINAR: Avatar (2009) 1080p BluRay")
    print()

    print("🎬 GRUPO 2:")
    print("  ✅ MANTENER: Titanic 1997 720p DVDRip")
    print("     📅 Fecha: 2024-01-05 14:00:00 (más reciente)")
    print("  🗑️ ELIMINAR: Titanic (1997) 1080p BluRay")
    print()

    print("📊 RESUMEN DE LA OPERACIÓN:")
    print("🎬 Grupos de duplicados encontrados: 2")
    print("🗑️ Películas que se ELIMINARÁN: 2")
    print("🔗 Symlinks que se PRESERVARÁN: 1")
    print("✅ Películas que se MANTENDRÁN: 2")
    print()

    print("⚠️ DIÁLOGO DE CONFIRMACIÓN:")
    print("┌─────────────────────────────────────────────┐")
    print("│ 🤖 CONFIRMACIÓN DE LIMPIEZA AUTOMÁTICA      │")
    print("│                                             │")
    print("│ 📊 RESUMEN DE LA OPERACIÓN:                 │")
    print("│ • Grupos de duplicados: 2                   │")
    print("│ • Películas a ELIMINAR: 2                   │")
    print("│ • Symlinks a PRESERVAR: 1                   │")
    print("│ • Películas a MANTENER: 2                   │")
    print("│                                             │")
    print("│ ⚠️ IMPORTANTE:                              │")
    print("│ • Se mantendrán las películas más recientes │")
    print("│ • Los symlinks se preservarán automáticamente│")
    print("│ • Esta acción NO se puede deshacer          │")
    print("│                                             │")
    print("│ ¿Deseas proceder con la limpieza automática?│")
    print("│                                             │")
    print("│        [SÍ]           [NO]                  │")
    print("└─────────────────────────────────────────────┘")

def show_usage_instructions():
    """Mostrar instrucciones de uso"""
    print("\n📋 INSTRUCCIONES DE USO")
    print("=" * 60)

    print("🎯 NUEVOS BOTONES MEJORADOS:")
    print()

    print("1. 🔍 DUPLICADOS MANUAL")
    print("   • Ubicación: Panel de Herramientas Administrativas > Sección Películas")
    print("   • Función: Detecta y muestra películas duplicadas con estadísticas detalladas")
    print("   • Criterios: direct_source=1 AND direct_proxy=1")
    print("   • Resultado: Lista completa con estadísticas y plan de acción")
    print("   • NUEVO: Muestra estadísticas generales y estado de cada película")
    print()

    print("2. 🤖 LIMPIAR DUPLICADOS")
    print("   • Ubicación: Panel de Herramientas Administrativas > Sección Películas")
    print("   • Función: Limpia automáticamente con confirmación previa")
    print("   • Criterios: direct_source=1 AND direct_proxy=1")
    print("   • Lógica: Mantiene la película más reciente por timestamp")
    print("   • Protección: No elimina symlinks (movie_symlink=1)")
    print("   • NUEVO: Muestra plan detallado y pide confirmación antes de proceder")
    print()

    print("🔧 CÓMO USAR:")
    print("1. Ejecuta: python xui_manager_final.py")
    print("2. Haz clic en '🛠️ Herramientas Admin'")
    print("3. En la sección 'PELÍCULAS', usa los nuevos botones:")
    print("   - Para revisar con estadísticas: '🔍 Duplicados Manual'")
    print("   - Para limpiar con confirmación: '🤖 Limpiar Duplicados'")
    print()

    print("✨ MEJORAS IMPLEMENTADAS:")
    print("• Estadísticas detalladas en modo manual")
    print("• Plan de limpieza con confirmación en modo automático")
    print("• Contadores de películas a eliminar/preservar/mantener")
    print("• Diálogo de confirmación antes de eliminar")
    print("• Detección correcta de symlinks usando campo movie_symlink")
    print("• Resultados más claros y organizados")
    print()

    print("⚠️ IMPORTANTE:")
    print("• Los symlinks (movie_symlink=1) se preservan automáticamente")
    print("• Se mantiene siempre la película con timestamp más reciente")
    print("• Solo procesa películas con direct_source=1 y direct_proxy=1")
    print("• El modo automático requiere confirmación explícita del usuario")
    print("• Los resultados se muestran en el panel central de datos")

if __name__ == "__main__":
    print("🚀 DEMOSTRACIÓN DE DUPLICADOS DE PELÍCULAS MEJORADA")
    print("=" * 60)

    # Ejecutar demostración
    if demo_duplicate_detection():
        demo_confirmation_flow()
        show_usage_instructions()
        print("\n✅ DEMOSTRACIÓN COMPLETADA EXITOSAMENTE")
        print("🎉 Las nuevas funcionalidades están listas para usar")
    else:
        print("\n❌ ERROR EN LA DEMOSTRACIÓN")

    print("=" * 60)
