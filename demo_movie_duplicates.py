#!/usr/bin/env python3
"""
🎬 DEMOSTRACIÓN DE FUNCIONALIDAD DE DUPLICADOS DE PELÍCULAS
Muestra cómo funcionan los nuevos botones agregados
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_duplicate_detection():
    """Demostrar la detección de duplicados"""
    print("🎬 DEMOSTRACIÓN: DETECCIÓN DE DUPLICADOS DE PELÍCULAS")
    print("=" * 60)
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        # Crear instancia de la aplicación
        app = XUIManagerFinal()
        
        # Datos de prueba que simulan películas duplicadas
        test_movies = [
            {
                'id': 1,
                'stream_display_name': 'Avatar (2009) 1080p BluRay x264',
                'stream_source': 'http://server1.com/movies/avatar_2009_1080p.mp4',
                'added': '2024-01-01 10:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 2,
                'stream_display_name': 'Avatar 2009 720p WEB-DL H264',
                'stream_source': 'http://server2.com/content/avatar_720p.mp4',
                'added': '2024-01-02 11:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 1  # Este es un symlink, se preservará
            },
            {
                'id': 3,
                'stream_display_name': 'Avatar (2009) 4K UHD BluRay',
                'stream_source': 'http://server3.com/4k/avatar_4k.mp4',
                'added': '2024-01-03 12:00:00',
                'tmdb_id': 19995,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 4,
                'stream_display_name': 'Titanic (1997) 1080p BluRay',
                'stream_source': 'http://server1.com/movies/titanic_1997.mp4',
                'added': '2024-01-04 13:00:00',
                'tmdb_id': 597,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 0
            },
            {
                'id': 5,
                'stream_display_name': 'Titanic 1997 720p DVDRip',
                'stream_source': 'http://server2.com/old/titanic_dvd.mp4',
                'added': '2024-01-05 14:00:00',
                'tmdb_id': 597,
                'direct_source': 1,
                'direct_proxy': 1,
                'movie_symlink': 1  # Este es un symlink, se preservará
            }
        ]
        
        print(f"📊 DATOS DE PRUEBA: {len(test_movies)} películas")
        print("-" * 40)
        
        for movie in test_movies:
            print(f"ID {movie['id']}: {movie['stream_display_name']}")
            print(f"  📅 Agregada: {movie['added']}")
            print(f"  🎭 TMDB ID: {movie['tmdb_id']}")
            print()
        
        print("🔍 EJECUTANDO DETECCIÓN DE DUPLICADOS...")
        print("-" * 40)
        
        # Usar la función de detección de duplicados
        duplicates = app.find_movie_duplicates(test_movies)
        
        if duplicates:
            print(f"🎯 DUPLICADOS ENCONTRADOS: {len(duplicates)} grupos")
            print()
            
            for i, group in enumerate(duplicates, 1):
                print(f"📁 GRUPO {i} ({len(group)} películas duplicadas):")
                
                # Ordenar por fecha para mostrar cuál se mantendría
                group_sorted = sorted(group, key=lambda x: x['added'], reverse=True)
                
                for j, movie in enumerate(group_sorted):
                    status = "🟢 MANTENER (más reciente)" if j == 0 else "🔴 ELIMINAR (más antigua)"
                    print(f"  {j+1}. {status}")
                    print(f"     ID: {movie['id']}")
                    print(f"     Título: {movie['stream_display_name']}")
                    print(f"     Fecha: {movie['added']}")
                    print(f"     URL: {movie['stream_source'][:50]}...")
                    print()
                
                print("-" * 30)
        else:
            print("✅ No se encontraron duplicados")
        
        print("\n🔗 PROBANDO DETECCIÓN DE SYMLINKS...")
        print("-" * 40)

        test_symlink_values = [
            {'movie_symlink': 0, 'name': 'Película normal'},
            {'movie_symlink': 1, 'name': 'Película symlink'},
            {'movie_symlink': 0, 'name': 'Otra película normal'},
            {'movie_symlink': 1, 'name': 'Otro symlink'}
        ]

        for test_case in test_symlink_values:
            is_symlink = app.is_symlink_movie(test_case['movie_symlink'])
            status = "🔗 SYMLINK (preservar)" if is_symlink else "📁 NORMAL (puede eliminar)"
            print(f"{status}")
            print(f"  {test_case['name']} (movie_symlink: {test_case['movie_symlink']})")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR EN DEMOSTRACIÓN: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_instructions():
    """Mostrar instrucciones de uso"""
    print("\n📋 INSTRUCCIONES DE USO")
    print("=" * 60)
    
    print("🎯 NUEVOS BOTONES AGREGADOS:")
    print()
    
    print("1. 🔍 DUPLICADOS MANUAL")
    print("   • Ubicación: Panel de Herramientas Administrativas > Sección Películas")
    print("   • Función: Detecta y muestra películas duplicadas")
    print("   • Criterios: direct_source=1 AND direct_proxy=1")
    print("   • Resultado: Lista detallada de duplicados para revisión manual")
    print()
    
    print("2. 🤖 LIMPIAR DUPLICADOS")
    print("   • Ubicación: Panel de Herramientas Administrativas > Sección Películas")
    print("   • Función: Limpia automáticamente películas duplicadas")
    print("   • Criterios: direct_source=1 AND direct_proxy=1")
    print("   • Lógica: Mantiene la película más reciente por timestamp")
    print("   • Protección: No elimina symlinks")
    print()
    
    print("🔧 CÓMO USAR:")
    print("1. Ejecuta: python xui_manager_final.py")
    print("2. Haz clic en '🛠️ Herramientas Admin'")
    print("3. En la sección 'PELÍCULAS', usa los nuevos botones:")
    print("   - Para revisar manualmente: '🔍 Duplicados Manual'")
    print("   - Para limpiar automáticamente: '🤖 Limpiar Duplicados'")
    print()
    
    print("⚠️ IMPORTANTE:")
    print("• Los symlinks se preservan automáticamente")
    print("• Se mantiene siempre la película con timestamp más reciente")
    print("• Solo procesa películas con direct_source=1 y direct_proxy=1")
    print("• Los resultados se muestran en el panel central de datos")

if __name__ == "__main__":
    print("🚀 DEMOSTRACIÓN DE DUPLICADOS DE PELÍCULAS")
    print("=" * 60)
    
    # Ejecutar demostración
    if demo_duplicate_detection():
        show_usage_instructions()
        print("\n✅ DEMOSTRACIÓN COMPLETADA EXITOSAMENTE")
    else:
        print("\n❌ ERROR EN LA DEMOSTRACIÓN")
    
    print("=" * 60)
