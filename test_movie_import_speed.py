#!/usr/bin/env python3
"""
⚡ PRUEBA DE VELOCIDAD DE IMPORTACIÓN DE PELÍCULAS
Script para verificar que la importación es más rápida sin verificación de duplicados
"""

import time
import os
from robust_movie_importer import RobustMovieImporter

def create_test_m3u(filename, num_movies=50):
    """Crear archivo M3U de prueba con películas"""
    print(f"📝 Creando archivo de prueba: {filename}")
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("#EXTM3U\n")
        
        for i in range(1, num_movies + 1):
            # Crear entrada de película
            f.write(f'#EXTINF:-1 tvg-id="movie{i}" tvg-name="Test Movie {i}" tvg-logo="https://example.com/poster{i}.jpg" group-title="Películas",Test Movie {i} (2024)\n')
            f.write(f'http://example.com/movies/test_movie_{i}.mp4\n')
    
    print(f"✅ Archivo creado con {num_movies} películas")
    return filename

def test_import_speed():
    """Probar velocidad de importación"""
    print("⚡ PRUEBA DE VELOCIDAD DE IMPORTACIÓN")
    print("=" * 50)
    
    # Crear archivo de prueba
    test_file = "test_movies_speed.m3u"
    num_movies = 100
    
    create_test_m3u(test_file, num_movies)
    
    try:
        # Crear importador
        importer = RobustMovieImporter()
        
        if not importer.connect():
            print("❌ No se pudo conectar a la base de datos")
            return
        
        print(f"\n🚀 Iniciando importación de {num_movies} películas...")
        print("📊 Midiendo tiempo de ejecución...")
        
        # Medir tiempo
        start_time = time.time()
        
        # Configuración de importación
        config = {
            'file_path': test_file,
            'server_id': 82  # ID de servidor de prueba
        }
        
        # Función de progreso con tiempo
        def progress_with_time(progress):
            elapsed = time.time() - start_time
            movies_per_second = (progress / 100) * num_movies / elapsed if elapsed > 0 else 0
            print(f"\r⚡ Progreso: {progress:3d}% | Tiempo: {elapsed:.1f}s | Velocidad: {movies_per_second:.1f} películas/s", end="", flush=True)
        
        # Importar con medición de tiempo
        result = importer.import_movies(config, progress_with_time)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n\n📊 RESULTADOS DE VELOCIDAD:")
        print("=" * 40)
        print(f"⏱️ Tiempo total: {total_time:.2f} segundos")
        print(f"🎬 Películas procesadas: {num_movies}")
        print(f"⚡ Velocidad promedio: {num_movies / total_time:.2f} películas/segundo")
        print(f"📈 Tiempo por película: {total_time / num_movies:.3f} segundos")
        
        if result['success']:
            print(f"\n✅ IMPORTACIÓN EXITOSA:")
            print(f"   📥 Importadas: {result.get('imported', 0)}")
            print(f"   ⏭️ Omitidas: {result.get('skipped', 0)}")
            print(f"   ❌ Errores: {result.get('errors', 0)}")
            
            # Calcular mejora estimada
            estimated_old_time = total_time * 2  # Estimación con verificación de duplicados
            improvement = ((estimated_old_time - total_time) / estimated_old_time) * 100
            
            print(f"\n🚀 MEJORA ESTIMADA:")
            print(f"   ⏱️ Tiempo anterior estimado: {estimated_old_time:.2f}s")
            print(f"   ⚡ Tiempo actual: {total_time:.2f}s")
            print(f"   📈 Mejora: ~{improvement:.1f}% más rápido")
            
        else:
            print(f"❌ Error en importación: {result.get('error', 'Desconocido')}")
    
    finally:
        importer.disconnect()
        
        # Limpiar archivo de prueba
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Archivo de prueba eliminado: {test_file}")

def compare_parsing_speed():
    """Comparar velocidad de parsing M3U"""
    print("\n📊 COMPARACIÓN DE VELOCIDAD DE PARSING")
    print("=" * 50)
    
    # Crear archivos de prueba de diferentes tamaños
    test_sizes = [50, 100, 200, 500]
    
    for size in test_sizes:
        test_file = f"test_movies_{size}.m3u"
        
        print(f"\n🎬 Probando con {size} películas...")
        
        # Crear archivo
        create_test_m3u(test_file, size)
        
        try:
            # Medir tiempo de parsing
            importer = RobustMovieImporter()
            
            start_time = time.time()
            movies = importer.parse_m3u_file(test_file)
            end_time = time.time()
            
            parse_time = end_time - start_time
            
            print(f"   ⏱️ Tiempo de parsing: {parse_time:.3f}s")
            print(f"   📊 Películas encontradas: {len(movies)}")
            print(f"   ⚡ Velocidad: {len(movies) / parse_time:.1f} películas/s")
            
        finally:
            # Limpiar
            if os.path.exists(test_file):
                os.remove(test_file)

def main():
    """Función principal"""
    print("⚡ PRUEBA DE VELOCIDAD - IMPORTACIÓN DE PELÍCULAS")
    print("=" * 60)
    print("🎯 Objetivo: Verificar mejora de velocidad sin verificación de duplicados")
    print()
    
    try:
        # Prueba de velocidad de importación
        test_import_speed()
        
        # Prueba de velocidad de parsing
        compare_parsing_speed()
        
        print(f"\n💡 RECOMENDACIONES:")
        print(f"   🚀 La importación ahora es más rápida")
        print(f"   🔧 Usar 'python movie_duplicate_manager.py' después para gestionar duplicados")
        print(f"   📊 Monitorear la base de datos para duplicados periódicamente")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ Prueba cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error en la prueba: {e}")

if __name__ == "__main__":
    main()
