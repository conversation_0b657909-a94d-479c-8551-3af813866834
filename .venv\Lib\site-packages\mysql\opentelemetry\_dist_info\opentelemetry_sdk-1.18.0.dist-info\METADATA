Metadata-Version: 2.1
Name: opentelemetry-sdk
Version: 1.18.0
Summary: OpenTelemetry Python SDK
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/opentelemetry-sdk
Author-email: OpenTelemetry Authors <<EMAIL>>
License-Expression: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Typing :: Typed
Requires-Python: >=3.7
Requires-Dist: opentelemetry-api==1.18.0
Requires-Dist: opentelemetry-semantic-conventions==0.39b0
Requires-Dist: setuptools>=16.0
Requires-Dist: typing-extensions>=3.7.4
Provides-Extra: test
Description-Content-Type: text/x-rst

OpenTelemetry Python SDK
============================================================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-sdk.svg
   :target: https://pypi.org/project/opentelemetry-sdk/

Installation
------------

::

    pip install opentelemetry-sdk

References
----------

* `OpenTelemetry Project <https://opentelemetry.io/>`_
