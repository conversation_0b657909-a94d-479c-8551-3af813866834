#!/usr/bin/env python3
"""
🎬🔄 PRUEBA DE PANEL DE PELÍCULAS Y COMPARADOR
Verificar que ambos componentes se ven con el estilo NVIDIA + Gemini
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import sys

def create_test_movies_panel():
    """Crear ventana de prueba del panel de películas"""
    
    # Colores NVIDIA + Gemini
    colors = {
        'bg_primary': '#0d1117',        # Negro GitHub
        'bg_secondary': '#161b22',      # Gris muy oscuro
        'bg_tertiary': '#21262d',       # Gris oscuro
        'bg_card': '#1c2128',           # Cards oscuros
        'accent': '#00d4ff',            # <PERSON>
        'accent_hover': '#00b8e6',      # Celeste más oscuro
        'nvidia_green': '#76b900',      # Verde NVIDIA
        'nvidia_green_hover': '#5a8a00', # Verde NVIDIA hover
        'text_primary': '#f0f6fc',      # <PERSON> suave
        'text_secondary': '#8b949e',    # Gris claro
        'success': '#238636',           # Verde éxito
        'warning': '#d29922',           # Amarillo
        'error': '#f85149',             # Rojo
        'border': '#76b900',            # Bordes verdes NVIDIA
        'border_focus': '#00d4ff'       # Bordes activos celestes
    }
    
    # Crear ventana principal
    root = tk.Tk()
    root.title("🎬 Prueba - Panel de Películas NVIDIA + Gemini")
    root.geometry("600x700")
    root.configure(bg=colors['bg_primary'])
    
    # Centrar ventana
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (600 // 2)
    y = (root.winfo_screenheight() // 2) - (700 // 2)
    root.geometry(f"600x700+{x}+{y}")
    
    # Frame principal
    main_frame = tk.Frame(root, bg=colors['bg_primary'])
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # === PANEL DE PELÍCULAS ===
    create_movies_panel_demo(main_frame, colors)
    
    print("🎬 Panel de películas creado")
    print("✅ Verifica que se vea con estilo NVIDIA + Gemini:")
    print("   🖤 Fondo negro elegante")
    print("   🟢 Bordes verdes NVIDIA")
    print("   🔵 Botón celeste Gemini para importar")
    print("   ⚡ Diseño profesional gaming")
    
    return root

def create_movies_panel_demo(parent, colors):
    """Crear demo del panel de películas"""
    # Card de películas con bordes verdes NVIDIA
    movies_card = tk.Frame(parent, bg=colors['bg_card'],
                         relief='solid', bd=2, highlightbackground=colors['border'])
    movies_card.pack(fill='both', expand=True)

    # Header
    header_frame = tk.Frame(movies_card, bg=colors['bg_card'])
    header_frame.pack(fill='x', padx=20, pady=20)
    
    # Icono y título
    icon_label = tk.Label(header_frame, text="🎬", font=('Segoe UI', 24),
                         bg=colors['bg_card'], fg=colors['accent'])
    icon_label.pack(side='left', padx=(0, 15))
    
    title_container = tk.Frame(header_frame, bg=colors['bg_card'])
    title_container.pack(side='left', fill='x', expand=True)
    
    title_label = tk.Label(title_container, text="PELÍCULAS",
                          font=('Segoe UI', 18, 'bold'),
                          bg=colors['bg_card'], fg=colors['text_primary'])
    title_label.pack(anchor='w')
    
    subtitle_label = tk.Label(title_container, text="Importar y gestionar películas desde archivos M3U",
                             font=('Segoe UI', 10),
                             bg=colors['bg_card'], fg=colors['text_secondary'])
    subtitle_label.pack(anchor='w')

    # Contenido principal
    content = tk.Frame(movies_card, bg=colors['bg_card'])
    content.pack(fill='both', expand=True, padx=20, pady=(0, 20))

    # Frame de importación con bordes verdes
    import_frame = tk.Frame(content, bg=colors['bg_secondary'], 
                          relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    import_frame.pack(fill='x', pady=(0, 15))

    # Título de importación estilo NVIDIA
    title_container = tk.Frame(import_frame, bg=colors['bg_secondary'])
    title_container.pack(fill='x', pady=(15, 10))
    
    import_title = tk.Label(title_container, text="📁 Importar Películas",
                           font=('Segoe UI', 14, 'bold'),
                           bg=colors['bg_secondary'], fg=colors['nvidia_green'])
    import_title.pack()
    
    # Línea decorativa verde
    title_line = tk.Frame(title_container, height=2, bg=colors['nvidia_green'])
    title_line.pack(fill='x', pady=(5, 0))

    # Selector de archivo
    file_frame = tk.Frame(import_frame, bg=colors['bg_secondary'])
    file_frame.pack(fill='x', padx=15, pady=(0, 10))

    tk.Label(file_frame, text="Archivo M3U:",
            font=('Segoe UI', 10, 'bold'),
            bg=colors['bg_secondary'], fg=colors['text_primary']).pack(anchor='w')

    file_input_frame = tk.Frame(file_frame, bg=colors['bg_secondary'])
    file_input_frame.pack(fill='x', pady=(5, 0))

    file_entry = tk.Entry(file_input_frame, 
                         font=('Segoe UI', 10), state='readonly',
                         bg=colors['bg_tertiary'], fg=colors['text_primary'])
    file_entry.pack(side='left', fill='x', expand=True, ipady=5)
    file_entry.insert(0, "C:/Movies/peliculas_2024.m3u")

    browse_btn = tk.Button(file_input_frame, text="📁 Examinar",
                          bg=colors['nvidia_green'], fg=colors['bg_primary'],
                          font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                          activebackground=colors['nvidia_green_hover'],
                          highlightbackground=colors['nvidia_green'])
    browse_btn.pack(side='right', padx=(10, 0), ipady=6, ipadx=12)

    # Nota informativa sobre servidor (sin input)
    info_frame = tk.Frame(import_frame, bg=colors['bg_secondary'])
    info_frame.pack(fill='x', padx=15, pady=(0, 15))

    info_label = tk.Label(info_frame, text="📡 Servidor de destino: 82 (Películas)",
                         font=('Segoe UI', 10),
                         bg=colors['bg_secondary'], fg=colors['text_secondary'])
    info_label.pack(anchor='w')

    # Botones de acción principales
    action_frame = tk.Frame(import_frame, bg=colors['bg_secondary'])
    action_frame.pack(fill='x', padx=15, pady=(0, 10))

    preview_btn = tk.Button(action_frame, text="ℹ️ Vista Previa",
                           bg=colors['bg_card'], fg=colors['text_primary'],
                           font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                           activebackground=colors['bg_tertiary'],
                           highlightbackground=colors['nvidia_green'])
    preview_btn.pack(side='left', padx=(0, 10), ipady=10, ipadx=20)

    import_btn = tk.Button(action_frame, text="🎬 Importar Películas",
                          bg=colors['accent'], fg=colors['bg_primary'],
                          font=('Segoe UI', 11, 'bold'), bd=2, relief='solid',
                          activebackground=colors['accent_hover'],
                          highlightbackground=colors['accent'])
    import_btn.pack(side='left', ipady=8, ipadx=15)

    # Botones de configuración adicionales
    config_frame = tk.Frame(import_frame, bg=colors['bg_secondary'])
    config_frame.pack(fill='x', padx=15, pady=(0, 15))

    # Botón de categorías
    category_btn = tk.Button(config_frame, text="🏷️ Categorías",
                            bg=colors['warning'], fg=colors['bg_primary'],
                            font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                            activebackground='#b8730a',
                            highlightbackground=colors['warning'])
    category_btn.pack(side='left', padx=(0, 10), ipady=8, ipadx=15)

    # Botón de bouquets
    bouquet_btn = tk.Button(config_frame, text="📋 Bouquets",
                           bg='#9b59b6', fg=colors['text_primary'],
                           font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                           activebackground='#8e44ad',
                           highlightbackground='#9b59b6')
    bouquet_btn.pack(side='left', ipady=8, ipadx=15)

def create_test_comparator():
    """Crear ventana de prueba del comparador"""
    
    # Colores NVIDIA + Gemini
    colors = {
        'bg_primary': '#0d1117',        # Negro GitHub
        'bg_secondary': '#161b22',      # Gris muy oscuro
        'bg_card': '#1c2128',           # Cards oscuros
        'bg_tertiary': '#21262d',       # Gris oscuro
        'border': '#76b900',            # Verde NVIDIA
        'border_hover': '#5a8a00',      # Verde NVIDIA hover
        'accent': '#00d4ff',            # Celeste Gemini
        'accent_hover': '#00b8e6',      # Celeste más oscuro
        'text_primary': '#f0f6fc',      # Blanco suave
        'text_secondary': '#8b949e',    # Gris claro
        'success': '#238636',           # Verde éxito
        'warning': '#d29922',           # Amarillo
        'error': '#f85149'              # Rojo
    }
    
    # Crear ventana principal
    root = tk.Tk()
    root.title("🔄 Prueba - Comparador NVIDIA + Gemini")
    root.geometry("1200x800")
    root.configure(bg=colors['bg_primary'])
    
    # Centrar ventana
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1200 // 2)
    y = (root.winfo_screenheight() // 2) - (800 // 2)
    root.geometry(f"1200x800+{x}+{y}")
    
    # Frame principal
    main_frame = tk.Frame(root, bg=colors['bg_primary'])
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Configurar grid
    main_frame.columnconfigure(0, weight=1)
    main_frame.columnconfigure(1, weight=1)
    main_frame.rowconfigure(2, weight=1)
    
    # === HEADER ===
    create_comparator_header_demo(main_frame, colors)
    
    # === PANEL DE CARGA ===
    create_upload_panel_demo(main_frame, colors)
    
    # === PANELES DE COMPARACIÓN ===
    create_comparison_panels_demo(main_frame, colors)
    
    print("🔄 Comparador de contenido creado")
    print("✅ Verifica que se vea con estilo NVIDIA + Gemini:")
    print("   🖤 Fondo negro elegante")
    print("   🟢 Títulos y bordes verdes NVIDIA")
    print("   🔵 Subtítulos celestes Gemini")
    print("   ⚡ Botones con colores apropiados")
    
    return root

def create_comparator_header_demo(parent, colors):
    """Crear demo del header del comparador"""
    header_frame = tk.Frame(parent, bg=colors['bg_secondary'],
                           relief='solid', bd=2, highlightbackground=colors['border'])
    header_frame.grid(row=0, column=0, columnspan=2, sticky='ew', padx=5, pady=5)

    # Contenedor del título
    title_container = tk.Frame(header_frame, bg=colors['bg_secondary'])
    title_container.pack(fill='x', pady=15)

    # Título principal estilo NVIDIA
    title_label = tk.Label(title_container, text="🔄 COMPARADOR DE CONTENIDO",
                          font=('Segoe UI', 20, 'bold'),
                          fg=colors['border'], bg=colors['bg_secondary'])
    title_label.pack()
    
    # Línea decorativa verde
    title_line = tk.Frame(title_container, height=3, bg=colors['border'])
    title_line.pack(fill='x', pady=(8, 0))

    # Subtítulo estilo Gemini
    subtitle_label = tk.Label(header_frame, text="Compara tu M3U con la base de datos actual",
                             font=('Segoe UI', 12),
                             fg=colors['accent'], bg=colors['bg_secondary'])
    subtitle_label.pack(pady=(0, 15))

def create_upload_panel_demo(parent, colors):
    """Crear demo del panel de carga"""
    upload_frame = tk.Frame(parent, bg=colors['bg_card'],
                           relief='solid', bd=2, highlightbackground=colors['border'])
    upload_frame.grid(row=1, column=0, columnspan=2, sticky='ew', padx=5, pady=5)
    upload_frame.columnconfigure(1, weight=1)

    # Contenedor del título
    title_container = tk.Frame(upload_frame, bg=colors['bg_card'])
    title_container.grid(row=0, column=0, columnspan=3, pady=15)

    # Título del panel estilo NVIDIA
    upload_title = tk.Label(title_container, text="📁 CARGAR ARCHIVO M3U",
                           font=('Segoe UI', 14, 'bold'),
                           fg=colors['border'], bg=colors['bg_card'])
    upload_title.pack()
    
    # Línea decorativa verde
    title_line = tk.Frame(title_container, height=2, bg=colors['border'])
    title_line.pack(fill='x', pady=(5, 0))

    # Campo de archivo
    tk.Label(upload_frame, text="Archivo M3U:",
            font=('Segoe UI', 10, 'bold'),
            fg=colors['text_primary'], bg=colors['bg_card']).grid(row=1, column=0, padx=15, pady=10, sticky='w')

    file_entry = tk.Entry(upload_frame, font=('Segoe UI', 10), width=60,
                         bg=colors['bg_secondary'], fg=colors['text_primary'],
                         insertbackground=colors['text_primary'],
                         relief='solid', bd=1, highlightbackground=colors['border'])
    file_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')
    file_entry.insert(0, "C:/Series/series_completas_2024.m3u")

    # Botones
    browse_btn = tk.Button(upload_frame, text="📂 Examinar",
                          bg=colors['border'], fg=colors['bg_primary'],
                          font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                          activebackground=colors['border_hover'],
                          highlightbackground=colors['border'])
    browse_btn.grid(row=1, column=2, padx=15, pady=10)

    analyze_btn = tk.Button(upload_frame, text="🔍 Analizar M3U",
                           bg=colors['accent'], fg=colors['bg_primary'],
                           font=('Segoe UI', 12, 'bold'), bd=2, relief='solid',
                           activebackground=colors['accent_hover'],
                           highlightbackground=colors['accent'])
    analyze_btn.grid(row=2, column=0, columnspan=3, pady=15)

def create_comparison_panels_demo(parent, colors):
    """Crear demo de los paneles de comparación"""
    # Panel izquierdo - M3U
    left_panel = tk.Frame(parent, bg=colors['bg_card'],
                         relief='solid', bd=2, highlightbackground=colors['border'])
    left_panel.grid(row=2, column=0, sticky='nsew', padx=(5, 2), pady=5)

    # Título izquierdo
    left_title_container = tk.Frame(left_panel, bg=colors['bg_card'])
    left_title_container.pack(fill='x', pady=15)

    left_title = tk.Label(left_title_container, text="📁 M3U CARGADO",
                         font=('Segoe UI', 14, 'bold'),
                         fg=colors['border'], bg=colors['bg_card'])
    left_title.pack()
    
    left_line = tk.Frame(left_title_container, height=2, bg=colors['border'])
    left_line.pack(fill='x', pady=(5, 0))

    # Panel derecho - BD
    right_panel = tk.Frame(parent, bg=colors['bg_card'],
                          relief='solid', bd=2, highlightbackground=colors['border'])
    right_panel.grid(row=2, column=1, sticky='nsew', padx=(2, 5), pady=5)

    # Título derecho
    right_title_container = tk.Frame(right_panel, bg=colors['bg_card'])
    right_title_container.pack(fill='x', pady=15)

    right_title = tk.Label(right_title_container, text="💾 BASE DE DATOS ACTUAL",
                          font=('Segoe UI', 14, 'bold'),
                          fg=colors['accent'], bg=colors['bg_card'])
    right_title.pack()
    
    right_line = tk.Frame(right_title_container, height=2, bg=colors['accent'])
    right_line.pack(fill='x', pady=(5, 0))

def main():
    """Función principal"""
    print("🎬🔄 INICIANDO PRUEBAS VISUALES")
    print("=" * 40)
    
    try:
        # Crear ventana de películas
        movies_root = create_test_movies_panel()
        
        # Crear ventana de comparador
        comparator_root = create_test_comparator()
        
        print("\n🚀 Ventanas abiertas - Cierra cuando termines de verificar")
        
        # Ejecutar ambas ventanas
        movies_root.mainloop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
