#!/usr/bin/env python3
"""
Gestor de categorías para películas
Basado en category_manager.py pero específico para movies
"""

import json
from db_connection import DatabaseConnection

class MovieCategoryManager:
    """Gestor de categorías para películas en el sistema XUI"""
    
    def __init__(self):
        self.db = None
        self.categories_cache = None
    
    def connect(self):
        """Conectar a la base de datos"""
        try:
            self.db = DatabaseConnection()
            return self.db.connect()
        except Exception as e:
            print(f"❌ Error conectando a BD: {e}")
            return False
    
    def get_movie_categories(self):
        """Obtener todas las categorías de tipo 'movie'"""
        try:
            if not self.db:
                if not self.connect():
                    return []
            
            # Si ya tenemos cache, devolverlo
            if self.categories_cache is not None:
                return self.categories_cache
            
            query = """
            SELECT id, category_name, parent_id, cat_order
            FROM streams_categories 
            WHERE category_type = 'movie'
            ORDER BY cat_order ASC, category_name ASC
            """
            
            categories = self.db.execute_query(query)
            
            if categories:
                # Formatear categorías para uso fácil
                formatted_categories = []
                for cat in categories:
                    formatted_categories.append({
                        'id': cat['id'],
                        'name': cat['category_name'],
                        'parent_id': cat.get('parent_id', 0),
                        'order': cat.get('cat_order', 0)
                    })
                
                self.categories_cache = formatted_categories
                return formatted_categories
            else:
                return []
                
        except Exception as e:
            print(f"❌ Error obteniendo categorías de películas: {e}")
            return []
    
    def get_category_by_id(self, category_id):
        """Obtener información de una categoría por ID"""
        try:
            categories = self.get_movie_categories()
            for cat in categories:
                if cat['id'] == category_id:
                    return cat
            return None
        except Exception as e:
            print(f"❌ Error obteniendo categoría {category_id}: {e}")
            return None
    
    def get_category_name_by_id(self, category_id):
        """Obtener solo el nombre de una categoría por ID"""
        try:
            category = self.get_category_by_id(category_id)
            return category['name'] if category else f"Categoría {category_id}"
        except Exception as e:
            print(f"❌ Error obteniendo nombre de categoría {category_id}: {e}")
            return f"Categoría {category_id}"
    
    def parse_category_id_from_movie(self, category_id_json):
        """Parsear category_id de streams (formato JSON array)"""
        try:
            if not category_id_json:
                return None
            
            # Si ya es una lista
            if isinstance(category_id_json, list):
                return category_id_json[0] if category_id_json else None
            
            # Si es string JSON
            if isinstance(category_id_json, str):
                parsed = json.loads(category_id_json)
                if isinstance(parsed, list) and parsed:
                    return parsed[0]
                elif isinstance(parsed, int):
                    return parsed
            
            # Si es int directo
            if isinstance(category_id_json, int):
                return category_id_json
                
            return None
            
        except Exception as e:
            print(f"❌ Error parseando category_id: {e}")
            return None
    
    def format_category_id_for_movie(self, category_id):
        """Formatear category_id para insertar en streams"""
        try:
            if category_id is None:
                return None
            
            # Asegurar que sea int
            if isinstance(category_id, str):
                category_id = int(category_id)
            
            # Retornar como JSON array
            return json.dumps([category_id])
            
        except Exception as e:
            print(f"❌ Error formateando category_id: {e}")
            return json.dumps([1])  # Categoría por defecto
    
    def update_movie_category(self, stream_id, category_id):
        """Actualizar la categoría de una película"""
        try:
            if not self.db:
                if not self.connect():
                    return False
            
            # Formatear category_id
            formatted_category_id = self.format_category_id_for_movie(category_id)
            
            query = """
            UPDATE streams 
            SET category_id = %s 
            WHERE id = %s AND type = 2
            """
            
            self.db.cursor.execute(query, (formatted_category_id, stream_id))
            self.db.connection.commit()
            
            print(f"✅ Categoría actualizada para película {stream_id}: {category_id}")
            return True
            
        except Exception as e:
            print(f"❌ Error actualizando categoría de película {stream_id}: {e}")
            return False
    
    def get_movie_current_category(self, stream_id):
        """Obtener la categoría actual de una película"""
        try:
            if not self.db:
                if not self.connect():
                    return None
            
            query = "SELECT category_id FROM streams WHERE id = %s AND type = 2"
            result = self.db.execute_query(query, (stream_id,))
            
            if result and result[0]['category_id']:
                category_id = self.parse_category_id_from_movie(result[0]['category_id'])
                return self.get_category_by_id(category_id)
            
            return None
            
        except Exception as e:
            print(f"❌ Error obteniendo categoría actual de película {stream_id}: {e}")
            return None
    
    def get_categories_summary(self):
        """Obtener resumen de categorías con conteo de películas"""
        try:
            if not self.db:
                if not self.connect():
                    return []

            query = """
            SELECT
                sc.id,
                sc.category_name,
                COUNT(s.id) as movie_count
            FROM streams_categories sc
            LEFT JOIN streams s ON s.category_id LIKE CONCAT('%[', sc.id, ']%') AND s.type = 2
            WHERE sc.category_type = 'movie'
            GROUP BY sc.id, sc.category_name
            ORDER BY sc.cat_order ASC, sc.category_name ASC
            """

            return self.db.execute_query(query)

        except Exception as e:
            print(f"❌ Error obteniendo resumen de categorías: {e}")
            return []
    
    def auto_detect_category_from_group(self, group_title):
        """Detectar automáticamente la categoría basada en el group-title del M3U"""
        try:
            if not group_title:
                return None

            group_lower = group_title.lower().strip()
            categories = self.get_movie_categories()

            # Mapeo extendido de palabras clave a categorías
            category_keywords = {
                'accion': ['accion', 'action', 'aventura', 'thriller', 'suspense', 'espionaje', 'spy'],
                'comedia': ['comedia', 'comedy', 'humor', 'funny', 'risa', 'divertida'],
                'drama': ['drama', 'dramatico', 'dramatic', 'emocional'],
                'terror': ['terror', 'horror', 'miedo', 'suspenso', 'scary', 'zombie', 'paranormal'],
                'ciencia ficcion': ['sci-fi', 'ciencia ficcion', 'science fiction', 'futurista', 'space', 'alien'],
                'romance': ['romance', 'romantica', 'amor', 'love', 'romantic'],
                'animacion': ['animacion', 'animation', 'cartoon', 'anime', 'animated', 'dibujos'],
                'documental': ['documental', 'documentary', 'docu', 'real'],
                'infantil': ['infantil', 'kids', 'niños', 'family', 'familiar', 'children'],
                'musical': ['musical', 'music', 'musica', 'song'],
                'western': ['western', 'vaqueros', 'cowboy', 'oeste'],
                'guerra': ['guerra', 'war', 'belico', 'militar', 'battle'],
                'crimen': ['crimen', 'crime', 'policial', 'detective', 'mafia', 'gangster'],
                'fantasia': ['fantasia', 'fantasy', 'magia', 'magic', 'medieval', 'dragon'],
                'biografia': ['biografia', 'biography', 'biopic', 'based on true'],
                'misterio': ['misterio', 'mystery', 'enigma', 'investigacion'],
                'deportes': ['deportes', 'sports', 'football', 'soccer', 'basketball'],
                'historica': ['historica', 'historical', 'historia', 'period', 'epoca'],
                'clasicas': ['clasicas', 'classic', 'vintage', 'old', 'retro'],
                'latino': ['latino', 'latina', 'spanish', 'mexicana', 'argentina', 'colombiana'],
                'bollywood': ['bollywood', 'hindi', 'indian', 'india'],
                'asiaticas': ['asiaticas', 'asian', 'chinese', 'japanese', 'korean', 'china', 'japan', 'korea']
            }

            # 1. Coincidencia directa (más precisa)
            for cat in categories:
                cat_name_lower = cat['name'].lower()

                # Coincidencia exacta
                if cat_name_lower == group_lower:
                    return cat['id']

                # Coincidencia parcial (una contiene a la otra)
                if len(cat_name_lower) > 3 and len(group_lower) > 3:
                    if cat_name_lower in group_lower or group_lower in cat_name_lower:
                        return cat['id']

            # 2. Buscar por palabras clave
            for cat in categories:
                cat_name_lower = cat['name'].lower()

                for keyword_group, keywords in category_keywords.items():
                    # Si el nombre de la categoría contiene el grupo de palabras clave
                    if keyword_group in cat_name_lower or any(kw in cat_name_lower for kw in keywords):
                        # Buscar si alguna palabra clave está en el group_title
                        for keyword in keywords:
                            if keyword in group_lower:
                                return cat['id']

            # 3. Búsqueda fuzzy (similitud)
            for cat in categories:
                cat_name_lower = cat['name'].lower()

                # Calcular similitud simple (palabras en común)
                cat_words = set(cat_name_lower.split())
                group_words = set(group_lower.split())

                if cat_words and group_words:
                    common_words = cat_words.intersection(group_words)
                    if len(common_words) > 0 and len(common_words) >= len(cat_words) * 0.5:
                        return cat['id']

            return None

        except Exception as e:
            print(f"❌ Error detectando categoría automática: {e}")
            return None
    
    def refresh_cache(self):
        """Refrescar cache de categorías"""
        self.categories_cache = None
        return self.get_movie_categories()
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        if self.db:
            self.db.disconnect()
