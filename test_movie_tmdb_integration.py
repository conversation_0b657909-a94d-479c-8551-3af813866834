#!/usr/bin/env python3
"""
🧪 TEST MOVIE TMDB INTEGRATION
Prueba la integración completa del sistema TMDB para películas
"""

from movie_tmdb_assigner import MovieTMDBAssigner
import time

def test_movie_tmdb_system():
    """Probar el sistema completo de TMDB para películas"""
    print("🧪 TESTING MOVIE TMDB INTEGRATION")
    print("=" * 50)
    
    assigner = MovieTMDBAssigner()
    
    if not assigner.connect():
        print("❌ No se pudo conectar a la base de datos")
        return False
    
    try:
        # 1. Probar estadísticas
        print("\n📊 1. PROBANDO ESTADÍSTICAS...")
        stats = assigner.get_tmdb_statistics()
        print(f"   🎬 Total películas: {stats['total_movies']}")
        print(f"   ✅ Con TMDB: {stats['with_tmdb']}")
        print(f"   ❌ Sin TMDB: {stats['without_tmdb']}")
        print(f"   📈 Porcentaje: {stats['tmdb_percentage']:.1f}%")
        
        # 2. Probar búsqueda de películas sin TMDB
        print("\n🔍 2. PROBANDO BÚSQUEDA DE PELÍCULAS SIN TMDB...")
        movies_without_tmdb = assigner.find_movies_without_tmdb(5)
        print(f"   📋 Encontradas: {len(movies_without_tmdb)} películas")
        
        if movies_without_tmdb:
            for i, movie in enumerate(movies_without_tmdb, 1):
                year_info = f" ({movie['year']})" if movie['year'] else ""
                print(f"   {i}. {movie['clean_title']}{year_info}")
        
        # 3. Probar búsqueda en TMDB (solo primera película)
        if movies_without_tmdb:
            test_movie = movies_without_tmdb[0]
            print(f"\n🔍 3. PROBANDO BÚSQUEDA TMDB PARA: {test_movie['clean_title']}")
            
            search_results = assigner.search_tmdb_movies(test_movie['clean_title'], test_movie['year'])
            print(f"   📊 Resultados encontrados: {len(search_results)}")
            
            if search_results:
                for i, result in enumerate(search_results[:3], 1):
                    title = result.get('title', 'Sin título')
                    year = result.get('release_date', '')[:4] if result.get('release_date') else 'N/A'
                    rating = result.get('vote_average', 0)
                    print(f"   {i}. {title} ({year}) ⭐{rating:.1f}")
                
                # 4. Probar obtención de detalles
                print(f"\n📊 4. PROBANDO OBTENCIÓN DE DETALLES...")
                first_result = search_results[0]
                tmdb_id = first_result['id']
                
                details = assigner.get_tmdb_movie_details(tmdb_id)
                if details:
                    print(f"   ✅ Detalles obtenidos para TMDB ID: {tmdb_id}")
                    print(f"   🎬 Título: {details.get('title', 'N/A')}")
                    print(f"   📅 Año: {details.get('release_date', 'N/A')[:4]}")
                    print(f"   ⭐ Rating: {details.get('vote_average', 0)}")
                    print(f"   🎭 Géneros: {len(details.get('genres', []))}")
                    print(f"   📝 Descripción: {len(details.get('overview', ''))} caracteres")
                else:
                    print("   ❌ No se pudieron obtener detalles")
        
        # 5. Probar asignación automática (solo 2 películas para prueba)
        print(f"\n🤖 5. PROBANDO ASIGNACIÓN AUTOMÁTICA (2 películas)...")
        if len(movies_without_tmdb) >= 2:
            test_movies = movies_without_tmdb[:2]
            results = assigner.auto_assign_tmdb(test_movies, max_requests=2)
            
            print(f"   📊 RESULTADOS:")
            print(f"   ✅ Asignadas: {results['assigned']}")
            print(f"   ⚠️ No encontradas: {results['skipped']}")
            print(f"   ❌ Errores: {results['errors']}")
            print(f"   📈 Total procesadas: {results['total_processed']}")
        
        print("\n✅ TODAS LAS PRUEBAS COMPLETADAS EXITOSAMENTE")
        return True
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        return False
    finally:
        assigner.disconnect()

def test_ui_integration():
    """Probar que los métodos de UI están disponibles"""
    print("\n🖥️ TESTING UI INTEGRATION")
    print("=" * 30)
    
    try:
        # Importar el manager principal
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Verificar que los métodos existen en xui_manager_final
        from xui_manager_final import XUIManager
        
        # Crear instancia temporal para verificar métodos
        methods_to_check = [
            'load_movies_without_tmdb',
            'auto_assign_tmdb_movies', 
            'manual_assign_tmdb_movies',
            'on_movie_click',
            'search_tmdb_for_movie',
            'open_movie_assignment_dialog',
            'assign_tmdb_to_movie',
            'show_movie_assignment_success'
        ]
        
        print("   🔍 Verificando métodos de UI...")
        for method_name in methods_to_check:
            if hasattr(XUIManager, method_name):
                print(f"   ✅ {method_name}")
            else:
                print(f"   ❌ {method_name} - NO ENCONTRADO")
                return False
        
        print("   ✅ Todos los métodos de UI están disponibles")
        return True
        
    except Exception as e:
        print(f"   ❌ Error verificando UI: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🚀 INICIANDO PRUEBAS COMPLETAS DEL SISTEMA MOVIE TMDB")
    print("=" * 60)
    
    # Probar sistema TMDB
    tmdb_success = test_movie_tmdb_system()
    
    # Probar integración UI
    ui_success = test_ui_integration()
    
    # Resultado final
    print("\n" + "=" * 60)
    if tmdb_success and ui_success:
        print("🎉 ¡TODAS LAS PRUEBAS EXITOSAS!")
        print("✅ El sistema de TMDB para películas está completamente integrado")
        print("✅ Los botones en Herramientas Admin están listos para usar")
        print("\n🎬 FUNCIONALIDADES DISPONIBLES:")
        print("   • 🎬 Películas sin TMDB - Cargar lista de películas sin metadatos")
        print("   • 🤖 Auto TMDB Movies - Asignación automática masiva")
        print("   • 🔍 Manual TMDB Movies - Asignación manual con búsqueda")
    else:
        print("❌ ALGUNAS PRUEBAS FALLARON")
        print("⚠️ Revisar los errores anteriores")

if __name__ == "__main__":
    main()
