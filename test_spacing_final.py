#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba final del espaciado reducido en herramientas admin
"""

import tkinter as tk
from tkinter import messagebox

def test_final_spacing():
    """Probar el espaciado final optimizado"""
    print("🧪 PRUEBA FINAL - ESPACIADO HERRAMIENTAS ADMIN")
    print("=" * 50)
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        app = XUIManagerFinal()
        print("✅ Aplicación creada")
        
        # Abrir herramientas admin
        app.open_admin_tools()
        print("✅ Panel admin abierto")
        
        # Mostrar información de los cambios
        info_msg = """
🎯 ESPACIADO ULTRA OPTIMIZADO

✅ Cambios aplicados:
   • minsize ELIMINADO (ancho natural)
   • padx reducido a mínimo (2px)
   • Columna se ajusta al contenido

📏 La columna ahora usa solo el espacio necesario
🚀 Máximo espacio disponible para datos y TMDB
        """
        
        print(info_msg)
        
        messagebox.showinfo("Espaciado Optimizado", 
                          "✅ Espaciado ultra optimizado aplicado.\n\n"
                          "La columna de herramientas ahora usa\n"
                          "solo el espacio mínimo necesario.")
        
        print("\n🖥️ Verifica visualmente que:")
        print("   • La columna de herramientas es mucho más estrecha")
        print("   • Hay mucho más espacio para las otras columnas")
        print("   • Los botones siguen siendo legibles")
        print("   • La interfaz está mejor balanceada")
        
        app.root.mainloop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_final_spacing()
