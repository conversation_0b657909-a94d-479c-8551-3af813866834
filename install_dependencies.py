#!/usr/bin/env python3
"""
📦 INSTALADOR DE DEPENDENCIAS
Instala las dependencias necesarias para el sistema TMDB mejorado
"""

import subprocess
import sys
import os

def install_package(package):
    """Instalar un paquete usando pip"""
    try:
        print(f"📦 Instalando {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package} instalado exitosamente")
            return True
        else:
            print(f"❌ Error instalando {package}: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Excepción instalando {package}: {e}")
        return False

def check_package(package):
    """Verificar si un paquete está instalado"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """Función principal"""
    print("📦 INSTALADOR DE DEPENDENCIAS TMDB")
    print("=" * 40)
    
    # Lista de dependencias necesarias
    dependencies = [
        ("requests", "requests"),
        ("Pillow", "PIL"),
        ("mysql-connector-python", "mysql.connector")
    ]
    
    print("🔍 Verificando dependencias actuales...")
    
    to_install = []
    for pip_name, import_name in dependencies:
        if check_package(import_name):
            print(f"✅ {pip_name} ya está instalado")
        else:
            print(f"❌ {pip_name} no está instalado")
            to_install.append(pip_name)
    
    if not to_install:
        print("🎉 Todas las dependencias ya están instaladas")
        return 0
    
    print(f"\n📦 Instalando {len(to_install)} dependencias...")
    
    success_count = 0
    for package in to_install:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 RESUMEN:")
    print(f"   ✅ Instaladas: {success_count}")
    print(f"   ❌ Fallidas: {len(to_install) - success_count}")
    
    if success_count == len(to_install):
        print("🎉 TODAS LAS DEPENDENCIAS INSTALADAS EXITOSAMENTE")
        
        # Verificar instalación
        print("\n🔍 Verificando instalación...")
        all_ok = True
        for pip_name, import_name in dependencies:
            if check_package(import_name):
                print(f"✅ {pip_name} verificado")
            else:
                print(f"❌ {pip_name} falló verificación")
                all_ok = False
        
        if all_ok:
            print("🎉 VERIFICACIÓN EXITOSA - Sistema listo para usar")
            return 0
        else:
            print("⚠️ ALGUNAS VERIFICACIONES FALLARON")
            return 1
    else:
        print("❌ ALGUNAS INSTALACIONES FALLARON")
        return 1

if __name__ == "__main__":
    exit(main())
