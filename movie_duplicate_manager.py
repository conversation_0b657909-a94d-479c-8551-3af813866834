#!/usr/bin/env python3
"""
🎬 GESTOR DE DUPLICADOS DE PELÍCULAS
Herramienta para detectar y gestionar películas duplicadas después de la importación
"""

import sys
import os
from database_manager import DatabaseManager
import re
from difflib import SequenceMatcher

class MovieDuplicateManager:
    def __init__(self):
        self.db = DatabaseManager()
        
    def connect(self):
        """Conectar a la base de datos"""
        return self.db.connect()
    
    def disconnect(self):
        """Desconectar de la base de datos"""
        self.db.disconnect()
    
    def normalize_title(self, title):
        """Normalizar título para comparación"""
        if not title:
            return ""
        
        # Convertir a minúsculas
        normalized = title.lower().strip()
        
        # Remover información de calidad
        normalized = re.sub(r'\b(hd|4k|1080p|720p|480p|bluray|web-dl|webrip|dvdrip|cam|ts)\b', '', normalized)
        
        # Remover información de audio
        normalized = re.sub(r'\b(dts|ac3|aac|mp3|dolby|atmos)\b', '', normalized)
        
        # Remover información de codec
        normalized = re.sub(r'\b(x264|x265|h264|h265|hevc|avc)\b', '', normalized)
        
        # Remover años entre paréntesis
        normalized = re.sub(r'\(\d{4}\)', '', normalized)
        
        # Remover caracteres especiales
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        
        # Remover espacios múltiples
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        
        return normalized
    
    def calculate_similarity(self, title1, title2):
        """Calcular similitud entre dos títulos"""
        norm1 = self.normalize_title(title1)
        norm2 = self.normalize_title(title2)
        
        if not norm1 or not norm2:
            return 0.0
        
        return SequenceMatcher(None, norm1, norm2).ratio()
    
    def find_duplicate_movies(self, similarity_threshold=0.85):
        """Encontrar películas duplicadas"""
        try:
            print("🔍 BUSCANDO PELÍCULAS DUPLICADAS")
            print("=" * 50)
            
            # Obtener todas las películas
            query = """
            SELECT id, stream_display_name, stream_source, added_time, tmdb_id
            FROM streams 
            WHERE type = 2 
            ORDER BY stream_display_name
            """
            
            movies = self.db.execute_query(query)
            
            if not movies:
                print("❌ No se encontraron películas")
                return []
            
            print(f"📊 Analizando {len(movies)} películas...")
            
            duplicates = []
            processed = set()
            
            for i, movie1 in enumerate(movies):
                if movie1['id'] in processed:
                    continue
                
                movie_group = [movie1]
                processed.add(movie1['id'])
                
                # Comparar con el resto
                for j, movie2 in enumerate(movies[i+1:], i+1):
                    if movie2['id'] in processed:
                        continue
                    
                    similarity = self.calculate_similarity(
                        movie1['stream_display_name'], 
                        movie2['stream_display_name']
                    )
                    
                    if similarity >= similarity_threshold:
                        movie_group.append(movie2)
                        processed.add(movie2['id'])
                
                # Si hay más de una película en el grupo, son duplicados
                if len(movie_group) > 1:
                    duplicates.append(movie_group)
            
            print(f"🎯 Encontrados {len(duplicates)} grupos de duplicados")
            return duplicates
            
        except Exception as e:
            print(f"❌ Error buscando duplicados: {e}")
            return []
    
    def display_duplicates(self, duplicates):
        """Mostrar duplicados encontrados"""
        if not duplicates:
            print("✅ No se encontraron duplicados")
            return
        
        print(f"\n📋 DUPLICADOS ENCONTRADOS: {len(duplicates)} grupos")
        print("=" * 60)
        
        for i, group in enumerate(duplicates, 1):
            print(f"\n🎬 GRUPO {i} ({len(group)} películas):")
            print("-" * 40)
            
            for j, movie in enumerate(group, 1):
                print(f"   {j}. ID: {movie['id']}")
                print(f"      📽️ Título: {movie['stream_display_name']}")
                print(f"      🔗 URL: {movie['stream_source'][:60]}...")
                print(f"      📅 Agregada: {movie['added_time']}")
                print(f"      🎭 TMDB ID: {movie['tmdb_id'] or 'Sin asignar'}")
                print()
    
    def remove_duplicates_interactive(self, duplicates):
        """Remover duplicados de forma interactiva"""
        if not duplicates:
            print("✅ No hay duplicados para remover")
            return
        
        print(f"\n🗑️ REMOCIÓN INTERACTIVA DE DUPLICADOS")
        print("=" * 50)
        
        removed_count = 0
        
        for i, group in enumerate(duplicates, 1):
            print(f"\n🎬 GRUPO {i}/{len(duplicates)} ({len(group)} películas):")
            print("-" * 40)
            
            # Mostrar opciones
            for j, movie in enumerate(group, 1):
                print(f"   {j}. {movie['stream_display_name']}")
                print(f"      ID: {movie['id']} | TMDB: {movie['tmdb_id'] or 'N/A'}")
            
            print(f"   0. Saltar este grupo")
            print(f"   -1. Salir")
            
            try:
                choice = input(f"\n¿Cuál película MANTENER? (1-{len(group)}): ").strip()
                
                if choice == "-1":
                    break
                elif choice == "0":
                    continue
                
                keep_index = int(choice) - 1
                
                if 0 <= keep_index < len(group):
                    # Remover todas excepto la seleccionada
                    for j, movie in enumerate(group):
                        if j != keep_index:
                            if self.remove_movie(movie['id']):
                                print(f"   ✅ Removida: {movie['stream_display_name']}")
                                removed_count += 1
                            else:
                                print(f"   ❌ Error removiendo: {movie['stream_display_name']}")
                    
                    print(f"   🎯 Mantenida: {group[keep_index]['stream_display_name']}")
                else:
                    print("   ⚠️ Opción inválida, saltando grupo")
                    
            except (ValueError, KeyboardInterrupt):
                print("   ⚠️ Entrada inválida o cancelado")
                break
        
        print(f"\n📊 RESUMEN:")
        print(f"   🗑️ Películas removidas: {removed_count}")
        print(f"   ✅ Duplicados procesados: {i}")
    
    def remove_movie(self, movie_id):
        """Remover una película por ID"""
        try:
            query = "DELETE FROM streams WHERE id = %s AND type = 2"
            result = self.db.execute_query(query, (movie_id,))
            return True
        except Exception as e:
            print(f"❌ Error removiendo película {movie_id}: {e}")
            return False
    
    def auto_remove_duplicates(self, duplicates, keep_strategy='newest'):
        """Remover duplicados automáticamente"""
        if not duplicates:
            print("✅ No hay duplicados para remover")
            return
        
        print(f"\n🤖 REMOCIÓN AUTOMÁTICA DE DUPLICADOS")
        print(f"📋 Estrategia: {keep_strategy}")
        print("=" * 50)
        
        removed_count = 0
        
        for i, group in enumerate(duplicates, 1):
            print(f"\n🎬 Procesando grupo {i}/{len(duplicates)}...")
            
            # Determinar cuál mantener según estrategia
            if keep_strategy == 'newest':
                keep_movie = max(group, key=lambda x: x['added_time'])
            elif keep_strategy == 'oldest':
                keep_movie = min(group, key=lambda x: x['added_time'])
            elif keep_strategy == 'with_tmdb':
                # Preferir la que tenga TMDB ID
                with_tmdb = [m for m in group if m['tmdb_id']]
                keep_movie = with_tmdb[0] if with_tmdb else group[0]
            else:
                keep_movie = group[0]  # Por defecto, mantener la primera
            
            # Remover las demás
            for movie in group:
                if movie['id'] != keep_movie['id']:
                    if self.remove_movie(movie['id']):
                        print(f"   ✅ Removida: {movie['stream_display_name']}")
                        removed_count += 1
                    else:
                        print(f"   ❌ Error removiendo: {movie['stream_display_name']}")
            
            print(f"   🎯 Mantenida: {keep_movie['stream_display_name']}")
        
        print(f"\n📊 RESUMEN AUTOMÁTICO:")
        print(f"   🗑️ Películas removidas: {removed_count}")
        print(f"   ✅ Grupos procesados: {len(duplicates)}")

def main():
    """Función principal"""
    print("🎬 GESTOR DE DUPLICADOS DE PELÍCULAS")
    print("=" * 50)
    
    manager = MovieDuplicateManager()
    
    if not manager.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    try:
        # Buscar duplicados
        duplicates = manager.find_duplicate_movies()
        
        if not duplicates:
            print("✅ No se encontraron duplicados")
            return
        
        # Mostrar duplicados
        manager.display_duplicates(duplicates)
        
        # Preguntar qué hacer
        print(f"\n🛠️ OPCIONES:")
        print(f"   1. Remoción interactiva (recomendado)")
        print(f"   2. Remoción automática (mantener más reciente)")
        print(f"   3. Remoción automática (mantener con TMDB)")
        print(f"   4. Solo mostrar (no remover)")
        
        choice = input(f"\n¿Qué deseas hacer? (1-4): ").strip()
        
        if choice == "1":
            manager.remove_duplicates_interactive(duplicates)
        elif choice == "2":
            manager.auto_remove_duplicates(duplicates, 'newest')
        elif choice == "3":
            manager.auto_remove_duplicates(duplicates, 'with_tmdb')
        elif choice == "4":
            print("✅ Solo mostrando duplicados, no se removió nada")
        else:
            print("⚠️ Opción inválida")
    
    finally:
        manager.disconnect()

if __name__ == "__main__":
    main()
