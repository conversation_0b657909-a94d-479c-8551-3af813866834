#!/usr/bin/env python3
"""
🎬 TEST COMPLETO DEL SISTEMA DE PELÍCULAS
Prueba categorías automáticas + bouquets automáticos
"""

import sys
import os
from movie_category_manager import MovieCategoryManager
from bouquet_manager import BouquetManager
from robust_movie_importer import RobustMovieImporter

def test_complete_movie_system():
    """Probar sistema completo de películas con categorías y bouquets"""
    print("🎬 TEST COMPLETO DEL SISTEMA DE PELÍCULAS")
    print("=" * 60)
    
    # Crear instancias
    category_manager = MovieCategoryManager()
    bouquet_manager = BouquetManager()
    importer = RobustMovieImporter()
    
    if not importer.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    # 1. Mostrar categorías disponibles
    print("\n📋 CATEGORÍAS DISPONIBLES:")
    categories = category_manager.get_movie_categories()
    
    if not categories:
        print("❌ No se encontraron categorías de películas")
        print("💡 Asegúrate de que existen categorías con category_type='movie' en streams_categories")
        return
    
    for cat in categories:
        print(f"  {cat['id']:3d}. {cat['name']}")
    
    # 2. Mostrar bouquets disponibles
    print("\n🎁 BOUQUETS DISPONIBLES:")
    bouquets = bouquet_manager.get_movie_bouquets()
    
    if not bouquets:
        print("❌ No se encontraron bouquets para películas")
        return
    
    for bouquet in bouquets:
        movies_count = bouquet.get('movies_count', 0)
        print(f"  {bouquet['id']:3d}. {bouquet['name']} ({movies_count} películas)")
    
    # 3. Configurar categoría y bouquet por defecto
    print("\n🎯 CONFIGURACIÓN POR DEFECTO:")
    print("-" * 60)
    
    if categories:
        # Usar primera categoría como por defecto
        default_cat = categories[0]
        success = importer.set_default_category(default_cat['id'])
        if success:
            print(f"✅ Categoría por defecto: {default_cat['name']} (ID: {default_cat['id']})")
        else:
            print("❌ Error estableciendo categoría por defecto")
    
    if bouquets:
        # Buscar bouquet de películas o usar el primero
        movie_bouquet = None
        for bouquet in bouquets:
            if 'pelicula' in bouquet['name'].lower() or 'movie' in bouquet['name'].lower():
                movie_bouquet = bouquet
                break
        
        if not movie_bouquet:
            movie_bouquet = bouquets[0]
        
        success = importer.set_default_bouquet(movie_bouquet['id'])
        if success:
            print(f"✅ Bouquet por defecto: {movie_bouquet['name']} (ID: {movie_bouquet['id']})")
        else:
            print("❌ Error estableciendo bouquet por defecto")
    
    # 4. Simular importación de películas
    print("\n🧪 SIMULACIÓN DE IMPORTACIÓN:")
    print("-" * 60)
    
    test_movies = [
        {
            'title': 'Avengers: Endgame',
            'group': 'Acción',
            'logo': 'https://example.com/avengers.jpg',
            'url': 'http://test.com/avengers.mp4'
        },
        {
            'title': 'The Hangover',
            'group': 'Comedia',
            'logo': 'https://example.com/hangover.jpg',
            'url': 'http://test.com/hangover.mp4'
        },
        {
            'title': 'Titanic',
            'group': 'Romance',
            'logo': 'https://example.com/titanic.jpg',
            'url': 'http://test.com/titanic.mp4'
        }
    ]
    
    for i, movie_data in enumerate(test_movies, 1):
        print(f"\n🎬 Película {i}: {movie_data['title']}")
        print(f"   📂 Group: '{movie_data['group']}'")
        
        # Detectar categoría
        detected_category = importer.detect_movie_category(movie_data)
        
        if detected_category:
            category_name = category_manager.get_category_name_by_id(detected_category)
            print(f"   ✅ Categoría detectada: {category_name} (ID: {detected_category})")
        else:
            if importer.default_category_id:
                default_cat_name = category_manager.get_category_name_by_id(importer.default_category_id)
                print(f"   🎯 Usará categoría por defecto: {default_cat_name} (ID: {importer.default_category_id})")
            else:
                print(f"   ❌ No se detectó categoría")
        
        # Mostrar bouquet que se usaría
        if importer.default_bouquet_id:
            bouquet = bouquet_manager.get_bouquet_by_id(importer.default_bouquet_id)
            if bouquet:
                print(f"   🎁 Se asignará al bouquet: {bouquet['name']} (ID: {bouquet['id']})")
        else:
            print(f"   ❌ No hay bouquet por defecto configurado")
    
    # 5. Test de detección avanzada
    print("\n🔍 TEST DE DETECCIÓN AVANZADA:")
    print("-" * 60)
    
    advanced_test_cases = [
        {'title': 'Fast & Furious 9', 'group': 'Action Movies', 'logo': ''},
        {'title': 'Scary Movie 5', 'group': 'Horror Comedy', 'logo': ''},
        {'title': 'Star Wars: The Rise of Skywalker', 'group': 'Sci-Fi', 'logo': ''},
        {'title': 'Película Desconocida', 'group': 'Categoría Inexistente', 'logo': ''},
    ]
    
    for movie_data in advanced_test_cases:
        print(f"\n🎬 {movie_data['title']}")
        print(f"   📂 Group: '{movie_data['group']}'")
        
        detected = importer.detect_movie_category(movie_data)
        
        if detected:
            category_name = category_manager.get_category_name_by_id(detected)
            print(f"   ✅ Detectado: {category_name} (ID: {detected})")
        else:
            print(f"   ❌ No detectado")
    
    print("\n" + "=" * 60)
    print("🎉 TEST COMPLETADO")
    
    # Cleanup
    importer.disconnect()

def test_bouquet_operations():
    """Probar operaciones específicas de bouquets"""
    print("\n🎁 TEST DE OPERACIONES DE BOUQUETS:")
    print("=" * 60)
    
    bouquet_manager = BouquetManager()
    if not bouquet_manager.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    # Obtener resumen de bouquets
    summary = bouquet_manager.get_bouquets_summary()
    
    if summary:
        print(f"{'ID':<5} {'Bouquet':<25} {'Series':<8} {'Películas':<10}")
        print("-" * 55)
        
        total_series = 0
        total_movies = 0
        for bouquet in summary:
            series_count = bouquet.get('series_count', 0)
            movies_count = bouquet.get('movies_count', 0)
            total_series += series_count
            total_movies += movies_count
            print(f"{bouquet['id']:<5} {bouquet['bouquet_name'][:24]:<25} {series_count:<8} {movies_count:<10}")
        
        print("-" * 55)
        print(f"{'TOTAL':<31} {total_series:<8} {total_movies:<10}")
    else:
        print("❌ No se pudo obtener resumen de bouquets")
    
    bouquet_manager.disconnect()

def main():
    """Función principal"""
    print("🎬 SISTEMA DE PRUEBAS COMPLETO PARA PELÍCULAS")
    print("=" * 60)
    
    try:
        # Test 1: Sistema completo
        test_complete_movie_system()
        
        # Test 2: Operaciones de bouquets
        test_bouquet_operations()
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
