#!/usr/bin/env python3
"""
🚀 PRUEBA RÁPIDA DE XUI MANAGER
Verificar que XUI Manager se ejecuta sin errores con los nuevos colores
"""

import sys
import traceback

def test_xui_import():
    """Probar importación de XUI Manager"""
    print("🔧 PROBANDO IMPORTACIÓN DE XUI MANAGER")
    print("=" * 45)
    
    try:
        print("📦 Importando XUI Manager...")
        from xui_manager_final import XUIManagerFinal
        print("✅ XUI Manager importado correctamente")
        
        print("\n🎨 Verificando colores...")
        # Crear instancia temporal para verificar colores
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Ocultar ventana
        
        app = XUIManagerFinal(root)
        
        # Verificar que los colores están definidos
        expected_colors = [
            'bg_primary', 'bg_secondary', 'bg_tertiary', 'bg_card',
            'accent', 'accent_hover', 'nvidia_green', 'nvidia_green_hover',
            'text_primary', 'text_secondary', 'border', 'border_focus'
        ]
        
        for color_name in expected_colors:
            if color_name in app.colors:
                color_value = app.colors[color_name]
                print(f"   ✅ {color_name}: {color_value}")
            else:
                print(f"   ❌ {color_name}: NO DEFINIDO")
                return False
        
        root.destroy()
        print("\n✅ Todos los colores están definidos correctamente")
        print("✅ Esquema NVIDIA + Gemini aplicado")
        return True
        
    except Exception as e:
        print(f"❌ Error importando XUI Manager: {e}")
        print("\n🔍 DETALLES DEL ERROR:")
        traceback.print_exc()
        return False

def main():
    """Función principal"""
    print("🚀 PRUEBA RÁPIDA - XUI MANAGER CON COLORES NVIDIA + GEMINI")
    print("=" * 65)
    
    try:
        if test_xui_import():
            print("\n🎉 ¡ÉXITO! XUI Manager está listo con el nuevo diseño")
            print("\n📋 RESUMEN:")
            print("   🎨 Fondo negro elegante (#0d1117)")
            print("   🟢 Bordes verdes NVIDIA (#76b900)")
            print("   🔵 Detalles celestes Gemini (#00d4ff)")
            print("   ⚡ Sin long threads problemáticos")
            print("   🎯 Interfaz gaming profesional")
            
            print("\n🚀 PARA EJECUTAR XUI MANAGER:")
            print("   python xui_manager_final.py")
            
        else:
            print("\n❌ Hay errores que necesitan ser corregidos")
            
    except KeyboardInterrupt:
        print("\n⚠️ Prueba cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")

if __name__ == "__main__":
    main()
