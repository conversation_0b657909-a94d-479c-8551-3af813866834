#!/usr/bin/env python3
"""
🐛 DEBUG TMDB PANEL
Script para debuggear el panel TMDB sin ejecutar la aplicación completa
"""

import sys
import os

def debug_colors():
    """Debuggear los colores disponibles"""
    print("🎨 DEBUGGING COLORES")
    print("=" * 30)
    
    try:
        # Importar solo la clase sin ejecutar main
        import importlib.util
        spec = importlib.util.spec_from_file_location("xui_manager", "xui_manager_final.py")
        module = importlib.util.module_from_spec(spec)
        
        # No ejecutar el módulo, solo cargarlo
        sys.modules["xui_manager"] = module
        spec.loader.exec_module(module)
        
        # Crear instancia sin inicializar GUI
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Ocultar ventana
        
        app = module.XUIManagerFinal()
        
        print("✅ Instancia creada")
        print("🎨 Colores disponibles:")
        for key, value in app.colors.items():
            print(f"   {key}: {value}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_card_creation():
    """Debuggear la creación de cards"""
    print("\n🃏 DEBUGGING CREACIÓN DE CARDS")
    print("=" * 30)
    
    try:
        import tkinter as tk
        
        # Crear ventana de prueba
        root = tk.Tk()
        root.title("Debug Cards")
        root.geometry("400x300")
        
        # Simular colores
        colors = {
            'bg_primary': '#1a1a1a',
            'bg_secondary': '#2d2d2d',
            'bg_tertiary': '#404040',
            'bg_card': '#3a3a3a',
            'accent': '#00d4ff',
            'text_primary': '#ffffff',
            'text_secondary': '#b0b0b0',
            'success': '#00ff88',
            'warning': '#ffaa00',
            'error': '#ff4444',
            'border': '#4a4a4a'
        }
        
        # Frame de prueba
        frame = tk.Frame(root, bg=colors['bg_primary'])
        frame.pack(fill='both', expand=True)
        
        # Datos de prueba
        test_result = {
            'id': 603,
            'title': 'The Matrix',
            'release_date': '1999-03-30',
            'vote_average': 8.2,
            'overview': 'Test movie description'
        }
        
        # Crear card simple
        card_frame = tk.Frame(frame, bg=colors['bg_card'], relief='solid', bd=1)
        card_frame.pack(padx=10, pady=10, fill='x')
        
        # Título
        title_label = tk.Label(card_frame, text=test_result['title'],
                             font=('Segoe UI', 12, 'bold'),
                             fg=colors['text_primary'], bg=colors['bg_card'])
        title_label.pack(pady=5)
        
        # ID con color correcto
        id_label = tk.Label(card_frame, text=f"🆔 TMDB ID: {test_result['id']}",
                          font=('Segoe UI', 9),
                          fg=colors['text_secondary'], bg=colors['bg_card'])
        id_label.pack(pady=2)
        
        print("✅ Card de prueba creada exitosamente")
        
        # Mostrar por 3 segundos
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creando card: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal de debug"""
    print("🐛 INICIANDO DEBUG TMDB PANEL")
    print("=" * 40)
    
    # Test 1: Colores
    success1 = debug_colors()
    
    # Test 2: Creación de cards
    success2 = debug_card_creation()
    
    print("\n📊 RESUMEN DEBUG:")
    print(f"   🎨 Colores: {'✅' if success1 else '❌'}")
    print(f"   🃏 Cards: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("🎉 DEBUG EXITOSO")
        return 0
    else:
        print("❌ DEBUG FALLÓ")
        return 1

if __name__ == "__main__":
    exit(main())
