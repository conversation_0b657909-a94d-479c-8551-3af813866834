#!/usr/bin/env python3
"""
🔍 DIAGNÓSTICO DE FUNCIONES DE DUPLICADOS
Verifica si las funciones están funcionando correctamente
"""

import sys
import os

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """Probar conexión a la base de datos"""
    print("🔌 PROBANDO CONEXIÓN A BASE DE DATOS...")
    try:
        from db_connection import DatabaseConnection
        db = DatabaseConnection()
        
        if db.connect():
            print("  ✅ Conexión exitosa")
            
            # Probar consulta básica
            query = "SELECT COUNT(*) as total FROM streams WHERE type = 2"
            result = db.execute_query(query)
            
            if result:
                total_movies = result[0]['total']
                print(f"  📊 Total de películas en BD: {total_movies}")
                
                # Probar consulta con filtros específicos
                query_filtered = """
                SELECT COUNT(*) as total FROM streams 
                WHERE type = 2 AND direct_source = 1 AND direct_proxy = 1
                """
                result_filtered = db.execute_query(query_filtered)
                
                if result_filtered:
                    filtered_movies = result_filtered[0]['total']
                    print(f"  🎯 Películas con direct_source=1 y direct_proxy=1: {filtered_movies}")
                else:
                    print("  ⚠️ No se pudo ejecutar consulta filtrada")
            else:
                print("  ⚠️ No se pudo obtener conteo de películas")
            
            db.disconnect()
            return True
        else:
            print("  ❌ Error de conexión")
            return False
            
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_duplicate_functions():
    """Probar las funciones de duplicados directamente"""
    print("\n🧪 PROBANDO FUNCIONES DE DUPLICADOS...")
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        # Crear instancia sin inicializar la UI completa
        app = XUIManagerFinal()
        
        # Datos de prueba simples
        test_movies = [
            {
                'id': 1,
                'stream_display_name': 'Avatar 2009 1080p',
                'stream_source': 'http://test.com/avatar1.mp4',
                'added': '2024-01-01 10:00:00',
                'tmdb_id': 19995,
                'movie_symlink': 0
            },
            {
                'id': 2,
                'stream_display_name': 'Avatar (2009) 720p',
                'stream_source': 'http://test.com/avatar2.mp4',
                'added': '2024-01-02 11:00:00',
                'tmdb_id': 19995,
                'movie_symlink': 1
            }
        ]
        
        print("  📝 Datos de prueba creados")
        
        # Probar find_movie_duplicates
        print("  🔍 Probando find_movie_duplicates...")
        duplicates = app.find_movie_duplicates(test_movies)
        
        if duplicates:
            print(f"    ✅ Función funciona - Encontró {len(duplicates)} grupos")
            for i, group in enumerate(duplicates, 1):
                print(f"      Grupo {i}: {len(group)} películas")
        else:
            print("    ⚠️ No encontró duplicados (puede ser normal con datos de prueba)")
        
        # Probar is_symlink_movie
        print("  🔗 Probando is_symlink_movie...")
        is_symlink_0 = app.is_symlink_movie(0)
        is_symlink_1 = app.is_symlink_movie(1)
        
        print(f"    movie_symlink=0 -> {is_symlink_0} (debería ser False)")
        print(f"    movie_symlink=1 -> {is_symlink_1} (debería ser True)")
        
        if not is_symlink_0 and is_symlink_1:
            print("    ✅ Función is_symlink_movie funciona correctamente")
        else:
            print("    ❌ Función is_symlink_movie tiene problemas")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error probando funciones: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_database_query():
    """Probar consulta real a la base de datos"""
    print("\n🎯 PROBANDO CONSULTA REAL...")
    
    try:
        from db_connection import DatabaseConnection
        db = DatabaseConnection()
        
        if not db.connect():
            print("  ❌ No se pudo conectar a la BD")
            return False
        
        # Consulta real como en la aplicación
        query = """
        SELECT id, stream_display_name, stream_source, added, tmdb_id, 
               direct_source, direct_proxy, movie_symlink
        FROM streams 
        WHERE type = 2 
        AND direct_source = 1 
        AND direct_proxy = 1
        LIMIT 5
        """
        
        movies = db.execute_query(query)
        
        if movies:
            print(f"  ✅ Consulta exitosa - {len(movies)} películas encontradas")
            
            for i, movie in enumerate(movies, 1):
                print(f"    {i}. ID: {movie['id']}")
                print(f"       Título: {movie['stream_display_name'][:50]}...")
                print(f"       Symlink: {movie.get('movie_symlink', 'N/A')}")
                print(f"       Agregada: {movie.get('added', 'N/A')}")
                print()
        else:
            print("  ⚠️ No se encontraron películas con los criterios especificados")
            print("  💡 Esto podría explicar por qué no se muestran resultados")
        
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"  ❌ Error en consulta real: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 DIAGNÓSTICO DE DUPLICADOS DE PELÍCULAS")
    print("=" * 60)
    
    # Test 1: Conexión a BD
    db_ok = test_database_connection()
    
    # Test 2: Funciones de duplicados
    functions_ok = test_duplicate_functions()
    
    # Test 3: Consulta real
    query_ok = test_real_database_query()
    
    print("\n" + "=" * 60)
    print("📋 RESUMEN DEL DIAGNÓSTICO:")
    print(f"🔌 Conexión a BD: {'✅ OK' if db_ok else '❌ FALLO'}")
    print(f"🧪 Funciones: {'✅ OK' if functions_ok else '❌ FALLO'}")
    print(f"🎯 Consulta real: {'✅ OK' if query_ok else '❌ FALLO'}")
    
    if db_ok and functions_ok and query_ok:
        print("\n🎉 TODO FUNCIONA CORRECTAMENTE")
        print("💡 Si no ves resultados, puede ser que no haya películas con direct_source=1 y direct_proxy=1")
    else:
        print("\n⚠️ HAY PROBLEMAS QUE RESOLVER")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
