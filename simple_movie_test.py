#!/usr/bin/env python3
"""
🧪 SIMPLE MOVIE TMDB TEST
Prueba simple del sistema TMDB para películas
"""

def test_import():
    """Probar importación"""
    try:
        from movie_tmdb_assigner import MovieTMDBAssigner
        print("✅ Import MovieTMDBAssigner OK")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_connection():
    """Probar conexión"""
    try:
        from movie_tmdb_assigner import MovieTMDBAssigner
        assigner = MovieTMDBAssigner()
        if assigner.connect():
            print("✅ Database connection OK")
            assigner.disconnect()
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_stats():
    """Probar estadísticas"""
    try:
        from movie_tmdb_assigner import MovieTMDBAssigner
        assigner = MovieTMDBAssigner()
        if assigner.connect():
            stats = assigner.get_tmdb_statistics()
            print(f"✅ Stats OK - Total: {stats['total_movies']}, Con TMDB: {stats['with_tmdb']}")
            assigner.disconnect()
            return True
        else:
            print("❌ Stats failed - no connection")
            return False
    except Exception as e:
        print(f"❌ Stats error: {e}")
        return False

def test_ui_methods():
    """Probar métodos de UI"""
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("xui_manager_final", "xui_manager_final.py")
        module = importlib.util.module_from_spec(spec)
        
        methods = [
            'load_movies_without_tmdb',
            'auto_assign_tmdb_movies', 
            'manual_assign_tmdb_movies'
        ]
        
        # Leer el archivo para verificar que los métodos existen
        with open('xui_manager_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        for method in methods:
            if f"def {method}(" in content:
                print(f"✅ Method {method} found")
            else:
                print(f"❌ Method {method} NOT found")
                return False
        
        return True
    except Exception as e:
        print(f"❌ UI methods error: {e}")
        return False

def main():
    print("🧪 SIMPLE MOVIE TMDB TEST")
    print("=" * 30)
    
    tests = [
        ("Import", test_import),
        ("Connection", test_connection), 
        ("Statistics", test_stats),
        ("UI Methods", test_ui_methods)
    ]
    
    passed = 0
    for name, test_func in tests:
        print(f"\n🔍 Testing {name}...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {name} failed")
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Movie TMDB system is ready!")
    else:
        print("⚠️ Some tests failed")

if __name__ == "__main__":
    main()
