#!/usr/bin/env python3
"""
🎬 TEST DE ASIGNACIÓN AUTOMÁTICA DE CATEGORÍAS PARA PELÍCULAS
Prueba el sistema de detección automática de categorías
"""

import sys
import os
from movie_category_manager import MovieCategoryManager
from robust_movie_importer import RobustMovieImporter

def test_category_detection():
    """Probar detección automática de categorías"""
    print("🎬 TEST DE DETECCIÓN AUTOMÁTICA DE CATEGORÍAS")
    print("=" * 60)
    
    # Crear instancias
    category_manager = MovieCategoryManager()
    importer = RobustMovieImporter()
    
    if not category_manager.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    # 1. Mostrar categorías disponibles
    print("\n📋 CATEGORÍAS DISPONIBLES:")
    categories = category_manager.get_movie_categories()
    
    if not categories:
        print("❌ No se encontraron categorías de películas")
        print("💡 Asegúrate de que existen categorías con category_type='movie' en streams_categories")
        return
    
    for cat in categories:
        print(f"  {cat['id']:3d}. {cat['name']}")
    
    # 2. Casos de prueba para detección automática
    print("\n🧪 CASOS DE PRUEBA:")
    test_cases = [
        {'title': 'Avengers Endgame', 'group': 'Acción', 'logo': ''},
        {'title': 'The Hangover', 'group': 'Comedia', 'logo': ''},
        {'title': 'Titanic', 'group': 'Romance', 'logo': ''},
        {'title': 'The Conjuring', 'group': 'Terror', 'logo': ''},
        {'title': 'Interstellar', 'group': 'Ciencia Ficción', 'logo': ''},
        {'title': 'Toy Story', 'group': 'Animación', 'logo': ''},
        {'title': 'The Godfather', 'group': 'Drama', 'logo': ''},
        {'title': 'Unknown Movie', 'group': 'Categoría Inexistente', 'logo': ''},
        {'title': 'Movie Without Group', 'group': '', 'logo': ''},
    ]
    
    print("-" * 60)
    for i, movie_data in enumerate(test_cases, 1):
        print(f"\n🎬 Test {i}: {movie_data['title']}")
        print(f"   📂 Group: '{movie_data['group']}'")
        
        # Detectar categoría
        detected_category = importer.detect_movie_category(movie_data)
        
        if detected_category:
            category_name = category_manager.get_category_name_by_id(detected_category)
            print(f"   ✅ Categoría detectada: {category_name} (ID: {detected_category})")
        else:
            print(f"   ❌ No se detectó categoría automática")
    
    # 3. Test de categoría por defecto
    print("\n🎯 TEST DE CATEGORÍA POR DEFECTO:")
    print("-" * 60)
    
    if categories:
        # Establecer primera categoría como por defecto
        default_cat = categories[0]
        success = importer.set_default_category(default_cat['id'])
        
        if success:
            print(f"✅ Categoría por defecto establecida: {default_cat['name']}")
            
            # Probar con película sin group-title
            test_movie = {'title': 'Test Movie', 'group': '', 'logo': ''}
            detected = importer.detect_movie_category(test_movie)
            
            if detected == default_cat['id']:
                print(f"✅ Categoría por defecto aplicada correctamente")
            else:
                print(f"❌ Error aplicando categoría por defecto")
        else:
            print("❌ Error estableciendo categoría por defecto")
    
    # 4. Test de palabras clave en títulos
    print("\n🔍 TEST DE DETECCIÓN POR TÍTULO:")
    print("-" * 60)
    
    # Remover categoría por defecto para esta prueba
    importer.set_default_category(None)
    
    title_test_cases = [
        {'title': 'Acción Movie 2024', 'group': '', 'logo': ''},
        {'title': 'Comedy Central Presents', 'group': '', 'logo': ''},
        {'title': 'Drama Queen', 'group': '', 'logo': ''},
    ]
    
    for movie_data in title_test_cases:
        print(f"\n🎬 Título: {movie_data['title']}")
        detected = importer.detect_movie_category(movie_data)
        
        if detected:
            category_name = category_manager.get_category_name_by_id(detected)
            print(f"   ✅ Detectado por título: {category_name} (ID: {detected})")
        else:
            print(f"   ❌ No detectado por título")
    
    print("\n" + "=" * 60)
    print("🎉 TEST COMPLETADO")
    
    # Cleanup
    category_manager.disconnect()

def test_category_summary():
    """Probar resumen de categorías"""
    print("\n📊 RESUMEN DE CATEGORÍAS:")
    print("=" * 60)
    
    category_manager = MovieCategoryManager()
    if not category_manager.connect():
        print("❌ No se pudo conectar a la base de datos")
        return
    
    summary = category_manager.get_categories_summary()
    
    if summary:
        print(f"{'ID':<5} {'Categoría':<25} {'Películas':<10}")
        print("-" * 45)
        
        total_movies = 0
        for cat in summary:
            movie_count = cat.get('movie_count', 0)
            total_movies += movie_count
            print(f"{cat['id']:<5} {cat['category_name']:<25} {movie_count:<10}")
        
        print("-" * 45)
        print(f"{'TOTAL':<31} {total_movies:<10}")
    else:
        print("❌ No se pudo obtener resumen de categorías")
    
    category_manager.disconnect()

def main():
    """Función principal"""
    print("🎬 SISTEMA DE PRUEBAS PARA CATEGORÍAS DE PELÍCULAS")
    print("=" * 60)
    
    try:
        # Test 1: Detección automática
        test_category_detection()
        
        # Test 2: Resumen de categorías
        test_category_summary()
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
