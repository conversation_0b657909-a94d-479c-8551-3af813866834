#!/usr/bin/env python3
"""
🛠️ PRUEBA DE HERRAMIENTAS ADMINISTRATIVAS
Verificar que el panel administrativo se ve con el estilo NVIDIA + Gemini
"""

import tkinter as tk
from tkinter import ttk
import sys

def create_test_admin_panel():
    """Crear ventana de prueba del panel administrativo"""
    
    # Colores NVIDIA + Gemini
    colors = {
        'bg_primary': '#0d1117',        # Negro GitHub
        'bg_secondary': '#161b22',      # Gris muy oscuro
        'bg_tertiary': '#21262d',       # Gris oscuro
        'bg_card': '#1c2128',           # Cards oscuros
        'accent': '#00d4ff',            # <PERSON>
        'accent_hover': '#00b8e6',      # Celeste más oscuro
        'nvidia_green': '#76b900',      # Verde NVIDIA
        'nvidia_green_hover': '#5a8a00', # Verde NVIDIA hover
        'text_primary': '#f0f6fc',      # <PERSON> suave
        'text_secondary': '#8b949e',    # Gris claro
        'success': '#238636',           # Verde éxito
        'warning': '#d29922',           # <PERSON><PERSON>
        'error': '#f85149',             # Rojo
        'border': '#76b900',            # Bordes verdes NVIDIA
        'border_focus': '#00d4ff'       # Bordes activos celestes
    }
    
    # Crear ventana principal
    root = tk.Tk()
    root.title("🛠️ Prueba - Herramientas Administrativas NVIDIA + Gemini")
    root.geometry("1400x900")
    root.configure(bg=colors['bg_primary'])
    
    # Centrar ventana
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1400 // 2)
    y = (root.winfo_screenheight() // 2) - (900 // 2)
    root.geometry(f"1400x900+{x}+{y}")
    
    # Frame principal
    main_frame = tk.Frame(root, bg=colors['bg_primary'])
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Configurar grid
    main_frame.columnconfigure(0, weight=0, minsize=250)  # Botones
    main_frame.columnconfigure(1, weight=2)               # Datos
    main_frame.columnconfigure(2, weight=3)               # TMDB
    main_frame.rowconfigure(1, weight=1)
    
    # Título principal
    title_frame = tk.Frame(main_frame, bg=colors['bg_primary'])
    title_frame.grid(row=0, column=0, columnspan=3, pady=(0, 20), sticky='ew')
    
    title = tk.Label(title_frame, text="🛠️ HERRAMIENTAS ADMINISTRATIVAS",
                    font=('Segoe UI', 20, 'bold'),
                    fg=colors['nvidia_green'], bg=colors['bg_primary'])
    title.pack(side='left')
    
    # Línea decorativa verde
    separator_line = tk.Frame(title_frame, height=3, bg=colors['nvidia_green'])
    separator_line.pack(fill='x', pady=(10, 0))
    
    # === COLUMNA 1: BOTONES ===
    create_buttons_column(main_frame, colors)
    
    # === COLUMNA 2: DATOS ===
    create_data_column(main_frame, colors)
    
    # === COLUMNA 3: TMDB ===
    create_tmdb_column(main_frame, colors)
    
    print("🎨 Ventana de prueba creada")
    print("✅ Verifica que se vea con estilo NVIDIA + Gemini:")
    print("   🖤 Fondo negro elegante")
    print("   🟢 Bordes verdes NVIDIA")
    print("   🔵 Detalles celestes Gemini")
    print("   ⚡ Botones con colores apropiados")
    
    return root

def create_buttons_column(parent, colors):
    """Crear columna de botones"""
    # Frame contenedor
    buttons_container = tk.Frame(parent, bg=colors['bg_card'], 
                               relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    buttons_container.grid(row=1, column=0, sticky='nsew', padx=(0, 15))
    
    # Título
    title_container = tk.Frame(buttons_container, bg=colors['bg_card'])
    title_container.pack(fill='x', pady=(15, 20))
    
    title = tk.Label(title_container, text="🔧 HERRAMIENTAS",
                    font=('Segoe UI', 14, 'bold'),
                    fg=colors['nvidia_green'], bg=colors['bg_card'])
    title.pack()
    
    # Línea decorativa
    title_line = tk.Frame(title_container, height=2, bg=colors['nvidia_green'])
    title_line.pack(fill='x', pady=(5, 0))
    
    # Configuración de botones
    button_config = {
        'font': ('Segoe UI', 9, 'bold'),
        'bd': 2,
        'relief': 'solid',
        'width': 18,
        'pady': 8
    }
    
    # Botones de ejemplo
    buttons = [
        ("🔍 Episodios Huérfanos", colors['accent'], colors['bg_primary']),
        ("👻 Episodios Fantasmas", colors['warning'], colors['bg_primary']),
        ("🔄 Episodios Duplicados", colors['error'], colors['text_primary']),
        ("🧹 Procesar Duplicados", colors['nvidia_green'], colors['bg_primary']),
        ("🏠 Asignar Series", colors['success'], colors['text_primary']),
        ("📊 Cargar TMDB", colors['bg_card'], colors['text_primary']),
        ("🤖 Auto TMDB", colors['accent'], colors['bg_primary']),
        ("🔍 Manual TMDB", colors['warning'], colors['bg_primary']),
    ]
    
    for text, bg_color, fg_color in buttons:
        btn = tk.Button(buttons_container, text=text,
                       bg=bg_color, fg=fg_color,
                       highlightbackground=bg_color,
                       **button_config)
        btn.pack(pady=6, padx=15, fill='x')
    
    # Separador
    separator = tk.Frame(buttons_container, height=3, bg=colors['nvidia_green'])
    separator.pack(fill='x', padx=15, pady=20)
    
    # Botón de regreso
    back_btn = tk.Button(buttons_container, text="🔙 Panel Principal",
                        bg=colors['nvidia_green'], fg=colors['bg_primary'],
                        font=('Segoe UI', 12, 'bold'),
                        relief='solid', bd=3,
                        highlightbackground=colors['nvidia_green'],
                        height=2)
    back_btn.pack(pady=15, padx=15, fill='x')

def create_data_column(parent, colors):
    """Crear columna de datos"""
    data_frame = tk.Frame(parent, bg=colors['bg_card'], 
                        relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    data_frame.grid(row=1, column=1, sticky='nsew', padx=10)
    
    # Título
    title_container = tk.Frame(data_frame, bg=colors['bg_card'])
    title_container.pack(fill='x', pady=(15, 10))
    
    title = tk.Label(title_container, text="📊 DATOS ENCONTRADOS",
                    font=('Segoe UI', 14, 'bold'),
                    fg=colors['nvidia_green'], bg=colors['bg_card'])
    title.pack()
    
    # Línea decorativa verde
    data_line = tk.Frame(title_container, height=2, bg=colors['nvidia_green'])
    data_line.pack(fill='x', pady=(5, 0))
    
    # Área de texto de ejemplo
    text_area = tk.Text(data_frame, bg=colors['bg_tertiary'], fg=colors['text_primary'],
                       font=('Consolas', 10), wrap='word', bd=0,
                       selectbackground=colors['accent'])
    text_area.pack(fill='both', expand=True, padx=15, pady=(10, 15))
    
    # Texto de ejemplo
    example_text = """🔍 EPISODIOS HUÉRFANOS DETECTADOS:

✅ Análisis completado
📊 Total encontrados: 15 episodios

📺 Serie: Breaking Bad
   🔸 S01E01 - Pilot
   🔸 S01E02 - Cat's in the Bag
   🔸 S02E03 - Bit by a Dead Bee

📺 Serie: Game of Thrones  
   🔸 S08E01 - Winterfell
   🔸 S08E02 - A Knight of the Seven Kingdoms

⚡ Estado: Listo para asignación
🎯 Acción recomendada: Usar asignación automática"""
    
    text_area.insert('1.0', example_text)
    text_area.config(state='disabled')

def create_tmdb_column(parent, colors):
    """Crear columna de TMDB"""
    tmdb_frame = tk.Frame(parent, bg=colors['bg_card'], 
                        relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    tmdb_frame.grid(row=1, column=2, sticky='nsew', padx=(15, 0))
    
    # Título
    title_frame = tk.Frame(tmdb_frame, bg=colors['bg_card'])
    title_frame.pack(fill='x', pady=(15, 10))
    
    title = tk.Label(title_frame, text="🎬 RESULTADOS TMDB",
                    font=('Segoe UI', 16, 'bold'),
                    fg=colors['accent'], bg=colors['bg_card'])
    title.pack()
    
    # Línea decorativa celeste
    tmdb_line = tk.Frame(title_frame, height=2, bg=colors['accent'])
    tmdb_line.pack(fill='x', pady=(5, 0))
    
    subtitle = tk.Label(title_frame, text="Selecciona el resultado más apropiado",
                       font=('Segoe UI', 10),
                       fg=colors['text_secondary'], bg=colors['bg_card'])
    subtitle.pack(pady=(8, 0))
    
    # Área de contenido TMDB
    content_area = tk.Text(tmdb_frame, bg=colors['bg_tertiary'], fg=colors['text_primary'],
                          font=('Segoe UI', 10), wrap='word', bd=0,
                          selectbackground=colors['accent'])
    content_area.pack(fill='both', expand=True, padx=15, pady=(10, 15))
    
    # Contenido de ejemplo
    tmdb_content = """🎬 RESULTADOS PARA: "Breaking Bad"

🎯 Coincidencia exacta encontrada:

📺 Breaking Bad (2008-2013)
⭐ Rating: 9.5/10
🆔 TMDB ID: 1396
📅 Años: 2008-2013
🎭 Género: Crime, Drama, Thriller

📝 Descripción:
Un profesor de química de secundaria diagnosticado con cáncer de pulmón inoperable recurre a la fabricación y venta de metanfetamina para asegurar el futuro financiero de su familia.

🎯 Temporadas: 5
📺 Episodios: 62

✅ RECOMENDADO: Asignar automáticamente
🔘 Confirmar asignación"""
    
    content_area.insert('1.0', tmdb_content)
    content_area.config(state='disabled')

def main():
    """Función principal"""
    print("🛠️ INICIANDO PRUEBA DE HERRAMIENTAS ADMINISTRATIVAS")
    print("=" * 55)
    
    try:
        root = create_test_admin_panel()
        
        print("\n🎨 VERIFICAR ELEMENTOS VISUALES:")
        print("   🖤 Fondo negro elegante (#0d1117)")
        print("   🟢 Bordes verdes NVIDIA (#76b900)")
        print("   🔵 Títulos celestes Gemini (#00d4ff)")
        print("   ⚡ Botones con colores apropiados")
        print("   📱 Layout de 3 columnas responsivo")
        
        print("\n🚀 Ventana abierta - Cierra cuando termines de verificar")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
