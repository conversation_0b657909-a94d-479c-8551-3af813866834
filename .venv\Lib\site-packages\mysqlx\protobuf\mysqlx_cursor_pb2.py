# -*- coding: utf-8 -*-
# Copyright (c) 2017, 2023, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0, as
# published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an
# additional permission to link the program and your derivative works
# with the separately licensed software that they have included with
# MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of MySQL Connector/Python, is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software Foundation, Inc.,
# 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mysqlx_cursor.proto

# type: ignore

"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mysqlx.protobuf import mysqlx_prepare_pb2 as mysqlx__prepare__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13mysqlx_cursor.proto\x12\rMysqlx.Cursor\x1a\x14mysqlx_prepare.proto\"\xf2\x01\n\x04Open\x12\x11\n\tcursor_id\x18\x01 \x02(\r\x12.\n\x04stmt\x18\x04 \x02(\x0b\x32 .Mysqlx.Cursor.Open.OneOfMessage\x12\x12\n\nfetch_rows\x18\x05 \x01(\x04\x1a\x92\x01\n\x0cOneOfMessage\x12\x33\n\x04type\x18\x01 \x02(\x0e\x32%.Mysqlx.Cursor.Open.OneOfMessage.Type\x12\x30\n\x0fprepare_execute\x18\x02 \x01(\x0b\x32\x17.Mysqlx.Prepare.Execute\"\x1b\n\x04Type\x12\x13\n\x0fPREPARE_EXECUTE\x10\x00\".\n\x05\x46\x65tch\x12\x11\n\tcursor_id\x18\x01 \x02(\r\x12\x12\n\nfetch_rows\x18\x05 \x01(\x04\"\x1a\n\x05\x43lose\x12\x11\n\tcursor_id\x18\x01 \x02(\rB\x1b\n\x17\x63om.mysql.cj.x.protobufH\x03')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mysqlx_cursor_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\027com.mysql.cj.x.protobufH\003'
  _OPEN._serialized_start=61
  _OPEN._serialized_end=303
  _OPEN_ONEOFMESSAGE._serialized_start=157
  _OPEN_ONEOFMESSAGE._serialized_end=303
  _OPEN_ONEOFMESSAGE_TYPE._serialized_start=276
  _OPEN_ONEOFMESSAGE_TYPE._serialized_end=303
  _FETCH._serialized_start=305
  _FETCH._serialized_end=351
  _CLOSE._serialized_start=353
  _CLOSE._serialized_end=379
# @@protoc_insertion_point(module_scope)
