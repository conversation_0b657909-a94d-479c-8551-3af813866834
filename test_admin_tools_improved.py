#!/usr/bin/env python3
"""
🛠️ PRUEBA DE HERRAMIENTAS ADMINISTRATIVAS MEJORADAS
Verificar que los recuadros se vean más arriba y con mejor estilo NVIDIA + Gemini
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import sys

def create_test_admin_tools():
    """Crear ventana de prueba de herramientas administrativas mejoradas"""
    
    # Colores NVIDIA + Gemini
    colors = {
        'bg_primary': '#0d1117',        # Negro GitHub
        'bg_secondary': '#161b22',      # Gris muy oscuro
        'bg_tertiary': '#21262d',       # Gris oscuro
        'bg_card': '#1c2128',           # <PERSON>s oscuros
        'accent': '#00d4ff',            # <PERSON> Gemini
        'accent_hover': '#00b8e6',      # Celeste más oscuro
        'nvidia_green': '#76b900',      # Verde NVIDIA
        'nvidia_green_hover': '#5a8a00', # Verde NVIDIA hover
        'text_primary': '#f0f6fc',      # <PERSON> suave
        'text_secondary': '#8b949e',    # Gris claro
        'success': '#238636',           # Verde éxito
        'warning': '#d29922',           # Amarillo
        'error': '#f85149',             # Rojo
        'border': '#76b900',            # Bordes verdes NVIDIA
        'border_focus': '#00d4ff'       # Bordes activos celestes
    }
    
    # Crear ventana principal
    root = tk.Tk()
    root.title("🛠️ Prueba - Herramientas Administrativas Mejoradas")
    root.geometry("1400x800")
    root.configure(bg=colors['bg_primary'])
    
    # Centrar ventana
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (1400 // 2)
    y = (root.winfo_screenheight() // 2) - (800 // 2)
    root.geometry(f"1400x800+{x}+{y}")
    
    # Frame principal
    main_frame = tk.Frame(root, bg=colors['bg_primary'])
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Configurar grid para 3 columnas
    main_frame.columnconfigure(0, weight=0, minsize=200)  # Herramientas
    main_frame.columnconfigure(1, weight=1)               # Datos
    main_frame.columnconfigure(2, weight=1)               # TMDB
    main_frame.rowconfigure(1, weight=1)
    
    # === HEADER ===
    create_admin_header(main_frame, colors)
    
    # === COLUMNA IZQUIERDA - HERRAMIENTAS ===
    create_tools_column(main_frame, colors)
    
    # === COLUMNA CENTRAL - DATOS ===
    create_data_column_improved(main_frame, colors)
    
    # === COLUMNA DERECHA - TMDB ===
    create_tmdb_column_improved(main_frame, colors)
    
    print("🛠️ Herramientas administrativas mejoradas creadas")
    print("✅ Verifica las mejoras:")
    print("   📏 Recuadros más arriba con menos padding")
    print("   🎨 Headers con fondo de color (verde/celeste)")
    print("   🖼️ Bordes más prominentes (3px)")
    print("   ⚡ Mejor contraste y legibilidad")
    print("   🎯 Diseño más profesional y gaming")
    
    return root

def create_admin_header(parent, colors):
    """Crear header de herramientas administrativas"""
    header_frame = tk.Frame(parent, bg=colors['bg_secondary'],
                           relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    header_frame.grid(row=0, column=0, columnspan=3, sticky='ew', padx=5, pady=5)

    # Contenedor del título
    title_container = tk.Frame(header_frame, bg=colors['bg_secondary'])
    title_container.pack(fill='x', pady=15)

    # Título principal
    title_label = tk.Label(title_container, text="🛠️ HERRAMIENTAS ADMINISTRATIVAS",
                          font=('Segoe UI', 20, 'bold'),
                          fg=colors['nvidia_green'], bg=colors['bg_secondary'])
    title_label.pack()
    
    # Línea decorativa verde
    title_line = tk.Frame(title_container, height=3, bg=colors['nvidia_green'])
    title_line.pack(fill='x', pady=(8, 0))

def create_tools_column(parent, colors):
    """Crear columna de herramientas"""
    tools_frame = tk.Frame(parent, bg=colors['bg_card'],
                          relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    tools_frame.grid(row=1, column=0, sticky='nsew', padx=(5, 8), pady=(5, 0))

    # Título de herramientas
    tools_title = tk.Label(tools_frame, text="🔧 HERRAMIENTAS",
                          font=('Segoe UI', 14, 'bold'),
                          fg=colors['nvidia_green'], bg=colors['bg_card'])
    tools_title.pack(pady=(15, 10))

    # Botones de herramientas
    buttons = [
        ("📊 Encontrar No/Gratis", colors['accent']),
        ("🔍 Encontrar Funciones", colors['warning']),
        ("🗑️ Encontrar Duplicados", colors['error']),
        ("🔄 Procesar Duplicados", colors['success']),
        ("📺 Asignar Series", colors['bg_tertiary']),
        ("🎬 Cargar TMDB", colors['accent']),
        ("🎭 Más TMDB", colors['warning']),
        ("📋 Manual TMDB", colors['nvidia_green'])
    ]

    for text, color in buttons:
        btn = tk.Button(tools_frame, text=text,
                       bg=color, fg=colors['text_primary'],
                       font=('Segoe UI', 10, 'bold'), bd=2, relief='solid',
                       activebackground=color)
        btn.pack(fill='x', padx=15, pady=2)

def create_data_column_improved(parent, colors):
    """Crear columna de datos mejorada"""
    # Frame de datos con bordes verdes más prominentes
    data_frame = tk.Frame(parent, bg=colors['bg_card'],
                        relief='solid', bd=3, highlightbackground=colors['nvidia_green'],
                        highlightthickness=2)
    data_frame.grid(row=1, column=1, sticky='nsew', padx=8, pady=(5, 0))
    data_frame.columnconfigure(0, weight=1)
    data_frame.rowconfigure(1, weight=1)

    # Header mejorado con gradiente visual
    header_frame = tk.Frame(data_frame, bg=colors['nvidia_green'], height=50)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)

    # Contenedor del título centrado
    title_container = tk.Frame(header_frame, bg=colors['nvidia_green'])
    title_container.pack(expand=True, fill='both')

    data_title = tk.Label(title_container, text="📊 DATOS ENCONTRADOS",
                         font=('Segoe UI', 16, 'bold'),
                         fg=colors['bg_primary'], bg=colors['nvidia_green'])
    data_title.pack(expand=True)

    # Subtítulo informativo
    subtitle_frame = tk.Frame(data_frame, bg=colors['bg_card'])
    subtitle_frame.pack(fill='x', pady=(8, 0))
    
    subtitle = tk.Label(subtitle_frame, text="Selecciona una herramienta para ver los datos encontrados",
                       font=('Segoe UI', 10),
                       fg=colors['text_secondary'], bg=colors['bg_card'])
    subtitle.pack()

    # Frame para el contenido de datos con mejor espaciado
    data_content = tk.Frame(data_frame, bg=colors['bg_card'])
    data_content.pack(fill='both', expand=True, padx=12, pady=(0, 12))
    data_content.columnconfigure(0, weight=1)
    data_content.rowconfigure(0, weight=1)

    # Área de texto mejorada con bordes
    text_container = tk.Frame(data_content, bg=colors['bg_secondary'],
                             relief='solid', bd=2, highlightbackground=colors['nvidia_green'])
    text_container.grid(row=0, column=0, sticky='nsew')
    text_container.columnconfigure(0, weight=1)
    text_container.rowconfigure(0, weight=1)

    data_text = scrolledtext.ScrolledText(text_container,
                                         bg=colors['bg_secondary'],
                                         fg=colors['text_primary'],
                                         font=('Consolas', 10),
                                         wrap=tk.WORD,
                                         state='normal',
                                         relief='flat', bd=0,
                                         insertbackground=colors['nvidia_green'],
                                         selectbackground=colors['nvidia_green'])
    data_text.grid(row=0, column=0, sticky='nsew', padx=2, pady=2)

    # Contenido de ejemplo
    sample_data = """🔍 ANÁLISIS COMPLETADO

📊 Resultados encontrados:
   • Series sin TMDB: 15
   • Episodios duplicados: 8
   • Funciones activas: 23
   • Datos corruptos: 2

⚠️ Elementos que requieren atención:
   • Breaking Bad - Temporada 5
   • Game of Thrones - Episodio duplicado
   • The Office - TMDB faltante

✅ Estado del sistema:
   • Base de datos: Conectada
   • TMDB API: Activa
   • Servidor 82: Online"""

    data_text.insert('1.0', sample_data)
    data_text.config(state='disabled')

def create_tmdb_column_improved(parent, colors):
    """Crear columna TMDB mejorada"""
    # Frame de TMDB con bordes celestes más prominentes
    tmdb_frame = tk.Frame(parent, bg=colors['bg_card'],
                        relief='solid', bd=3, highlightbackground=colors['accent'],
                        highlightthickness=2)
    tmdb_frame.grid(row=1, column=2, sticky='nsew', padx=(8, 0), pady=(5, 0))
    tmdb_frame.columnconfigure(0, weight=1)
    tmdb_frame.rowconfigure(1, weight=1)

    # Header mejorado con fondo celeste
    header_frame = tk.Frame(tmdb_frame, bg=colors['accent'], height=50)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)

    # Contenedor del título centrado
    title_container = tk.Frame(header_frame, bg=colors['accent'])
    title_container.pack(expand=True, fill='both')

    tmdb_title = tk.Label(title_container, text="🎬 RESULTADOS TMDB",
                         font=('Segoe UI', 16, 'bold'),
                         fg=colors['bg_primary'], bg=colors['accent'])
    tmdb_title.pack(expand=True)

    # Subtítulo informativo
    subtitle_frame = tk.Frame(tmdb_frame, bg=colors['bg_card'])
    subtitle_frame.pack(fill='x', pady=(8, 0))
    
    subtitle = tk.Label(subtitle_frame, text="Selecciona el resultado más apropiado",
                       font=('Segoe UI', 10),
                       fg=colors['text_secondary'], bg=colors['bg_card'])
    subtitle.pack()

    # Frame para el contenido de TMDB con mejor espaciado
    tmdb_content = tk.Frame(tmdb_frame, bg=colors['bg_card'])
    tmdb_content.pack(fill='both', expand=True, padx=12, pady=(0, 12))
    tmdb_content.columnconfigure(0, weight=1)
    tmdb_content.rowconfigure(0, weight=1)

    # Área de texto mejorada con bordes celestes
    text_container = tk.Frame(tmdb_content, bg=colors['bg_secondary'],
                             relief='solid', bd=2, highlightbackground=colors['accent'])
    text_container.grid(row=0, column=0, sticky='nsew')
    text_container.columnconfigure(0, weight=1)
    text_container.rowconfigure(0, weight=1)

    tmdb_text = scrolledtext.ScrolledText(text_container,
                                         bg=colors['bg_secondary'],
                                         fg=colors['text_primary'],
                                         font=('Consolas', 10),
                                         wrap=tk.WORD,
                                         state='normal',
                                         relief='flat', bd=0,
                                         insertbackground=colors['accent'],
                                         selectbackground=colors['accent'])
    tmdb_text.grid(row=0, column=0, sticky='nsew', padx=2, pady=2)

    # Contenido de ejemplo
    sample_tmdb = """🎬 BÚSQUEDA TMDB ACTIVA

🔍 Resultados para "Breaking Bad":

1. Breaking Bad (2008-2013)
   ⭐ Rating: 9.5/10
   📺 5 temporadas, 62 episodios
   🎭 Drama, Crime, Thriller
   
2. Breaking Bad: El Camino (2019)
   ⭐ Rating: 7.3/10
   🎬 Película
   🎭 Drama, Crime
   
3. Better Call Saul (2015-2022)
   ⭐ Rating: 8.8/10
   📺 6 temporadas, 63 episodios
   🎭 Drama, Crime

✅ Selecciona el resultado correcto
🔄 Búsqueda automática en progreso..."""

    tmdb_text.insert('1.0', sample_tmdb)
    tmdb_text.config(state='disabled')

def main():
    """Función principal"""
    print("🛠️ INICIANDO PRUEBA DE HERRAMIENTAS ADMINISTRATIVAS MEJORADAS")
    print("=" * 60)
    
    try:
        # Crear ventana de herramientas administrativas
        admin_root = create_test_admin_tools()
        
        print("\n🚀 Ventana abierta - Verifica las mejoras visuales")
        print("📏 Los recuadros deben estar más arriba")
        print("🎨 Headers con fondos de color llamativos")
        print("🖼️ Bordes más gruesos y prominentes")
        
        # Ejecutar ventana
        admin_root.mainloop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
