#!/usr/bin/env python3
"""
🧪 TEST MOVIE TMDB PANEL
Prueba del nuevo panel de búsqueda manual TMDB para películas con carátulas
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_movie_tmdb_panel():
    """Probar el nuevo panel de búsqueda TMDB para películas"""
    print("🧪 PRUEBA PANEL TMDB PELÍCULAS")
    print("=" * 40)
    
    try:
        # Importar el manager principal
        from xui_manager_final import XUIManagerFinal
        
        # Crear instancia del manager
        app = XUIManagerFinal()
        
        # Datos de prueba para película
        test_movie = {
            'id': 1,
            'clean_title': 'The Matrix',
            'year': 1999
        }
        
        # Datos de prueba para resultados TMDB
        test_tmdb_results = [
            {
                'id': 603,
                'title': 'The Matrix',
                'release_date': '1999-03-30',
                'vote_average': 8.2,
                'overview': 'Set in the 22nd century, The Matrix tells the story of a computer hacker who joins a group of underground insurgents fighting the vast and powerful computers who now rule the earth.',
                'poster_path': '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg'
            },
            {
                'id': 604,
                'title': 'The Matrix Reloaded',
                'release_date': '2003-05-07',
                'vote_average': 7.1,
                'overview': 'Six months after the events depicted in The Matrix, Neo has proved to be a good omen for the free humans, as more and more humans are being freed from the matrix and brought to Zion.',
                'poster_path': '/9TGHDvWrqKBzwDxDodHYXEmOE6J.jpg'
            },
            {
                'id': 605,
                'title': 'The Matrix Revolutions',
                'release_date': '2003-10-27',
                'vote_average': 6.7,
                'overview': 'The human city of Zion defends itself against the massive invasion of the machines as Neo fights to end the war at another front while also opposing the rogue Agent Smith.',
                'poster_path': '/sKogjhfs5q3aEG8LsGFbw7AvnMn.jpg'
            },
            {
                'id': 624860,
                'title': 'The Matrix Resurrections',
                'release_date': '2021-12-16',
                'vote_average': 6.7,
                'overview': 'Plagued by strange memories, Neo\'s life takes an unexpected turn when he finds himself back inside the Matrix.',
                'poster_path': '/8c4a8kE7PizaGQQnditMmI1xbRp.jpg'
            }
        ]
        
        print(f"✅ Manager creado exitosamente")
        print(f"🎬 Película de prueba: {test_movie['clean_title']} ({test_movie['year']})")
        print(f"📊 Resultados TMDB: {len(test_tmdb_results)}")
        
        # Mostrar el diálogo de asignación
        print(f"🚀 Abriendo panel de asignación TMDB...")
        app.open_movie_assignment_dialog(test_movie, test_tmdb_results)
        
        # Iniciar la aplicación
        app.root.mainloop()
        
        print(f"🎉 PRUEBA COMPLETADA")
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        print(f"💡 Asegúrate de que todos los módulos estén disponibles")
        return False
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        return False
    
    return True

def test_spacing_fix():
    """Probar la corrección del espaciado izquierdo"""
    print("\n🧪 PRUEBA CORRECCIÓN ESPACIADO")
    print("=" * 40)
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        app = XUIManagerFinal()
        
        # Verificar que el panel administrativo tenga el espaciado correcto
        if hasattr(app, 'admin_panel'):
            # Obtener configuración de columnas
            column_config = app.admin_panel.grid_columnconfigure(0)
            print(f"✅ Panel administrativo encontrado")
            print(f"📏 Configuración de columna 0: {column_config}")
        else:
            print(f"⚠️ Panel administrativo no encontrado aún")
        
        print(f"🎉 PRUEBA ESPACIADO COMPLETADA")
        return True
        
    except Exception as e:
        print(f"❌ Error durante prueba de espaciado: {e}")
        return False

def main():
    """Función principal"""
    print("🎬 INICIANDO PRUEBAS DEL SISTEMA TMDB PELÍCULAS")
    print("=" * 50)
    
    # Verificar dependencias
    print("🔍 Verificando dependencias...")
    
    try:
        import requests
        print("✅ requests disponible")
    except ImportError:
        print("❌ requests no disponible")
    
    try:
        from PIL import Image, ImageTk
        print("✅ PIL disponible")
    except ImportError:
        print("⚠️ PIL no disponible - se usará modo fallback")
    
    # Ejecutar pruebas
    print("\n" + "=" * 50)
    
    # Prueba 1: Corrección de espaciado
    success1 = test_spacing_fix()
    
    # Prueba 2: Panel TMDB (comentada para evitar abrir GUI automáticamente)
    print("\n💡 Para probar el panel TMDB, ejecuta:")
    print("   python test_movie_tmdb_panel.py --gui")
    
    if len(sys.argv) > 1 and sys.argv[1] == '--gui':
        success2 = test_movie_tmdb_panel()
    else:
        success2 = True
        print("✅ Prueba GUI omitida (usar --gui para ejecutar)")
    
    # Resumen
    print("\n" + "=" * 50)
    print("📊 RESUMEN DE PRUEBAS:")
    print(f"   🔧 Corrección espaciado: {'✅' if success1 else '❌'}")
    print(f"   🎬 Panel TMDB: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("🎉 TODAS LAS PRUEBAS EXITOSAS")
        return 0
    else:
        print("❌ ALGUNAS PRUEBAS FALLARON")
        return 1

if __name__ == "__main__":
    exit(main())
