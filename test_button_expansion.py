#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba específica para verificar que los botones se expanden correctamente
"""

import tkinter as tk

def test_button_expansion():
    """Probar expansión de botones en la columna"""
    print("🧪 PRUEBA EXPANSIÓN DE BOTONES")
    print("=" * 40)
    
    try:
        from xui_manager_final import XUIManagerFinal
        
        app = XUIManagerFinal()
        app.open_admin_tools()
        
        print("✅ Panel admin abierto")
        print("\n🎯 VERIFICA QUE:")
        print("   • Los botones ocupen TODO el ancho de la columna")
        print("   • NO haya espacio vacío entre botones y scroll")
        print("   • Los botones lleguen hasta la barra de scroll")
        print("   • La columna sea compacta pero los botones anchos")
        
        # Mostrar la ventana
        app.root.mainloop()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_button_expansion()
