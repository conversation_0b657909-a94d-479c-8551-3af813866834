#!/usr/bin/env python3
"""
🎬 TEST DE INTEGRACIÓN COMPLETO
Prueba todo el sistema de películas: categorías automáticas + bouquets automáticos + UI
"""

import sys
import os
import tempfile
from movie_category_manager import MovieCategoryManager
from bouquet_manager import <PERSON><PERSON><PERSON>Mana<PERSON>
from robust_movie_importer import RobustMovieImporter

def create_test_m3u():
    """Crear archivo M3U de prueba"""
    m3u_content = """#EXTM3U
#EXTINF:-1 tvg-id="" tvg-name="Avengers Endgame" tvg-logo="https://example.com/avengers.jpg" group-title="Acción",Avengers: Endgame
http://test.com/avengers.mp4
#EXTINF:-1 tvg-id="" tvg-name="The Hangover" tvg-logo="https://example.com/hangover.jpg" group-title="Comedia",The Hangover
http://test.com/hangover.mp4
#EXTINF:-1 tvg-id="" tvg-name="Titanic" tvg-logo="https://example.com/titanic.jpg" group-title="Romance",Titanic
http://test.com/titanic.mp4
#EXTINF:-1 tvg-id="" tvg-name="The Conjuring" tvg-logo="https://example.com/conjuring.jpg" group-title="Terror",The Conjuring
http://test.com/conjuring.mp4
#EXTINF:-1 tvg-id="" tvg-name="Interstellar" tvg-logo="https://example.com/interstellar.jpg" group-title="Ciencia Ficción",Interstellar
http://test.com/interstellar.mp4
"""
    
    # Crear archivo temporal
    with tempfile.NamedTemporaryFile(mode='w', suffix='.m3u', delete=False, encoding='utf-8') as f:
        f.write(m3u_content)
        return f.name

def test_complete_integration():
    """Test de integración completo"""
    print("🎬 TEST DE INTEGRACIÓN COMPLETO")
    print("=" * 60)
    
    # 1. Verificar componentes básicos
    print("\n🔧 VERIFICANDO COMPONENTES...")
    
    # Test MovieCategoryManager
    cat_manager = MovieCategoryManager()
    if not cat_manager.connect():
        print("❌ Error conectando MovieCategoryManager")
        return False
    
    categories = cat_manager.get_movie_categories()
    if not categories:
        print("❌ No hay categorías de películas disponibles")
        cat_manager.disconnect()
        return False
    
    print(f"✅ MovieCategoryManager: {len(categories)} categorías encontradas")
    cat_manager.disconnect()
    
    # Test BouquetManager
    bouquet_manager = BouquetManager()
    if not bouquet_manager.connect():
        print("❌ Error conectando BouquetManager")
        return False
    
    bouquets = bouquet_manager.get_movie_bouquets()
    if not bouquets:
        print("❌ No hay bouquets disponibles")
        bouquet_manager.disconnect()
        return False
    
    print(f"✅ BouquetManager: {len(bouquets)} bouquets encontrados")
    bouquet_manager.disconnect()
    
    # 2. Test RobustMovieImporter
    print("\n🎬 PROBANDO ROBUST MOVIE IMPORTER...")
    
    importer = RobustMovieImporter()
    if not importer.connect():
        print("❌ Error conectando RobustMovieImporter")
        return False
    
    # Configurar categoría y bouquet por defecto
    if categories:
        default_cat = categories[0]
        success = importer.set_default_category(default_cat['id'])
        if success:
            print(f"✅ Categoría por defecto configurada: {default_cat['name']} (ID: {default_cat['id']})")
        else:
            print("❌ Error configurando categoría por defecto")
    
    if bouquets:
        default_bouquet = bouquets[0]
        success = importer.set_default_bouquet(default_bouquet['id'])
        if success:
            print(f"✅ Bouquet por defecto configurado: {default_bouquet['name']} (ID: {default_bouquet['id']})")
        else:
            print("❌ Error configurando bouquet por defecto")
    
    # 3. Test de detección automática
    print("\n🔍 PROBANDO DETECCIÓN AUTOMÁTICA...")
    
    test_movies = [
        {'title': 'Avengers: Endgame', 'group': 'Acción', 'logo': 'https://example.com/avengers.jpg'},
        {'title': 'The Hangover', 'group': 'Comedia', 'logo': 'https://example.com/hangover.jpg'},
        {'title': 'Titanic', 'group': 'Romance', 'logo': 'https://example.com/titanic.jpg'},
        {'title': 'The Conjuring', 'group': 'Terror', 'logo': 'https://example.com/conjuring.jpg'},
        {'title': 'Interstellar', 'group': 'Ciencia Ficción', 'logo': 'https://example.com/interstellar.jpg'},
    ]
    
    detection_results = []
    for movie_data in test_movies:
        detected_category = importer.detect_movie_category(movie_data)
        
        if detected_category:
            category_name = importer.category_manager.get_category_name_by_id(detected_category)
            print(f"✅ {movie_data['title']} -> {category_name} (ID: {detected_category})")
            detection_results.append(True)
        else:
            print(f"⚠️ {movie_data['title']} -> Categoría por defecto")
            detection_results.append(False)
    
    detection_rate = (sum(detection_results) / len(detection_results)) * 100
    print(f"\n📊 Tasa de detección automática: {detection_rate:.1f}%")
    
    # 4. Test de importación simulada
    print("\n🧪 SIMULANDO IMPORTACIÓN...")
    
    # Crear archivo M3U de prueba
    test_file = create_test_m3u()
    print(f"📁 Archivo de prueba creado: {test_file}")
    
    try:
        # Simular importación (solo parsing, sin insertar en BD)
        from m3u_parser import M3UParser
        
        parser = M3UParser()
        entries = parser.parse_file(test_file)
        
        if entries:
            print(f"✅ Archivo M3U parseado: {len(entries)} entradas encontradas")
            
            # Simular detección para cada entrada
            for i, entry in enumerate(entries[:3], 1):  # Solo primeras 3 para no saturar
                movie_data = {
                    'title': entry.get('tvg-name', entry.get('title', 'Sin título')),
                    'group': entry.get('group-title', ''),
                    'logo': entry.get('tvg-logo', '')
                }
                
                detected = importer.detect_movie_category(movie_data)
                category_name = "Por defecto"
                if detected:
                    category_name = importer.category_manager.get_category_name_by_id(detected)
                
                print(f"  {i}. {movie_data['title']} -> {category_name}")
        else:
            print("❌ No se pudieron parsear entradas del archivo M3U")
    
    except Exception as e:
        print(f"❌ Error durante simulación: {e}")
    
    finally:
        # Limpiar archivo temporal
        try:
            os.unlink(test_file)
        except:
            pass
    
    # 5. Verificar estado final
    print("\n📋 RESUMEN FINAL:")
    print("-" * 60)
    
    # Verificar configuración del importer
    if hasattr(importer, 'default_category_id') and importer.default_category_id:
        print(f"✅ Categoría por defecto: ID {importer.default_category_id}")
    else:
        print("⚠️ Sin categoría por defecto configurada")
    
    if hasattr(importer, 'default_bouquet_id') and importer.default_bouquet_id:
        print(f"✅ Bouquet por defecto: ID {importer.default_bouquet_id}")
    else:
        print("⚠️ Sin bouquet por defecto configurado")
    
    # Verificar managers
    print(f"✅ MovieCategoryManager: Operativo")
    print(f"✅ BouquetManager: Operativo")
    print(f"✅ RobustMovieImporter: Operativo")
    
    importer.disconnect()
    
    print("\n🎉 TEST DE INTEGRACIÓN COMPLETADO")
    return True

def test_ui_integration():
    """Test básico de integración con UI"""
    print("\n🖥️ TEST DE INTEGRACIÓN UI:")
    print("-" * 60)
    
    try:
        # Verificar que se pueden importar los módulos UI
        print("📦 Verificando módulos UI...")
        
        # Test import xui_manager_final
        try:
            import xui_manager_final
            print("✅ xui_manager_final importado correctamente")
        except Exception as e:
            print(f"❌ Error importando xui_manager_final: {e}")
            return False
        
        # Verificar que la clase tiene los nuevos métodos
        ui_class = xui_manager_final.XUIManagerFinal
        
        required_methods = [
            'open_movie_category_assignment',
            'open_movie_bouquet_assignment',
            'create_movie_category_window',
            'create_movie_bouquet_window',
            'apply_movie_category_assignment',
            'apply_movie_bouquet_assignment'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(ui_class, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ Métodos faltantes en UI: {', '.join(missing_methods)}")
            return False
        else:
            print("✅ Todos los métodos UI están presentes")
        
        print("✅ Integración UI verificada correctamente")
        return True
        
    except Exception as e:
        print(f"❌ Error durante test UI: {e}")
        return False

def main():
    """Función principal"""
    print("🎬 SISTEMA DE PRUEBAS DE INTEGRACIÓN COMPLETO")
    print("=" * 60)
    
    try:
        # Test 1: Integración completa del sistema
        success1 = test_complete_integration()
        
        # Test 2: Integración UI
        success2 = test_ui_integration()
        
        # Resultado final
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 TODOS LOS TESTS PASARON EXITOSAMENTE")
            print("✅ El sistema está listo para usar")
        else:
            print("❌ ALGUNOS TESTS FALLARON")
            print("⚠️ Revisar errores antes de usar el sistema")
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
